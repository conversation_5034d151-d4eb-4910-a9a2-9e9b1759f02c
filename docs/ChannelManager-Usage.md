# ChannelManager 使用指南

## 概述

`ChannelManager` 是一个优化后的渠道管理系统，旨在简化渠道相关的调用流程并提供高效的缓存机制。

## 主要优化点

### 1. 统一的渠道管理
- 将原本分散在 `AuthManager` 中的渠道相关方法统一到 `ChannelManager`
- 提供清晰的接口分离：检测、策略获取、播放能力检查

### 2. 高效的缓存机制
- **渠道检测缓存**：渠道类型检测结果会被缓存，避免重复的用户代理检测
- **策略配置缓存**：渠道策略配置会被缓存 5 分钟，避免重复的 API 调用
- **智能缓存管理**：支持缓存清除和状态查询

### 3. 简化的调用流程
- 原本需要调用 `shouldGetChannelStrategy()` → `getChannelStrategy()` → `checkChannelPlayable()`
- 现在只需要调用 `getChannelStrategy()` → `checkEpisodePlayable()`

## 使用示例

### 基本使用

```typescript
import { ChannelManager } from '@/services/ChannelManager';

const channelManager = ChannelManager.getInstance();

// 获取当前渠道（带缓存）
const currentChannel = channelManager.getCurrentChannel();
console.log('Current channel:', currentChannel);

// 获取渠道策略（带缓存）
const strategy = await channelManager.getChannelStrategy();
if (strategy) {
  console.log('Channel strategy:', strategy);
}

// 检查剧集播放能力
const isPlayable = channelManager.checkEpisodePlayable(episodeNo, strategy);
console.log('Episode playable:', isPlayable);
```

### 在 AuthManager 中的使用

```typescript
// 优化前的调用流程
async getVideoDetail(videoId: string) {
  let strategy: undefined | IChannelStrategyConfig;
  if (this.shouldGetChannelStrategy()) {  // 第一次调用
    strategy = await this.getChannelStrategy();  // 第二次调用
  }
  
  // 在处理每个 episode 时
  const isChannelBlocked = !this.checkChannelPlayable(
    MOCK_CHANNEL_CONFIG,  // 硬编码配置
    no,
  );
}

// 优化后的调用流程
async getVideoDetail(videoId: string) {
  // 一次调用获取策略（带缓存）
  const strategy = await this.channelManager.getChannelStrategy();
  
  // 在处理每个 episode 时
  const isChannelPlayable = this.channelManager.checkEpisodePlayable(
    no,
    strategy,  // 使用实际获取的策略
  );
}
```

## 架构设计

### 接口分离

```typescript
interface IChannelDetector {
  detect(): Channels;
}

interface IChannelStrategyProvider {
  getStrategy(channel: Channels): Promise<IChannelStrategyConfig>;
}

interface IChannelPlayabilityChecker {
  checkPlayable(config: IChannelStrategyConfig, episodeNo: number): boolean;
}
```

### 缓存机制

- **渠道检测缓存**：单次检测，全局复用
- **策略配置缓存**：按渠道缓存，TTL 为 5 分钟
- **缓存管理**：提供清除和状态查询功能

## 扩展性

### 添加新渠道

1. 在 `Channels` 枚举中添加新渠道
2. 在 `FacebookChannelDetector` 中添加检测逻辑（或创建新的检测器）
3. 在 `ChannelStrategyProvider` 中添加策略配置
4. 在 `ChannelPlayabilityChecker` 中添加播放规则

```typescript
// 示例：添加微信渠道
export enum Channels {
  FACEBOOK = "facebook",
  WECHAT = "wechat",  // 新增
  DEFAULT = "default",
}

// 在检测器中添加逻辑
class MultiChannelDetector implements IChannelDetector {
  detect(): Channels {
    if (isFacebookBrowser()) return Channels.FACEBOOK;
    if (isWeChatBrowser()) return Channels.WECHAT;  // 新增
    return Channels.DEFAULT;
  }
}
```

### 自定义策略提供者

```typescript
class APIChannelStrategyProvider implements IChannelStrategyProvider {
  async getStrategy(channel: Channels): Promise<IChannelStrategyConfig> {
    const response = await fetch(`/api/channel-strategy/${channel}`);
    return response.json();
  }
}

// 在 ChannelManager 构造函数中替换
this.strategyProvider = new APIChannelStrategyProvider();
```

## 调试和监控

### 缓存状态查询

```typescript
const cacheStatus = channelManager.getCacheStatus();
console.log('Current channel:', cacheStatus.channel);
console.log('Cached channels:', cacheStatus.cachedChannels);
```

### 强制刷新缓存

```typescript
channelManager.clearCache();
const newStrategy = await channelManager.getChannelStrategy();
```

## 性能优势

1. **减少重复检测**：渠道检测只执行一次
2. **减少 API 调用**：策略配置缓存 5 分钟
3. **简化调用链**：从 3 步简化为 2 步
4. **内存效率**：使用 Map 进行高效缓存管理

## 测试

运行测试以验证功能：

```bash
npm test src/services/__tests__/ChannelManager.test.ts
```

测试覆盖：
- 渠道检测逻辑
- 缓存机制
- 策略获取
- 播放能力检查
- 单例模式
