module.exports = {
  mode: "jit",
  important: false,
  content: ["./src/**/*.{js,ts,jsx,tsx}", "./src/**/*.{css,less,scss,sass}", "./public/**/*.html"],
  theme: {
    extend: {
      boxShadow: {
        "custom-dropdown": "0px 0px 2px 0px rgba(0, 0, 0, 0.10), 0px 2px 10px 0px rgba(0, 0, 0, 0.10)"
      },
      colors: {
        primary: "var(--primary-color)",
        success: "var(--success-color)",
        warning: "var(--warning-color)",
        error: "var(--error-color)",
        info: "var(--info-color)"
      },
      spacing: {
        sm: "8px",
        md: "16px",
        lg: "24px",
        xl: "32px"
      }
    }
  },
  plugins: [],
  corePlugins: {
    preflight: false
  }
};
