### Resources
apiResources: &apiResources
  defaultResources:
    requests:
      cpu: 300m
      memory: 300Mi
    limits:
      cpu: 500m
      memory: 500Mi
  productionResources:
    requests:
      cpu: 500m
      memory: 300Mi
    limits:
      cpu: 1000m
      memory: 1000Mi
deployments:
  - name: short-player
    abTestingSupported: true
    containers:
      - name: short-player
        healthcheck:
          type: httpGet
          checkPort: 80
          checkPath: /ping
        <<: *apiResources
        envFromConfigmap:
          - global
    service:
      name: short-player
      ports:
        - name: http
          protocol: TCP
          port: 80
      routes:

        - name: short-player
          type: external
          servicePort: 80
          host: admin
          paths:
            - /short-player
          auth:
            enabled: true
            type: forward
            skipIfAuthFailed: true