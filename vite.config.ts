/// <reference types="vitest" />
import ShoplazzaI18NPlugin from "@shoplazza/i18n-vite-plugin";
import react from "@vitejs/plugin-react";
import autoprefixer from "autoprefixer";
import { resolve } from "path";
import postcssImport from "postcss-import";
import postcssPresetEnv from "postcss-preset-env";
import type { UserConfig } from "vite";
import { defineConfig } from "vite";
import cssInjectedByJsPlugin from "vite-plugin-css-injected-by-js";
import { createHtmlPlugin } from "vite-plugin-html";
import { analyzer } from "vite-bundle-analyzer";

import packageInfo from "./package.json";
import { viteMockServe } from "vite-plugin-mock";

/**
 * 常量配置
 */
const packageName = packageInfo.name;

/**
 * Vite 配置
 * @see https://vitejs.dev/config/
 */
export default defineConfig(({ mode }): UserConfig => {
  const useDevMode = mode === "development";

  /**
   * 开发环境配置
   */
  return {
    define: {
      PACKAGE_NAME: JSON.stringify(packageName)
    },

    resolve: {
      alias: {
        "@": resolve(__dirname, "src")
      }
    },

    /**
     * 构建配置
     */
    build: {
      assetsDir: "",
      rollupOptions: {
        // output: {
        //   file: `bs.${packageName}.[hash].js`,
        // },
        output: {
          // 使用包名作为文件名前缀，便于区分不同微应用
          entryFileNames: `bs.short-player.js`,
          // chunkFileNames: `bs.[name].[hash].js`,
          // assetFileNames: `[name].[hash][extname]`,
          // manualChunks(id) {
          //   if (id.includes('node_modules')) {
          //     return 'vendor';
          //   }
          //   if (id.includes('pages/')) {
          //     return id.split('pages/')[1].split('/')[0];
          //   }
          // }
        }
      },
      chunkSizeWarningLimit: 1000
    },

    /**
     * 插件配置
     */
    plugins: [
      // !useDevMode && analyzer(),
      useDevMode &&
        viteMockServe({
          mockPath: "src/__mock__", // mock文件地址
          enable: true,
          watchFiles: true
        }),
      react({
        babel: {
          plugins: [
            ["@babel/plugin-proposal-decorators", { legacy: true }],
            ["@babel/plugin-proposal-class-properties", { loose: true }]
          ]
        }
      }),
      // CSS 注入插件配置
      cssInjectedByJsPlugin({
        injectCode: cssCode => getStyleInjectCode(cssCode, packageName)
      }),
      // HTML 插件配置
      createHtmlPlugin({
        minify: true,
        inject: {
          data: {
            mode,
            packageName,
            dev_data: ""
          }
        }
      }),
      // 主分支启用国际化插件
      ...getI18nPlugins(),
    ],

    /**
     * CSS 相关配置
     */
    css: {
      devSourcemap: true,
      preprocessorOptions: {
        less: {
          // 自定义主题变量
          modifyVars: {
            "ant-prefix": packageName,
            "ant-icon-prefix": `${packageName}-icon`,
            "iconfont-css-prefix": `${packageName}-icon`
          },
          javascriptEnabled: true
        }
      },
      postcss: {
        plugins: [postcssImport(), autoprefixer(), postcssPresetEnv()]
      }
    },

    /**
     * 开发服务器配置
     */
    server: {
      port: 7000,
      host: "0.0.0.0",
      cors: true
    },

    test: {
      watch: false,
      environment: "jsdom",
      setupFiles: ["./src/test-setup.ts"],
      coverage: {
        provider: "v8",
        reporter: ["text", "json", "html"],
        include: ["src/**/*.ts", "src/**/*.tsx"],
        exclude: ["src/**/*.d.ts"]
      }
    }
  };
});

/**
 * 获取样式注入代码
 */
function getStyleInjectCode(cssCode: string, appName: string) {
  return `
    try {
      if(typeof document != 'undefined') {
        var elementStyle = document.createElement('style');
        elementStyle.className='micro-app-${appName}';
        elementStyle.appendChild(document.createTextNode(${cssCode}));
        document.head.appendChild(elementStyle);
      }
    } catch(e) {
      console.error('vite-plugin-css-injected-by-js', e);
    }
  `;
}

/**
 * 获取国际化插件配置
 */
function getI18nPlugins() {
  return process.env.CI_COMMIT_REF_SLUG === "master"
    ? [
        ShoplazzaI18NPlugin({
          fallbackCDNLink: {},
          app: `短剧播放页___${packageName}`,
          locales: {
            zh_CN: "**/zh_CN.ts"
          }
        })
      ]
    : [];
}
