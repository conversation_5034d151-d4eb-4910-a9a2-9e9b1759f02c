# H5 短剧播放器

基于 React 和 xgplayer 构建的移动端短剧播放器，支持 m3u8 视频格式。

## 功能特点

- 🎬 支持 m3u8 视频格式播放
- 📱 完全响应式设计，适配各种移动设备
- 🎵 自定义播放控制器
- 📋 剧集列表选择功能
- 🎨 现代化的 UI 设计
- ⚡ 流畅的动画效果
- 🔄 自动播放下一集功能

## 技术栈

- React 18
- TypeScript
- xgplayer (支持 HLS/m3u8)
- CSS3 (使用现代 CSS 特性)

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm start
```

应用将在 [http://localhost:3000](http://localhost:3000) 启动。

### 构建生产版本

```bash
npm run build
```

### 从 liquid 通过 whistle 代理到本地开发

见 `.whistle.js` 文件配置

1. 新建如下 rule:

> 域名替换为目标店铺

```
https://tuco.stg.myshoplaza.com/ resReplace://{bs-player-script-replacement}
```

2. 新建如下 value, 命名为: `bs-player-script-replacement`:

> 端口替换为本地合适的端口

```
<script type="module" src="/bs-player.js"></script>: <script type="module" src="http://localhost:7000/@vite/client"></script><script type="module">import RefreshRuntime from 'http://localhost:7000/@react-refresh';RefreshRuntime.injectIntoGlobalHook(window);window.$RefreshReg$ = () => {};window.$RefreshSig$ = () => (type) => type;window.__vite_plugin_react_preamble_installed__ = true;</script><script type="module" src="http://localhost:7000/src/index.tsx"></script>
```

> 原始环境文件(压缩前):

```html
<script type="module" src="http://localhost:7000/@vite/client"></script>
<script type="module">
  import RefreshRuntime from "http://localhost:7000/@react-refresh";
  RefreshRuntime.injectIntoGlobalHook(window);
  window.$RefreshReg$ = () => {};
  window.$RefreshSig$ = () => (type) => type;
  window.__vite_plugin_react_preamble_installed__ = true;
</script>
<script type="module" src="http://localhost:7000/src/index.tsx"></script>
```

### 从 liquid 通过本地打包验证产物

1. 使用 `npm run build:local` 命令, 打包到 public 路径为 `http://localhost:3000` 的产物链接
2. 复制产物中 html 的如下两行, 替换到 liquid 模板中:
```html
<script
  type="module"
  crossorigin
  src="http://localhost:3000/bs.short-player.aVIzul0L.js"
></script>
<link
  rel="modulepreload"
  crossorigin
  href="http://localhost:3000/bs.vendor.BsHiWk0w.js"
/>
```
3. 本地启动服务器, 注意需要打开跨域响应

示例: 使用 [serve](https://www.npmjs.com/package/serve) 命令:
```
serve -s -C dist
```
4. 此时 liquid 页面刷新即可正常渲染

## 组件结构

```
src/
├── components/
│   ├── ShortDramaPlayer.tsx    # 主播放器组件
│   └── ShortDramaPlayer.css    # 播放器样式
├── App.tsx                     # 应用入口组件
├── App.css                     # 应用样式
└── index.tsx                   # React 应用入口
```

## 播放器功能

### 基础播放控制

- 播放/暂停
- 上一集/下一集
- 音量控制
- 全屏播放

### 高级功能

- 手势控制（左右滑动快进快退，上下滑动调节音量/亮度）
- 自动播放下一集
- 剧集选择面板
- 播放进度记忆

### 自定义配置

可以通过修改 `ShortDramaPlayer.tsx` 中的 `episodes` 数组来配置视频源：

```typescript
const episodes: Episode[] = [
  {
    id: 1,
    title: "第1集：相遇",
    url: "your-m3u8-url.m3u8",
    poster: "poster-image-url.jpg",
    duration: 180,
  },
  // 更多剧集...
];
```

## 浏览器支持

- iOS Safari 10+
- Android Chrome 60+
- Android Firefox 60+
- 其他支持 HLS 的现代浏览器

## 许可证

MIT License
