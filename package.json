{"name": "short-player", "version": "0.1.0", "private": true, "dependencies": {"hls.js": "^1.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "xgplayer": "^3.0.19", "xgplayer-subtitles": "^3.0.22"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.25.9", "@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@shoplazza/admin-eslint": "*", "@shoplazza/admin-prettier": "*", "@shoplazza/admin-tsconfig": "*", "@shoplazza/i18n-vite-plugin": "^1.3.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/lodash-es": "4.17.12", "@types/mockjs": "^1.0.10", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-react-swc": "^3.7.2", "@vitest/coverage-v8": "^3.2.4", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.3.0", "mockjs": "^1.1.0", "postcss": "^8.4.49", "postcss-import": "^16.1.0", "postcss-preset-env": "^10.1.3", "typescript": "^5.7.2", "typescript-plugin-css-modules": "^5.1.0", "vite": "^6.0.6", "vite-bundle-analyzer": "^1.0.0", "vite-plugin-css-injected-by-js": "^3.5.2", "vite-plugin-html": "^3.2.2", "vite-plugin-imp": "^2.4.0", "vite-plugin-mock": "^3.0.2", "vite-plugin-qiankun": "^1.0.15", "vitest": "^3.2.4"}, "scripts": {"dev": "vite", "build": "vite build", "build:local": "vite build", "preview": "vite preview", "format": "npm run format:prettier && npm run lint", "lint": "eslint --fix", "format:prettier": "prettier --write \"src/**/*.{js,jsx,ts,tsx,css,less,scss,json,md}\"", "i18n:zh": "shoplazza-i18n upload -p '**/zh_CN.ts' -l 'zh_CN' -a '短剧播放页___short-player'", "i18n:en": "shoplazza-i18n upload -p '**/en_US.ts' -l 'en_US' -a '短剧播放页___short-player'", "test": "vitest", "test:coverage": "vitest run --coverage"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{ts,tsx,js}": ["npm run format"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}