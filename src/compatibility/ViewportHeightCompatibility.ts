import { moduleLoggers } from "@/utils/LogManager";
import {
  isFacebookBrowser as detectFacebookBrowser,
  isHarmonyDevice,
  isIOSDevice,
} from "@/utils/userAgentDetection";

/**
 * @file ViewportHeightCompatibility.ts
 * @description 视口高度兼容性模块
 * 解决各种机型的高度适配问题，包括地址栏收缩、安全区域等
 */

// 设备类型检测 - 使用统一的 UA 检测工具
const isIOS = isIOSDevice();
const isHarmony = isHarmonyDevice();

// Facebook 内置浏览器检测 - 使用统一的 UA 检测工具
const isFacebookBrowser = detectFacebookBrowser();

// CSS变量名
const VH_VARIABLE = "--csp-product-vh";

/**
 * 视口高度兼容性管理器
 */
class ViewportHeightCompatibility {
  private static instance: ViewportHeightCompatibility;
  private scrollTimeout: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private listeners: Set<(vh: number) => void> = new Set();
  private lastKnownHeight = 0;
  // 添加事件监听器引用，用于清理
  private boundEventHandlers: {
    load?: () => void;
    resize?: () => void;
    orientationchange?: () => void;
    scroll?: () => void;
    visualViewportResize?: () => void;
    focus?: () => void;
    blur?: () => void;
    pageshow?: () => void;
    pagehide?: () => void;
    touchend?: () => void;
    touchstart?: () => void;
    touchmove?: () => void;
  } = {};

  // Facebook 内置浏览器高度检测相关
  private heightCheckInterval: NodeJS.Timeout | null = null;
  private animationFrameId: number | null = null;
  private lastCheckedHeight = 0;
  private heightCheckEnabled = false;
  private touchEndTimeout: NodeJS.Timeout | null = null;
  private forceUpdateTimeout: NodeJS.Timeout | null = null;
  private heightHistory: number[] = [];
  private visualViewportResizeTimeout: NodeJS.Timeout | null = null;
  private playerMounted = false; // 标记播放器是否已挂载
  private scrollCheckTimeout: NodeJS.Timeout | null = null;
  private setVHVariableDebounceTimeout: NodeJS.Timeout | null = null;
  private facebookResizeTimeout: NodeJS.Timeout | null = null; // Facebook resize延迟处理

  private constructor() {}

  static getInstance(): ViewportHeightCompatibility {
    if (!ViewportHeightCompatibility.instance) {
      ViewportHeightCompatibility.instance = new ViewportHeightCompatibility();
    }
    return ViewportHeightCompatibility.instance;
  }

  /**
   * 初始化视口高度兼容性
   */
  init(): void {
    moduleLoggers.Compatibility.info("初始化视口高度兼容性", {
      isIOS,
      isHarmony,
      isFacebookBrowser,
      userAgent: navigator.userAgent,
      wasInitialized: this.isInitialized,
    });

    // 如果已经初始化，先清理再重新初始化
    if (this.isInitialized) {
      moduleLoggers.Compatibility.info(
        "视口高度兼容性已初始化，先清理再重新初始化",
      );
      this.cleanup();
    }

    // 阻止body事件
    this.blockBodyEvent();

    // 设置初始高度
    this.setVHVariable();

    // 绑定事件监听器
    this.bindEventListeners();

    // 延迟设置，确保DOM完全加载
    this.scheduleDelayedUpdates();

    // 如果是 iOS，启用主动高度检测
    this.startActiveHeightDetection();

    this.isInitialized = true;
    moduleLoggers.Compatibility.info("视口高度兼容性初始化完成");
  }

  /**
   * 启用主动高度检测（针对 iOS 设备）
   */
  private startActiveHeightDetection(): void {
    this.heightCheckEnabled = true;
    this.lastCheckedHeight = window.innerHeight;

    moduleLoggers.Compatibility.info("启用 iOS 设备主动高度检测", {
      initialHeight: this.lastCheckedHeight,
      hasVisualViewport: "visualViewport" in window,
      isFacebookBrowser,
    });

    // 优先使用 visualViewport 的 resize 事件（用户提供的有效方案）
    if ("visualViewport" in window) {
      const visualViewport = (window as any).visualViewport;

      // 防抖处理 visualViewport resize 事件
      const debouncedHandleVisualViewportResize = () => {
        if (this.visualViewportResizeTimeout) {
          clearTimeout(this.visualViewportResizeTimeout);
        }

        this.visualViewportResizeTimeout = setTimeout(() => {
          if (!this.heightCheckEnabled) return;

          const currentHeight = visualViewport.height;
          const heightDiff = Math.abs(currentHeight - this.lastCheckedHeight);

          if (heightDiff > 3) {
            moduleLoggers.Compatibility.info(
              "iOS 设备 visualViewport 检测到高度变化",
              {
                previousHeight: this.lastCheckedHeight,
                currentHeight,
                visualViewportWidth: visualViewport.width,
                visualViewportHeight: visualViewport.height,
                difference: heightDiff,
                playerMounted: this.playerMounted,
              },
            );

            // 记录高度历史
            this.heightHistory.push(currentHeight);
            if (this.heightHistory.length > 10) {
              this.heightHistory.shift();
            }

            this.lastCheckedHeight = currentHeight;
            this.setVHVariable();

            // 如果播放器已挂载，强制更新容器
            if (this.playerMounted) {
              this.forceContainerUpdate();
              this.forcePlayerContainerUpdate(currentHeight);
            }
          }
        }, 1000);
      };

      // 添加 visualViewport resize 监听器
      visualViewport.addEventListener(
        "resize",
        debouncedHandleVisualViewportResize,
      );

      // 保存引用用于清理
      this.boundEventHandlers.visualViewportResize =
        debouncedHandleVisualViewportResize;

      moduleLoggers.Compatibility.info("已启用 visualViewport resize 监听器");
    }

    // 启动连续的高度检测作为备用方案
    this.startContinuousHeightCheck();
  }

  /**
   * 启动连续的高度检测（备用方案）
   */
  private startContinuousHeightCheck(): void {
    if (this.heightCheckInterval) {
      clearInterval(this.heightCheckInterval);
    }

    this.heightCheckInterval = setInterval(() => {
      if (!this.heightCheckEnabled || !this.playerMounted) return;

      const currentHeight = window.innerHeight;
      const visualHeight =
        "visualViewport" in window
          ? (window as any).visualViewport.height
          : currentHeight;

      // 使用更敏感的高度变化检测
      const actualHeight = Math.max(currentHeight, visualHeight);
      const heightDiff = Math.abs(actualHeight - this.lastCheckedHeight);

      if (heightDiff > 2) {
        moduleLoggers.Compatibility.info("连续检测到高度变化", {
          previousHeight: this.lastCheckedHeight,
          currentHeight: actualHeight,
          innerHeight: currentHeight,
          visualHeight,
          difference: heightDiff,
        });

        this.lastCheckedHeight = actualHeight;
        this.setVHVariable();
        this.forcePlayerContainerUpdate(actualHeight);
      }
    }, 100); // 每100ms检查一次
  }

  /**
   * 强制更新播放器容器（专门处理导航栏变化）
   */
  private forcePlayerContainerUpdate(height: number): void {
    moduleLoggers.Compatibility.info("强制更新播放器容器", {
      height,
      playerMounted: this.playerMounted,
      timestamp: Date.now(),
    });

    // 立即执行一次
    this.updatePlayerContainers(height);

    // 延迟执行确保更新生效
    setTimeout(() => {
      this.updatePlayerContainers(height);
    }, 10);

    // 再次延迟执行，确保所有容器都正确更新
    setTimeout(() => {
      this.updatePlayerContainers(height);
    }, 100);

    // 最后一次确认更新
    setTimeout(() => {
      this.updatePlayerContainers(height);
    }, 300);
  }

  /**
   * 更新播放器容器尺寸
   */
  private updatePlayerContainers(height: number): void {
    const containers = [
      document.getElementById("custom-solution-product-container"),
      document.querySelector(".app"),
      document.querySelector(".player-container"),
      document.querySelector(".player-wrapper"),
      document.querySelector(".short-drama-player"),
      document.querySelector(".video-player"),
      document.querySelector(".player"),
      document.querySelector("#root"),
      document.querySelector("#app"),
    ].filter(Boolean) as HTMLElement[];

    containers.forEach((container) => {
      if (container) {
        // 强制设置高度
        container.style.height = `${height}px`;
        container.style.minHeight = `${height}px`;
        container.style.maxHeight = `${height}px`;

        // 确保容器不会有内部滚动
        container.style.overflow = "hidden";

        // 移除可能导致高度问题的属性
        container.style.paddingBottom = "0px";
        container.style.paddingTop = "0px";
        container.style.marginBottom = "0px";
        container.style.marginTop = "0px";

        // 强制重新计算布局
        container.offsetHeight;

        moduleLoggers.Compatibility.info("更新容器", {
          id: container.id,
          className: container.className,
          height: container.style.height,
          scrollHeight: container.scrollHeight,
          offsetHeight: container.offsetHeight,
          timestamp: Date.now(),
        });
      }
    });

    // 确保 body 和 html 也不会超出
    document.body.style.height = `${height}px`;
    document.body.style.overflow = "hidden";
    document.documentElement.style.height = `${height}px`;
    document.documentElement.style.overflow = "hidden";
  }

  /**
   * 强制更新容器
   */
  private forceContainerUpdate(): void {
    // 延迟执行，确保高度变化已经稳定
    if (this.forceUpdateTimeout) {
      clearTimeout(this.forceUpdateTimeout);
    }

    this.forceUpdateTimeout = setTimeout(() => {
      const currentHeight = window.innerHeight;
      moduleLoggers.Compatibility.info("强制更新容器高度", {
        currentHeight,
        historyHeights: this.heightHistory,
        playerMounted: this.playerMounted,
      });

      // 强制重新计算和应用高度
      this.setVHVariable();

      // 触发窗口resize事件，确保所有监听器都能收到通知
      const resizeEvent = new Event("resize");
      window.dispatchEvent(resizeEvent);

      // 强制重新渲染
      this.forceContainerResize(currentHeight);
    }, 50);
  }

  /**
   * 处理Facebook浏览器的resize事件
   * Facebook浏览器在导航栏收缩时需要延迟获取准确的高度
   */
  private handleFacebookResizeEvent(): void {
    // 清除之前的延迟处理
    if (this.facebookResizeTimeout) {
      clearTimeout(this.facebookResizeTimeout);
    }

    // 立即记录当前高度
    const immediateHeight = window.innerHeight;

    moduleLoggers.Compatibility.info("Facebook resize事件触发", {
      immediateHeight,
      userAgent: navigator.userAgent,
    });

    // 立即执行一次标准处理
    this.setVHVariableDebounce();

    // 延迟1秒后再次获取高度并更新
    this.facebookResizeTimeout = setTimeout(() => {
      // 直接使用 window.innerHeight，这是延迟1秒后的准确高度
      const delayedHeight = window.innerHeight;

      moduleLoggers.Compatibility.info("Facebook resize延迟处理", {
        immediateHeight,
        delayedHeight,
        heightDifference: delayedHeight - immediateHeight,
      });

      // 如果延迟后的高度与立即获取的高度不同，说明导航栏确实发生了变化
      if (Math.abs(delayedHeight - immediateHeight) > 5) {
        moduleLoggers.Compatibility.info("Facebook导航栏高度变化检测到", {
          heightChange: delayedHeight - immediateHeight,
        });

        // 直接使用延迟后的准确高度更新容器
        this.updateContainerWithAccurateHeight(delayedHeight);
      }
    }, 1000); // 延迟1秒
  }

  /**
   * 使用准确的高度值更新容器
   * 直接使用延迟后获取的 window.innerHeight
   */
  private updateContainerWithAccurateHeight(accurateHeight: number): void {
    moduleLoggers.Compatibility.info("使用准确高度更新容器", {
      accurateHeight,
    });

    // 直接更新 custom-solution-product-container
    const container = document.getElementById(
      "custom-solution-product-container",
    );
    if (container) {
      // 直接设置高度，不考虑其他复杂计算
      container.style.height = `${accurateHeight}px`;
      container.style.minHeight = `${accurateHeight}px`;
      container.style.maxHeight = `${accurateHeight}px`;

      // 设置CSS变量
      const vh = accurateHeight * 0.01;
      container.style.setProperty("--csp-product-vh", `${vh}px`);
      document.documentElement.style.setProperty("--csp-product-vh", `${vh}px`);

      // 强制重新计算布局
      container.offsetHeight;

      moduleLoggers.Compatibility.info(
        "custom-solution-product-container 已更新为准确高度",
        {
          height: accurateHeight,
          containerHeight: container.style.height,
          vh,
        },
      );
    }

    // 同时更新其他相关容器
    const otherContainers = [
      document.querySelector(".app"),
      document.querySelector(".player-container"),
      document.querySelector(".player-wrapper"),
    ].filter(Boolean) as HTMLElement[];

    otherContainers.forEach((otherContainer) => {
      if (otherContainer) {
        otherContainer.style.height = `${accurateHeight}px`;
        otherContainer.offsetHeight; // 强制重新计算
      }
    });

    // 触发自定义事件，通知其他组件高度已更新
    window.dispatchEvent(
      new CustomEvent("viewport-height-changed", {
        detail: { vh: accurateHeight },
      }),
    );
  }

  /**
   * 强制更新 custom-solution-product-container 容器
   * 专门针对这个主容器进行多重强制更新
   */
  private forceUpdateCustomSolutionContainer(height: number): void {
    const container = document.getElementById(
      "custom-solution-product-container",
    );

    if (!container) {
      moduleLoggers.Compatibility.warn(
        "未找到 custom-solution-product-container 容器",
      );
      return;
    }

    moduleLoggers.Compatibility.info(
      "强制更新 custom-solution-product-container",
      {
        height,
        currentHeight: container.style.height,
        offsetHeight: container.offsetHeight,
      },
    );

    // 多重强制更新策略
    const updateContainer = () => {
      // 1. 直接设置高度
      container.style.height = `${height}px`;
      container.style.minHeight = `${height}px`;
      container.style.maxHeight = `${height}px`;

      // 2. 确保定位和布局正确
      container.style.position = "fixed";
      container.style.top = "0px";
      container.style.left = "0px";
      container.style.right = "0px";
      container.style.bottom = "0px";
      container.style.width = "100%";

      // 3. 移除可能影响高度的属性
      container.style.paddingTop = "0px";
      container.style.paddingBottom = "0px";
      container.style.marginTop = "0px";
      container.style.marginBottom = "0px";
      container.style.overflow = "hidden";

      // 4. 强制重新计算布局
      container.offsetHeight;

      // 5. 设置CSS变量
      container.style.setProperty("--csp-product-vh", `${height * 0.01}px`);
    };

    // 立即执行一次
    updateContainer();

    // 使用 requestAnimationFrame 在下一帧再次执行
    requestAnimationFrame(() => {
      updateContainer();

      // 再次延迟执行，确保更新生效
      setTimeout(() => {
        updateContainer();

        moduleLoggers.Compatibility.info(
          "custom-solution-product-container 更新完成",
          {
            finalHeight: container.style.height,
            finalOffsetHeight: container.offsetHeight,
          },
        );
      }, 50);
    });
  }

  /**
   * 确保 custom-solution-product-container 容器高度正确
   * 这是一个轻量级的检查和更新方法
   */
  private ensureCustomSolutionContainerHeight(height: number): void {
    const container = document.getElementById(
      "custom-solution-product-container",
    );

    if (!container) {
      return;
    }

    // 检查当前高度是否正确
    const currentHeight =
      parseInt(container.style.height) || container.offsetHeight;
    const heightDiff = Math.abs(currentHeight - height);

    // 如果高度差异超过5px，进行更新
    if (heightDiff > 5) {
      moduleLoggers.Compatibility.info(
        "检测到 custom-solution-product-container 高度不正确，进行更新",
        {
          currentHeight,
          targetHeight: height,
          difference: heightDiff,
        },
      );

      // 直接设置高度和相关样式
      container.style.height = `${height}px`;
      container.style.setProperty("--csp-product-vh", `${height * 0.01}px`);

      // 强制重新计算布局
      container.offsetHeight;
    }
  }

  /**
   * 停止主动高度检测
   */
  private stopActiveHeightDetection(): void {
    this.heightCheckEnabled = false;

    if (this.animationFrameId) {
      cancelAnimationFrame(this.animationFrameId);
      this.animationFrameId = null;
    }

    if (this.heightCheckInterval) {
      clearInterval(this.heightCheckInterval);
      this.heightCheckInterval = null;
    }

    if (this.visualViewportResizeTimeout) {
      clearTimeout(this.visualViewportResizeTimeout);
      this.visualViewportResizeTimeout = null;
    }

    moduleLoggers.Compatibility.info("停止 iOS 设备主动高度检测");
  }

  /**
   * 阻止body事件
   */
  private blockBodyEvent(): void {
    document.body.classList.add("bs-body-blocked");
  }

  /**
   * 恢复body事件
   */
  private restoreBodyEvent(): void {
    document.body.classList.remove("bs-body-blocked");
  }

  /**
   * 设置视口高度CSS变量
   */
  private setVHVariable(): void {
    moduleLoggers.Compatibility.info("Touch: setVHVariable", {
      isIOS,
      isHarmony,
      isFacebookBrowser,
      innerHeight: window.innerHeight,
      clientHeight: document.documentElement.clientHeight,
    });
    // 获取安全区域信息
    const safeAreaTop = this.getSafeAreaInset("top");
    const safeAreaBottom = this.getSafeAreaInset("bottom");

    // 使用visualViewport API获取实际可用高度（如果可用）
    let availableHeight = window.innerHeight;
    if ("visualViewport" in window) {
      availableHeight = (window as any).visualViewport.height;
    }

    // 根据不同设备类型使用不同的高度计算策略
    let effectiveHeight: number;

    if (isIOS) {
      // iOS设备：优先使用visualViewport，确保不超出屏幕
      // 如果没有visualViewport，则使用innerHeight
      effectiveHeight = availableHeight;
    } else if (isHarmony) {
      // 鸿蒙系统：优先使用document.documentElement.clientHeight，更精确地表示内容区域高度
      effectiveHeight = document.documentElement.clientHeight;
    } else {
      // 其他设备：使用更保守的计算方式，取最小值确保不超出视口
      effectiveHeight = Math.min(
        availableHeight,
        document.documentElement.clientHeight,
        window.screen.height,
      );
    }

    moduleLoggers.Compatibility.info("视口高度计算", {
      effectiveHeight,
      availableHeight,
      safeAreaTop,
      safeAreaBottom,
    });

    const vh = effectiveHeight * 0.01;

    // 设置CSS变量
    document.documentElement.style.setProperty(VH_VARIABLE, `${vh}px`);

    // 触发自定义事件
    this.dispatchViewportHeightChanged(vh * 100);

    // 强制更新容器尺寸（统一处理所有机型）
    this.forceContainerResize(vh * 100);

    // 特别确保 custom-solution-product-container 被正确更新
    this.ensureCustomSolutionContainerHeight(vh * 100);

    // 记录高度变化
    if (this.lastKnownHeight !== vh * 100) {
      moduleLoggers.Compatibility.info("视口高度已更新", {
        vh: vh * 100,
        previousHeight: this.lastKnownHeight,
        change: vh * 100 - this.lastKnownHeight,
      });
      this.lastKnownHeight = vh * 100;
    }
  }

  private setVHVariableDebounce() {
    if (this.setVHVariableDebounceTimeout) {
      clearTimeout(this.setVHVariableDebounceTimeout);
    }

    this.setVHVariableDebounceTimeout = setTimeout(() => {
      this.setVHVariable();
    }, 1000);
  }

  /**
   * 获取安全区域插入值
   */
  private getSafeAreaInset(type: "top" | "bottom" | "left" | "right"): number {
    const envValue = getComputedStyle(
      document.documentElement,
    ).getPropertyValue(`env(safe-area-inset-${type})`);

    if (envValue) {
      const parsed = parseFloat(envValue);
      return isNaN(parsed) ? 0 : parsed;
    }

    // 回退值
    const fallbackValues = {
      top: 0,
      bottom: isIOS ? 34 : 0, // iOS底部安全区域默认34px
      left: 0,
      right: 0,
    };

    return fallbackValues[type];
  }

  /**
   * 强制更新容器尺寸
   */
  private forceContainerResize(height: number): void {
    // 查找所有相关容器，包括更多可能的播放器容器
    const containers = [
      document.getElementById("custom-solution-product-container"),
      document.querySelector(".app"),
      document.querySelector(".player-container"),
      document.querySelector(".player-wrapper"),
      document.querySelector(".short-drama-player"), // 短剧播放器
      document.querySelector(".video-player"),
      document.querySelector(".player"),
      document.querySelector("#root"), // React 根容器
      document.querySelector("#app"), // Vue 应用容器
    ].filter(Boolean) as HTMLElement[];

    moduleLoggers.Compatibility.info("强制容器调整大小", {
      height,
      foundContainers: containers.length,
      containerSelectors: containers.map(
        (c) => c.id || c.className || c.tagName,
      ),
    });

    containers.forEach((container) => {
      if (container) {
        container.style.height = `${height}px`;

        // 统一处理：确保容器能够全屏铺满
        // 移除任何可能影响全屏显示的padding
        container.style.paddingBottom = "0px";
        container.style.paddingTop = "0px";
        container.style.paddingLeft = "0px";
        container.style.paddingRight = "0px";

        // 确保容器定位正确
        if (
          container.style.position === "fixed" ||
          container.classList.contains("fullscreen")
        ) {
          container.style.top = "0px";
          container.style.left = "0px";
          container.style.right = "0px";
          container.style.bottom = "0px";
        }
      }
    });

    // iOS 设备特殊处理：强制重新计算布局
    if (isIOS) {
      // 使用 requestAnimationFrame 确保在下一帧重新计算
      requestAnimationFrame(() => {
        containers.forEach((container) => {
          if (container) {
            // 临时改变一个样式属性来强制重新计算
            const originalTransform = container.style.transform;
            container.style.transform = "translateZ(0)";
            // 强制重新计算
            container.offsetHeight;
            container.style.transform = originalTransform;

            // 再次设置高度
            container.style.height = `${height}px`;

            // 触发重新布局
            container.style.display = "block";
          }
        });

        // 再次延迟确保布局完成
        setTimeout(() => {
          containers.forEach((container) => {
            if (container) {
              container.style.height = `${height}px`;
            }
          });
        }, 50);
      });
    }
  }

  /**
   * 绑定事件监听器
   */
  private bindEventListeners(): void {
    // 页面加载完成
    this.boundEventHandlers.load = () => this.setVHVariableDebounce();
    window.addEventListener("load", this.boundEventHandlers.load);

    // 窗口大小变化
    this.boundEventHandlers.resize = () => {
      // this.handleFacebookResizeEvent();
      this.setVHVariableDebounce();
    };
    window.addEventListener("resize", this.boundEventHandlers.resize);

    // 设备方向变化
    this.boundEventHandlers.orientationchange = () => {
      this.setVHVariableDebounce();
    };
    window.addEventListener(
      "orientationchange",
      this.boundEventHandlers.orientationchange,
    );

    // 统一处理滚动事件监听，适用于所有机型
    this.boundEventHandlers.scroll = () => {
      if (this.scrollTimeout) {
        clearTimeout(this.scrollTimeout);
      }

      // 立即检查高度变化（针对滚动导致的导航栏变化）
      if (this.playerMounted && isIOS) {
        this.checkHeightChangeOnScroll();
      }

      this.scrollTimeout = setTimeout(() => this.setVHVariableDebounce());
    };
    window.addEventListener("scroll", this.boundEventHandlers.scroll);

    // 监听视觉视口变化（如果可用且不是 Facebook 内置浏览器）
    // Facebook 内置浏览器的 visualViewport 处理在 startActiveHeightDetection 中
    if ("visualViewport" in window) {
      this.boundEventHandlers.visualViewportResize = () => {
        moduleLoggers.Compatibility.info("visualViewportResize", {
          visualViewportHeight: (window as any).visualViewport.height,
        });
        this.setVHVariableDebounce();
      };
      (window as any).visualViewport.addEventListener(
        "resize",
        this.boundEventHandlers.visualViewportResize,
      );
    }

    // 针对 iOS 设备，添加额外的事件监听
    if (isIOS) {
      // 页面获得焦点时检查高度
      this.boundEventHandlers.focus = () => {
        moduleLoggers.Compatibility.info("iOS 设备页面获得焦点，检查高度");
        this.setVHVariableDebounce();
      };
      window.addEventListener("focus", this.boundEventHandlers.focus);

      // 页面失去焦点时检查高度
      this.boundEventHandlers.blur = () => {
        moduleLoggers.Compatibility.info("iOS 设备页面失去焦点，检查高度");
        this.setVHVariableDebounce();
      };
      window.addEventListener("blur", this.boundEventHandlers.blur);

      // 页面显示时检查高度
      this.boundEventHandlers.pageshow = () => {
        moduleLoggers.Compatibility.info("iOS 设备页面显示，检查高度");
        this.setVHVariableDebounce();
      };
      window.addEventListener("pageshow", this.boundEventHandlers.pageshow);

      // 页面隐藏时检查高度
      this.boundEventHandlers.pagehide = () => {
        moduleLoggers.Compatibility.info("iOS 设备页面隐藏，检查高度");
        this.setVHVariableDebounce();
      };
      window.addEventListener("pagehide", this.boundEventHandlers.pagehide);

      // 监听触摸开始事件
      this.boundEventHandlers.touchstart = () => {
        this.lastCheckedHeight = window.innerHeight;
      };
      document.addEventListener(
        "touchstart",
        this.boundEventHandlers.touchstart,
        {
          passive: true,
        },
      );

      // 监听触摸移动事件
      this.boundEventHandlers.touchmove = () => {
        // 在触摸移动时也检查高度变化
        const currentHeight = window.innerHeight;
        if (Math.abs(currentHeight - this.lastCheckedHeight) > 3) {
          moduleLoggers.Compatibility.info("iOS 设备触摸移动时检测到高度变化", {
            previousHeight: this.lastCheckedHeight,
            currentHeight,
          });
          this.lastCheckedHeight = currentHeight;
          this.setVHVariableDebounce();
        }
      };
      document.addEventListener(
        "touchmove",
        this.boundEventHandlers.touchmove,
        {
          passive: true,
        },
      );

      // 监听触摸事件，在触摸结束后检查高度变化
      this.boundEventHandlers.touchend = () => {
        if (this.touchEndTimeout) {
          clearTimeout(this.touchEndTimeout);
        }
        this.touchEndTimeout = setTimeout(() => {
          const currentHeight = window.innerHeight;
          if (Math.abs(currentHeight - this.lastCheckedHeight) > 3) {
            moduleLoggers.Compatibility.info(
              "iOS 设备触摸结束后检测到高度变化",
              {
                previousHeight: this.lastCheckedHeight,
                currentHeight,
              },
            );
            this.lastCheckedHeight = currentHeight;
            this.setVHVariableDebounce();
            this.forceContainerUpdate();

            // 如果播放器已挂载，强制更新播放器容器
            if (this.playerMounted) {
              this.forcePlayerContainerUpdate(currentHeight);
            }
          }
        }, 50); // 减少延迟，更快响应
      };

      document.addEventListener("touchend", this.boundEventHandlers.touchend, {
        passive: true,
      });
    }
  }

  /**
   * 滚动时检查高度变化
   */
  private checkHeightChangeOnScroll(): void {
    if (this.scrollCheckTimeout) {
      clearTimeout(this.scrollCheckTimeout);
    }

    this.scrollCheckTimeout = setTimeout(() => {
      const currentHeight = window.innerHeight;
      const visualHeight =
        "visualViewport" in window
          ? (window as any).visualViewport.height
          : currentHeight;

      const actualHeight = Math.max(currentHeight, visualHeight);
      const heightDiff = Math.abs(actualHeight - this.lastCheckedHeight);

      if (heightDiff > 2) {
        moduleLoggers.Compatibility.info("滚动时检测到高度变化", {
          previousHeight: this.lastCheckedHeight,
          currentHeight: actualHeight,
          innerHeight: currentHeight,
          visualHeight,
          difference: heightDiff,
          playerMounted: this.playerMounted,
        });

        this.lastCheckedHeight = actualHeight;
        this.setVHVariableDebounce();

        if (this.playerMounted) {
          this.forcePlayerContainerUpdate(actualHeight);
        }
      }
    }, 1000);
  }

  /**
   * 安排延迟更新
   */
  private scheduleDelayedUpdates(): void {
    // 统一处理延迟更新，确保所有机型都能正确计算高度
    // DOM加载完成后延迟更新
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.setVHVariableDebounce();
      });
    } else {
      this.setVHVariableDebounce();
    }
  }

  /**
   * 触发视口高度变化事件
   */
  private dispatchViewportHeightChanged(vh: number): void {
    // 触发自定义事件
    window.dispatchEvent(
      new CustomEvent("viewport-height-changed", {
        detail: { vh },
      }),
    );

    // 通知所有监听器
    this.listeners.forEach((listener) => {
      try {
        listener(vh);
      } catch (error) {
        moduleLoggers.Compatibility.error("视口高度变化监听器执行失败", error);
      }
    });
  }

  /**
   * 添加视口高度变化监听器
   */
  addViewportHeightListener(listener: (vh: number) => void): () => void {
    this.listeners.add(listener);

    // 返回移除函数
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 获取当前视口高度
   */
  getCurrentVH(): number {
    // 对于 iOS 设备，优先使用实际的当前高度
    if (isIOS) {
      let currentHeight = window.innerHeight;

      // 如果有 visualViewport，使用 visualViewport 的高度
      if ("visualViewport" in window) {
        currentHeight = (window as any).visualViewport.height;
      }

      moduleLoggers.Compatibility.info("iOS 设备获取当前高度", {
        innerHeight: window.innerHeight,
        visualViewportHeight:
          "visualViewport" in window
            ? (window as any).visualViewport.height
            : null,
        currentHeight,
      });

      return currentHeight;
    }

    // 首先尝试从 CSS 变量获取，但需要验证值的合理性
    const vhValue = getComputedStyle(document.documentElement)
      .getPropertyValue(VH_VARIABLE)
      .replace("px", "");

    const parsedVh = parseFloat(vhValue);

    // 验证 CSS 变量值的合理性
    // 如果值小于 1，说明可能是错误的 CSS 变量值（如 0.0667px）
    // 此时应该使用 window.innerHeight 作为回退
    if (isNaN(parsedVh) || parsedVh < 1) {
      moduleLoggers.Compatibility.warn(
        "CSS 变量值异常，使用 window.innerHeight 作为回退",
        {
          cssVariableValue: vhValue,
          parsedValue: parsedVh,
          innerHeight: window.innerHeight,
        },
      );
      return window.innerHeight;
    }

    // 如果 CSS 变量值合理，将其乘以 100 转换为完整的视口高度
    // 因为 CSS 变量存储的是 vh 单位值（如 6.67px），需要转换为完整高度（如 667px）
    const fullHeight = parsedVh * 100;

    // 再次验证转换后的值是否合理
    if (fullHeight < 100 || fullHeight > 10000) {
      moduleLoggers.Compatibility.warn(
        "转换后的视口高度值异常，使用 window.innerHeight 作为回退",
        {
          cssVariableValue: vhValue,
          parsedValue: parsedVh,
          convertedHeight: fullHeight,
          innerHeight: window.innerHeight,
        },
      );
      return window.innerHeight;
    }

    return fullHeight;
  }

  /**
   * 手动触发视口高度更新
   */
  updateViewportHeight(): void {
    this.setVHVariable();
  }

  /**
   * 确保获取正确的当前高度（供播放器挂载时使用）
   */
  ensureCorrectHeight(): number {
    moduleLoggers.Compatibility.info("确保获取正确的当前高度", {
      beforeUpdate: {
        innerHeight: window.innerHeight,
        visualViewportHeight:
          "visualViewport" in window
            ? (window as any).visualViewport.height
            : null,
        currentVH: this.getCurrentVH(),
      },
    });

    // 强制更新一次高度
    this.setVHVariable();

    // 获取当前正确的高度
    const currentHeight = this.getCurrentVH();

    // 强制更新容器
    this.forceContainerResize(currentHeight);

    moduleLoggers.Compatibility.info("强制更新后的高度", {
      updatedHeight: currentHeight,
    });

    return currentHeight;
  }

  /**
   * 播放器挂载时调用此方法确保高度正确
   */
  onPlayerMount(): void {
    this.playerMounted = true;
    moduleLoggers.Compatibility.info("播放器挂载，检查并更新高度");

    // 立即更新一次
    const currentHeight = this.ensureCorrectHeight();
    this.forcePlayerContainerUpdate(currentHeight);

    // 延迟一下，确保播放器 DOM 已经挂载
    setTimeout(() => {
      const height = this.ensureCorrectHeight();
      this.forcePlayerContainerUpdate(height);
    }, 100);

    // 再次延迟检查，确保高度正确
    setTimeout(() => {
      const height = this.ensureCorrectHeight();
      this.forcePlayerContainerUpdate(height);
    }, 500);

    // 启动更积极的高度监测
    this.startAggressiveHeightCheck();
  }

  /**
   * 启动更积极的高度监测（仅在播放器挂载时）
   */
  private startAggressiveHeightCheck(): void {
    if (!this.playerMounted || !isIOS) return;

    const aggressiveCheck = () => {
      if (!this.playerMounted) return;

      const currentHeight = window.innerHeight;
      const visualHeight =
        "visualViewport" in window
          ? (window as any).visualViewport.height
          : currentHeight;

      const actualHeight = Math.max(currentHeight, visualHeight);
      const heightDiff = Math.abs(actualHeight - this.lastCheckedHeight);

      if (heightDiff > 1) {
        moduleLoggers.Compatibility.info("积极检测到高度变化", {
          previousHeight: this.lastCheckedHeight,
          currentHeight: actualHeight,
          difference: heightDiff,
        });

        this.lastCheckedHeight = actualHeight;
        this.forcePlayerContainerUpdate(actualHeight);
      }

      // 继续检测
      if (this.playerMounted) {
        requestAnimationFrame(aggressiveCheck);
      }
    };

    requestAnimationFrame(aggressiveCheck);
  }

  /**
   * 播放器卸载时调用
   */
  onPlayerUnmount(): void {
    this.playerMounted = false;
    moduleLoggers.Compatibility.info("播放器已卸载");
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      this.scrollTimeout = null;
    }

    if (this.touchEndTimeout) {
      clearTimeout(this.touchEndTimeout);
      this.touchEndTimeout = null;
    }

    if (this.forceUpdateTimeout) {
      clearTimeout(this.forceUpdateTimeout);
      this.forceUpdateTimeout = null;
    }

    if (this.visualViewportResizeTimeout) {
      clearTimeout(this.visualViewportResizeTimeout);
      this.visualViewportResizeTimeout = null;
    }

    if (this.scrollCheckTimeout) {
      clearTimeout(this.scrollCheckTimeout);
      this.scrollCheckTimeout = null;
    }

    if (this.facebookResizeTimeout) {
      clearTimeout(this.facebookResizeTimeout);
      this.facebookResizeTimeout = null;
    }

    // 停止主动高度检测
    this.stopActiveHeightDetection();

    // 恢复body事件
    this.restoreBodyEvent();

    this.listeners.clear();
    this.isInitialized = false;

    // 清理事件监听器
    if (this.boundEventHandlers.load) {
      window.removeEventListener("load", this.boundEventHandlers.load);
    }
    if (this.boundEventHandlers.resize) {
      window.removeEventListener("resize", this.boundEventHandlers.resize);
    }
    if (this.boundEventHandlers.orientationchange) {
      window.removeEventListener(
        "orientationchange",
        this.boundEventHandlers.orientationchange,
      );
    }
    if (this.boundEventHandlers.scroll) {
      window.removeEventListener("scroll", this.boundEventHandlers.scroll);
    }
    if (
      this.boundEventHandlers.visualViewportResize &&
      "visualViewport" in window
    ) {
      (window as any).visualViewport.removeEventListener(
        "resize",
        this.boundEventHandlers.visualViewportResize,
      );
    }
    if (this.boundEventHandlers.focus) {
      window.removeEventListener("focus", this.boundEventHandlers.focus);
    }
    if (this.boundEventHandlers.blur) {
      window.removeEventListener("blur", this.boundEventHandlers.blur);
    }
    if (this.boundEventHandlers.pageshow) {
      window.removeEventListener("pageshow", this.boundEventHandlers.pageshow);
    }
    if (this.boundEventHandlers.pagehide) {
      window.removeEventListener("pagehide", this.boundEventHandlers.pagehide);
    }
    if (this.boundEventHandlers.touchend) {
      document.removeEventListener(
        "touchend",
        this.boundEventHandlers.touchend,
      );
    }
    if (this.boundEventHandlers.touchstart) {
      document.removeEventListener(
        "touchstart",
        this.boundEventHandlers.touchstart,
      );
    }
    if (this.boundEventHandlers.touchmove) {
      document.removeEventListener(
        "touchmove",
        this.boundEventHandlers.touchmove,
      );
    }

    // 重置事件处理器引用
    this.boundEventHandlers = {};

    moduleLoggers.Compatibility.info("视口高度兼容性已清理");
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    return {
      isIOS,
      isHarmony,
      isFacebookBrowser,
      userAgent: navigator.userAgent,
      innerHeight: window.innerHeight,
      clientHeight: document.documentElement.clientHeight,
      screenHeight: window.screen.height,
      safeAreaTop: this.getSafeAreaInset("top"),
      safeAreaBottom: this.getSafeAreaInset("bottom"),
      visualViewport:
        "visualViewport" in window
          ? {
              height: (window as any).visualViewport?.height,
              width: (window as any).visualViewport?.width,
              scale: (window as any).visualViewport?.scale,
            }
          : null,
      playerMounted: this.playerMounted,
    };
  }
}

// 导出单例实例
export const viewportHeightCompatibility =
  ViewportHeightCompatibility.getInstance();

// 导出类型
export type { ViewportHeightCompatibility };

// 导出设备检测常量
export { isFacebookBrowser, isHarmony, isIOS };
