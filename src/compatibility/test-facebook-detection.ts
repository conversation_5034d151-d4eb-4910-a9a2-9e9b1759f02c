/**
 * @file test-facebook-detection.ts
 * @description 测试Facebook浏览器检测功能
 */

import { viewportHeightCompatibility } from "./ViewportHeightCompatibility";

// 测试Facebook浏览器检测
export function testFacebookDetection() {
  console.log("=== Facebook浏览器检测测试 ===");

  const deviceInfo = viewportHeightCompatibility.getDeviceInfo();

  console.log("设备信息:", {
    userAgent: deviceInfo.userAgent,
    isIOS: deviceInfo.isIOS,
    isFacebookBrowser: deviceInfo.isFacebookBrowser,
    isFacebookiOS: deviceInfo.isFacebookiOS,
    innerHeight: deviceInfo.innerHeight,
    visualViewport: deviceInfo.visualViewport,
  });

  // 模拟不同的User Agent进行测试
  const testUserAgents = [
    // Facebook iOS App
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 [FBAN/FBIOS;FBDV/iPhone13,2;FBMD/iPhone;FBSN/iOS;FBSV/15.0;FBSS/3;FBID/phone;FBLC/en_US;FBOP/5]",

    // Facebook Android App
    "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/88.0.4324.181 Mobile Safari/537.36 [FB_IAB/FB4A;FBAV/**********.69;]",

    // 普通Safari iOS
    "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1",

    // 普通Chrome Android
    "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Mobile Safari/537.36",
  ];

  testUserAgents.forEach((ua, index) => {
    const isFB = /FBAN|FBAV|FB_IAB|FB4A|FBIOS/.test(ua);
    const isIOSDevice = /iPad|iPhone|iPod/.test(ua);

    console.log(`测试 ${index + 1}:`, {
      userAgent: ua.substring(0, 50) + "...",
      isFacebookBrowser: isFB,
      isIOS: isIOSDevice,
      isFacebookiOS: isIOSDevice && isFB,
    });
  });
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== "undefined") {
  // 延迟执行，确保模块加载完成
  setTimeout(() => {
    testFacebookDetection();
  }, 1000);
}
