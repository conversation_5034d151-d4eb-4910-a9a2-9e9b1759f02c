/* 视口高度兼容性样式 */

:root {
  --csp-product-vh: 1vh;
}

/* 阻止body事件 */
.bs-body-blocked {
  touch-action: none;
  pointer-events: none;
}

.bs-body-blocked * {
  touch-action: auto;
  pointer-events: auto;
}

/* 主容器样式 */
#custom-solution-product-container {
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  box-sizing: border-box;
  background-color: #000;
}

/* React应用容器样式 */
.app {
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  background-color: #000;
  position: relative;
  overflow: hidden;
}

/* 多实例播放器容器 */
.multi-player-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 播放器实例 */
.player-instance {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.player-instance.active {
  z-index: 10;
}

.player-instance.hidden {
  z-index: 1;
  visibility: hidden;
  pointer-events: none;
}

/* 播放器容器 */
.player-container {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
}

.player-container.fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  z-index: 9999;
}

/* 播放器包装器 */
.player-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #000;
}

/* 加载覆盖层 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: #fff;
  padding: 2rem;
}

/* 安全区域适配 - 使用CSS环境变量 */
@supports (padding: max(0px)) {
  /* 主容器使用安全区域 */
  #custom-solution-product-container,
  .app,
  .player-container.fullscreen {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }
}
