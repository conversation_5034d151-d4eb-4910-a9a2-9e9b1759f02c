import { moduleLoggers } from "@/utils/LogManager";

/**
 * @file CurrentVideoPlayerCompatibility.ts
 * @description 兼容层 - 实现 window.currentVideoPlayer 对象
 * 这是一个临时的兼容层，用于支持现有的 window.currentVideoPlayer API
 * 后续可以移除此文件
 */
import { StateManager } from "../core/managers/StateManager";
import { viewportHeightCompatibility } from "./ViewportHeightCompatibility";

/**
 * 获取当前剧集ID
 * @returns 当前剧集的ID字符串
 */
function getCurrentEpisodeId(): string {
  const stateManager = StateManager.getInstance();
  const activeInstance = stateManager.getActiveInstance();

  if (!activeInstance?.currentEpisode?.id) {
    moduleLoggers.Compatibility.warn("无法获取当前剧集ID，返回空字符串");
    return "";
  }

  moduleLoggers.Compatibility.info(
    `获取当前剧集ID: ${activeInstance.currentEpisode.id}`,
  );
  return activeInstance.currentEpisode.id;
}

/**
 * 获取当前视频ID
 * @returns 当前视频的ID字符串
 */
function getCurrentVideoId(): string {
  const stateManager = StateManager.getInstance();
  const activeInstance = stateManager.getActiveInstance();

  if (!activeInstance?.videoData?.id) {
    moduleLoggers.Compatibility.warn("无法获取当前视频ID，返回空字符串");
    return "";
  }

  moduleLoggers.Compatibility.info(
    `获取当前视频ID: ${activeInstance.videoData.id}`,
  );
  return activeInstance.videoData.id;
}

/**
 * 获取当前剧集信息
 * @returns 包含剧集编号的对象
 */
function getCurrentEpisode(): { no: number } {
  const stateManager = StateManager.getInstance();
  const activeInstance = stateManager.getActiveInstance();

  if (!activeInstance?.currentEpisode?.no) {
    moduleLoggers.Compatibility.warn(
      "无法获取当前剧集编号，返回默认值 { no: 0 }",
    );
    return { no: 0 };
  }

  moduleLoggers.Compatibility.info(
    `获取当前剧集编号: ${activeInstance.currentEpisode.no}`,
  );
  return { no: activeInstance.currentEpisode.no };
}

/**
 * 暂停播放当前视频
 */
function pause() {
  const stateManager = StateManager.getInstance();
  const activeInstance = stateManager.getActiveInstance();

  if (!activeInstance) {
    moduleLoggers.Compatibility.warn("无法暂停播放，当前没有活跃的播放器实例");
    return;
  }

  const player = stateManager.getPlayer(activeInstance.id);
  if (!player) {
    moduleLoggers.Compatibility.warn("无法暂停播放，活跃的播放器实例不存在");
    return;
  }

  if (!player.xgplayer) {
    moduleLoggers.Compatibility.warn("无法暂停播放，播放器实例未初始化");
    return;
  }

  player.pause();
}

/**
 * 播放当前视频
 */
function play() {
  const stateManager = StateManager.getInstance();
  const activeInstance = stateManager.getActiveInstance();

  if (!activeInstance) {
    moduleLoggers.Compatibility.warn("无法播放，当前没有活跃的播放器实例");
    return;
  }

  const player = stateManager.getPlayer(activeInstance.id);
  if (!player) {
    moduleLoggers.Compatibility.warn("无法播放，活跃的播放器实例不存在");
    return;
  }

  if (!player.xgplayer) {
    moduleLoggers.Compatibility.warn("无法播放，播放器实例未初始化");
    return;
  }

  player.play();
}

/**
 * 初始化兼容层
 * 将 currentVideoPlayer 对象挂载到 window 上
 */
export function initCurrentVideoPlayerCompatibility(): void {
  moduleLoggers.Compatibility.info("初始化 currentVideoPlayer 兼容层");

  // 检查是否已经存在
  if (window.currentVideoPlayer) {
    moduleLoggers.Compatibility.warn(
      "window.currentVideoPlayer 已存在，跳过初始化",
    );
    return;
  }

  // 创建兼容层对象
  const currentVideoPlayer = {
    getCurrentEpisodeId,
    getCurrentVideoId,
    getCurrentEpisode,
    toggleGestureDisabled,
    pause,
    play,
  };

  // 挂载到 window 对象
  window.currentVideoPlayer = currentVideoPlayer;

  moduleLoggers.Compatibility.info("currentVideoPlayer 兼容层初始化完成");
}

/**
 * 清理兼容层
 * 从 window 对象上移除 currentVideoPlayer
 */
function cleanupCurrentVideoPlayerCompatibility(): void {
  moduleLoggers.Compatibility.info("清理 currentVideoPlayer 兼容层");

  if (window.currentVideoPlayer) {
    delete (window as any).currentVideoPlayer;
    moduleLoggers.Compatibility.info("currentVideoPlayer 兼容层已清理");
  } else {
    moduleLoggers.Compatibility.info(
      "currentVideoPlayer 兼容层不存在，无需清理",
    );
  }
}

function cleanupViewportHeightCompatibility(): void {
  moduleLoggers.Compatibility.info("清理 viewportHeight 兼容层");
  viewportHeightCompatibility.cleanup();
}

export function cleanupCompatibility(): void {
  cleanupCurrentVideoPlayerCompatibility();
  cleanupViewportHeightCompatibility();
}

/**
 * 检查兼容层是否已初始化
 * @returns 是否已初始化
 */
export function isCurrentVideoPlayerCompatibilityInitialized(): boolean {
  return !!window.currentVideoPlayer;
}

/**
 * 切换手势禁用状态
 * @param disabled 是否禁用手势
 */
export function toggleGestureDisabled(disabled: boolean): boolean {
  const oldDisabled = window.__BS_IS_GESTURE_DISABLED__ || false;
  window.__BS_IS_GESTURE_DISABLED__ = disabled;
  return oldDisabled;
}
