/**
 * @file compatibility/index.ts
 * @description 兼容层模块导出
 * 包含所有兼容层相关的功能
 */

import { cleanupCompatibility } from "./CurrentVideoPlayerCompatibility";

export {
  cleanupCompatibility,
  initCurrentVideoPlayerCompatibility,
  isCurrentVideoPlayerCompatibilityInitialized,
  toggleGestureDisabled,
} from "./CurrentVideoPlayerCompatibility";
export type { ViewportHeightCompatibility } from "./ViewportHeightCompatibility";
export {
  isFacebookBrowser,
  isHarmony,
  isIOS,
  viewportHeightCompatibility,
} from "./ViewportHeightCompatibility";

// 将清理函数挂载到 window 上，方便重置时使用
if (typeof window !== "undefined") {
  window.__BS_CLEANUP_COMPATIBILITY__ = cleanupCompatibility;
}
