/**
 * @file CurrentVideoPlayerCompatibility.test.ts
 * @description 兼容层测试文件
 * 验证 window.currentVideoPlayer 的所有函数都能正确工作
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

// 指定 jsdom 环境
// @vitest-environment jsdom
import { StateManager } from "../core/managers/StateManager";
import {
  cleanupCurrentVideoPlayerCompatibility,
  initCurrentVideoPlayerCompatibility,
  isCurrentVideoPlayerCompatibilityInitialized,
  toggleGestureDisabled,
} from "./CurrentVideoPlayerCompatibility";

// Mock StateManager
vi.mock("../core/managers/StateManager");

describe("CurrentVideoPlayerCompatibility", () => {
  let mockStateManager: any;

  beforeEach(() => {
    // 清理之前的兼容层
    cleanupCurrentVideoPlayerCompatibility();

    // 重置 mock
    vi.clearAllMocks();

    // 创建 mock StateManager
    mockStateManager = {
      getInstance: vi.fn(),
      getActiveInstance: vi.fn(),
    };

    vi.mocked(StateManager.getInstance).mockReturnValue(mockStateManager);
  });

  afterEach(() => {
    cleanupCurrentVideoPlayerCompatibility();
  });

  describe("初始化", () => {
    it("应该正确初始化兼容层", () => {
      initCurrentVideoPlayerCompatibility();

      expect(window.currentVideoPlayer).toBeDefined();
      expect(typeof window.currentVideoPlayer.getCurrentEpisodeId).toBe(
        "function",
      );
      expect(typeof window.currentVideoPlayer.getCurrentVideoId).toBe(
        "function",
      );
      expect(typeof window.currentVideoPlayer.getCurrentEpisode).toBe(
        "function",
      );
    });

    it("应该避免重复初始化", () => {
      initCurrentVideoPlayerCompatibility();
      const firstInstance = window.currentVideoPlayer;

      initCurrentVideoPlayerCompatibility();
      const secondInstance = window.currentVideoPlayer;

      expect(firstInstance).toBe(secondInstance);
    });

    it("应该正确检查初始化状态", () => {
      expect(isCurrentVideoPlayerCompatibilityInitialized()).toBe(false);

      initCurrentVideoPlayerCompatibility();

      expect(isCurrentVideoPlayerCompatibilityInitialized()).toBe(true);
    });
  });

  describe("getCurrentEpisodeId", () => {
    beforeEach(() => {
      initCurrentVideoPlayerCompatibility();
    });

    it("应该返回当前剧集ID", () => {
      const mockActiveInstance = {
        currentEpisode: { id: "episode-123" },
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentEpisodeId();

      expect(result).toBe("episode-123");
    });

    it("当没有活跃实例时应该返回空字符串", () => {
      mockStateManager.getActiveInstance.mockReturnValue(null);

      const result = window.currentVideoPlayer.getCurrentEpisodeId();

      expect(result).toBe("");
    });

    it("当没有当前剧集时应该返回空字符串", () => {
      const mockActiveInstance = {
        currentEpisode: null,
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentEpisodeId();

      expect(result).toBe("");
    });
  });

  describe("getCurrentVideoId", () => {
    beforeEach(() => {
      initCurrentVideoPlayerCompatibility();
    });

    it("应该返回当前视频ID", () => {
      const mockActiveInstance = {
        videoData: { id: "video-456" },
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentVideoId();

      expect(result).toBe("video-456");
    });

    it("当没有活跃实例时应该返回空字符串", () => {
      mockStateManager.getActiveInstance.mockReturnValue(null);

      const result = window.currentVideoPlayer.getCurrentVideoId();

      expect(result).toBe("");
    });

    it("当没有视频数据时应该返回空字符串", () => {
      const mockActiveInstance = {
        videoData: null,
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentVideoId();

      expect(result).toBe("");
    });
  });

  describe("getCurrentEpisode", () => {
    beforeEach(() => {
      initCurrentVideoPlayerCompatibility();
    });

    it("应该返回当前剧集信息", () => {
      const mockActiveInstance = {
        currentEpisode: { no: 5 },
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentEpisode();

      expect(result).toEqual({ no: 5 });
    });

    it("当没有活跃实例时应该返回默认值", () => {
      mockStateManager.getActiveInstance.mockReturnValue(null);

      const result = window.currentVideoPlayer.getCurrentEpisode();

      expect(result).toEqual({ no: 0 });
    });

    it("当没有当前剧集时应该返回默认值", () => {
      const mockActiveInstance = {
        currentEpisode: null,
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentEpisode();

      expect(result).toEqual({ no: 0 });
    });

    it("当剧集没有编号时应该返回默认值", () => {
      const mockActiveInstance = {
        currentEpisode: { id: "episode-123" }, // 没有 no 字段
      };
      mockStateManager.getActiveInstance.mockReturnValue(mockActiveInstance);

      const result = window.currentVideoPlayer.getCurrentEpisode();

      expect(result).toEqual({ no: 0 });
    });
  });

  describe("toggleGestureDisabled", () => {
    it("应该正确切换手势禁用状态", () => {
      toggleGestureDisabled(true);
      expect(window.__BS_IS_GESTURE_DISABLED__).toBe(true);
      toggleGestureDisabled(false);
      expect(window.__BS_IS_GESTURE_DISABLED__).toBe(false);
    });
  });

  describe("清理", () => {
    it("应该正确清理兼容层", () => {
      initCurrentVideoPlayerCompatibility();
      expect(window.currentVideoPlayer).toBeDefined();

      cleanupCurrentVideoPlayerCompatibility();
      expect(window.currentVideoPlayer).toBeUndefined();
    });

    it("重复清理应该不会报错", () => {
      cleanupCurrentVideoPlayerCompatibility();
      expect(() => cleanupCurrentVideoPlayerCompatibility()).not.toThrow();
    });
  });
});
