export interface IFlashsaleInfo {
  variant_id: string;
  product_id: string;
  quantity: number;
  discount_id: string;
  limit_time: number;
  limit_buy: number;
  user_limit_buy: number;
  discount_sales: number;
  discount_sales_rate: string;
  discount_stock: number;
  ends_at: number;
  starts_at: number;
  allow_oversold: string;
  allocation_method: string;
  price: string;
  compare_at_price: string;
  discount_price: string;
  customary_saved_price: string;
  customary_off_ratio: string;
  discount_saved_price: string;
  discount_off_ratio: string;
  use_before_price: boolean;
  before_price: string;
  title: string;
  properties: string;
  color_setting_promotional_copy: string;
  discount_quantity: number;
  is_need_split: boolean;
}

interface IImage {
  alt: string;
  aspect_ratio: number;
  height: number;
  path: string;
  src: string;
  width: number;
}

interface IVariant {
  id: string;
  image: IImage;
  inventory_policy: string;
  inventory_quantity: number;
  inventory_tracking: boolean;
  price: string;
  price_max: string;
  price_min: string;
  price_varies: boolean;
  product_type: string;
  published: boolean;
  published_at: string;
  redirect: boolean;
  redirect_url: string;
  requires_shipping: boolean;
  retail_price: string;
  retail_price_max: string;
  retail_price_min: string;
  sales: number;
  spu: string;
  tags: string;
  taxable: boolean;
  title: string;
  updated_at: string;
  url: string;
  variants: IVariant[];
  video_url: string;
  view_count: number;
  weight: number;
  weight_unit: string;
}

interface IOption {
  id: string;
  name: string;
  position: number;
  product_id: string;
  values: string[];
}

interface IProduct {
  available: boolean;
  bind_collection_ids: string[];
  compare_at_price: string;
  compare_at_price_max: string;
  compare_at_price_min: string;
  created_at: string;
  display_fake_sales: boolean;
  fake_sales: number;
  flashsale_info: IFlashsaleInfo[];
  handle: string;
  has_only_default_variant: boolean;
  id: string;
  image: IImage;
  images: IImage[];
  independent_seo: boolean;
  inventory_policy: string;
  inventory_quantity: number;
  inventory_tracking: boolean;
  mixed_wholesale: boolean;
  need_variant_image: boolean;
  note: string;
  off_ratio: string;
  options: IOption[];
  price: string;
  price_max: string;
  price_min: string;
  price_varies: boolean;
  product_type: string;
  published: boolean;
  published_at: string;
  redirect: boolean;
  redirect_url: string;
  requires_shipping: boolean;
  retail_price: string;
  retail_price_max: string;
  retail_price_min: string;
  sales: number;
  spu: string;
  tags: string;
  taxable: boolean;
  title: string;
  updated_at: string;
  url: string;
  variants: IVariant[];
  video_url: string;
  view_count: number;
  vendor: string;
  vendor_url: string;
  weight: number;
  weight_unit: string;
}
