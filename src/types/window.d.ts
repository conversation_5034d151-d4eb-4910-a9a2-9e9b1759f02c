// 管理器类的类型定义
interface VideoPlayerManagerClass {
  resetGlobalInstance(): void;
  getInstance(): VideoPlayerManager;
}

interface PreloadManagerClass {
  resetGlobalInstance(): void;
  getInstance(): PreloadManager;
}

interface StateManagerClass {
  resetGlobalInstance(): void;
  getInstance(): StateManager;
}

interface EventManagerClass {
  resetGlobalInstance(): void;
  getInstance(): EventManager;
}

interface StatisticsManagerClass {
  resetGlobalInstance(): void;
  getInstance(): StatisticsManager;
}

interface PlayerFactoryClass {
  resetGlobalInstance(): void;
  getInstance(
    eventManager?: EventManager,
    stateManager?: StateManager,
  ): PlayerFactory;
}

interface TrackingManagerClass {
  resetGlobalInstance(): void;
  getInstance(): TrackingManager;
}

interface InstanceLifecycleManagerClass {
  resetGlobalInstance(): void;
  getInstance(
    playerFactory?: PlayerFactory,
    stateManager?: StateManager,
    eventManager?: EventManager,
  ): InstanceLifecycleManager;
}

// 管理器实例的类型定义
interface VideoPlayerManager {
  reset(): void;
  destroyAllInstances(): void;
  initData(productId: string): Promise<any>;
  initPlayer(episodeIndex: number, id: string): void;
  switchEpisode(episodeIndex: number): void;
  getInstanceStats(): any;
  cleanupExpiredInstances(): void;
  statisticsManager: StatisticsManager;
  eventManager: EventManager;
  stateManager: StateManager;
}

interface PreloadManager {
  destroy(): void;
  preloadEpisode(url: string, videoId: string, episodeNo: number): void;
  getPreloadStatus(): any;
  reset(): void;
}

interface StateManager {
  reset(): void;
  createInstance(id: string, containerId: string, videoData?: any): any;
  removeInstance(id: string): void;
  getInstance(id: string): any;
  getAllInstances(): any[];
  setActiveInstance(id: string): void;
  getActiveInstance(): any;
  getGlobalState(): any;
}

interface EventManager {
  clearAll(): void;
  on(type: string, handler: Function): void;
  off(type: string, handler: Function): void;
  emit(type: string, event: any): void;
  reset(): void;
}

interface StatisticsManager {
  reset(): void;
  refreshStatistics(videoId: string, episodeId: string): Promise<any>;
  updateFavorite(
    videoId: string,
    episodeId: string,
    isLike: boolean,
  ): Promise<void>;
  updateCollect(
    videoId: string,
    episodeId: string,
    isCollect: boolean,
  ): Promise<void>;
  getLastPlayedFromStorage(videoId: string): any;
  initializeProgressTracker(
    eventManager: EventManager,
    stateManager: StateManager,
  ): void;
}

interface TrackingManager {
  reset(): void;
  trackPlay(product: any, episode: any): void;
  trackFinishPlay(product: any, episode: any): void;
  trackDuration(product: any, episode: any, data: any): void;
  resetAllThirtySecondPlayTracking(): void;
}

interface Window {
  // 外置变量
  __BS_PLAYER_ID__: string;
  __PRODUCT_ID__: string;
  SPZ: {
    whenApiDefined: (element: HTMLElement | null) => Promise<any>;
  };
  C_SETTINGS: {
    routes: {
      root: string;
    };
  };
  csTracker: {
    track: (
      event: string,
      params: Record<string, any>,
      target?: string[],
    ) => void;
    getSkitUid: () => string;
    getClientId: () => string;
  };
  BSPlayerContainer: any;
  // 内置变量
  __BS_IS_ADJUSTING_VOLUME__?: boolean;
  __BS_IS_GESTURE_DISABLED__?: boolean;
  mountBSPlayer: (productId: string, id: string) => void;
  unmountBSPlayer: (id?: string) => void;
  deepCleanupBSPlayer: () => void;
  currentVideoPlayer: {
    getCurrentEpisodeId: () => string;
    getCurrentVideoId: () => string;
    getCurrentEpisode: () => {
      no: number;
    };
    toggleGestureDisabled: (disabled: boolean) => boolean;
    pause: () => void;
    play: () => void;
  };
  __BS_VIDEO_PLAYER_MANAGER_INSTANCE__?: VideoPlayerManager;
  __BS_VIDEO_PLAYER_MANAGER_CLASS__?: VideoPlayerManagerClass;
  // 各个管理器的类，用于重置
  __BS_PRELOAD_MANAGER_CLASS__?: PreloadManagerClass;
  __BS_STATE_MANAGER_CLASS__?: StateManagerClass;
  __BS_EVENT_MANAGER_CLASS__?: EventManagerClass;
  __BS_STATISTICS_MANAGER_CLASS__?: StatisticsManagerClass;
  __BS_TRACKING_MANAGER_CLASS__?: TrackingManagerClass;
  __BS_INSTANCE_LIFECYCLE_MANAGER_CLASS__?: InstanceLifecycleManagerClass;
  __BS_PLAYER_FACTORY_CLASS__?: PlayerFactoryClass;
  // 兼容层清理函数
  __BS_CLEANUP_COMPATIBILITY__?: () => void;
  // 播放器挂载状态
  __BS_PLAYER_MOUNTED__?: {
    productId: string;
    containerId: string;
    mountedAt: number;
    status: "mounted" | "unmounted";
  };
  // TODO: 迁移所有内置变量到 window.BSPlayer 下
  BSPlayer: any;
}

// Web Components 类型定义
declare namespace JSX {
  interface IntrinsicElements {
    "spz-loading": {
      layout?: string;
      [key: string]: any;
    };
  }
}
