import React, {
  createContext,
  ReactNode,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

import { GestureManager } from "../core/managers/GestureManager";
import { PlayerInstanceState } from "../core/managers/StateManager";
import { Statistics } from "../core/managers/StatisticsManager";
import { useEpisodeManager } from "../hooks/useEpisodeManager";
import { updateUrlVariantId } from "../utils/urlParams";
import { useCore } from "./CoreProvider";

interface MultiPlayerContextType {
  // 实例管理
  createPlayer: (
    id: string,
    containerId: string,
    videoData?: IPlayerVideoData,
    autoPlay?: boolean,
    startTime?: number,
    currentEpisode?: IPlayerEpisode,
  ) => void;
  destroyPlayer: (id: string) => void;
  getPlayer: (id: string) => any; // PlayerInstance类型，这里用any避免循环依赖
  getPlayerState: (id: string) => PlayerInstanceState | undefined;
  getAllPlayers: () => PlayerInstanceState[];

  // 活跃播放器管理
  activePlayerId: string | null;
  setActivePlayer: (id: string) => void;
  getActivePlayer: () => PlayerInstanceState | null;

  // 共享选集抽屉管理
  drawerState: {
    isOpen: boolean;
    currentPlayerId: string | null;
    playerData: IPlayerVideoData | null;
  };
  openEpisodeDrawer: () => void;
  closeEpisodeDrawer: () => void;
  onEpisodeSelect: (episodeNo: number) => void;
  switchToPlayer: (videoData: IPlayerVideoData, episodeNo?: number) => void;

  // 集数切换管理
  switchEpisode: (episodeNo: number) => void;
  switchToNextEpisode: () => void;

  // 全局状态
  isLoading: boolean;
  totalPlayers: number;

  // 统计数据相关
  statistics: Statistics | null;
  refreshStatistics: () => Promise<void>;
  updateFavorite: (
    videoId: string,
    episodeId: string,
    isLike: boolean,
  ) => Promise<void>;
  updateCollect: (
    videoId: string,
    episodeId: string,
    isCollect: boolean,
  ) => Promise<void>;

  // 全局播放设置管理
  globalPlaybackSettings: {
    playbackRate: number;
    volume: number;
    muted: boolean;
  };
  setGlobalPlaybackRate: (rate: number) => void;
  setGlobalVolume: (volume: number) => void;
  setGlobalMuted: (muted: boolean) => void;

  // 播放结束处理
  handlePlayerEnded: (playerId: string, currentEpisodeNo: number) => void;
}

const MultiPlayerContext = createContext<MultiPlayerContextType | undefined>(
  undefined,
);

export const useMultiPlayer = () => {
  const context = useContext(MultiPlayerContext);
  if (context === undefined) {
    throw new Error("useMultiPlayer must be used within a MultiPlayerProvider");
  }
  return context;
};

interface MultiPlayerProviderProps {
  children: ReactNode;
  videoData?: IPlayerVideoData; // 可选的视频数据，从外部传入
}

export const MultiPlayerProvider: React.FC<MultiPlayerProviderProps> = ({
  children,
  videoData: externalVideoData,
}) => {
  const {
    playerFactory,
    stateManager,
    statisticsManager,
    eventManager, // 从 useCore 获取 eventManager
    videoPlayerManager, // 添加 videoPlayerManager
  } = useCore();

  // 全局状态
  const [isLoading, setIsLoading] = useState(false);
  const [activePlayerId, setActivePlayerIdState] = useState<string | null>(
    null,
  );

  // 选集抽屉状态
  const [drawerState, setDrawerState] = useState<{
    isOpen: boolean;
    currentPlayerId: string | null;
    playerData: IPlayerVideoData | null;
  }>({
    isOpen: false,
    currentPlayerId: null,
    playerData: null,
  });

  // 创建全局的 onEnded 处理函数
  const handlePlayerEnded = useCallback(
    (playerId: string, currentEpisodeNo: number) => {
      const activePlayer = stateManager.getActiveInstance();
      if (!activePlayer?.videoData) return;

      const nextEpisodeNo = currentEpisodeNo + 1;
      if (nextEpisodeNo <= activePlayer.videoData.series.length) {
        moduleLoggers.MultiPlayer.info(
          `播放结束，自动切换到下一集: 第${nextEpisodeNo}集`,
        );
        // 直接切换，不需要定时器
        videoPlayerManager.instanceLifecycleManager.switchEpisode(
          nextEpisodeNo,
          handlePlayerEnded, // 传递 onEnded 回调
        );
      }
    },
    [stateManager, videoPlayerManager],
  );

  const { switchEpisode, switchToNextEpisode, handlePlayerEndedWrapper } =
    useEpisodeManager({
      playerFactory,
      stateManager,
      activePlayerId,
      instanceLifecycleManager: videoPlayerManager.instanceLifecycleManager,
      handlePlayerEnded, // 传递全局的 handlePlayerEnded 函数
    });

  // 手势管理器
  const gestureManagerRef = useRef<GestureManager | null>(null);

  // 获取所有播放器状态
  const allPlayers = useMemo(() => {
    return stateManager.getAllInstances();
  }, [stateManager]);

  // 添加统计数据状态和缓存
  const [statistics, setStatistics] = useState<Statistics | null>(null);

  // 全局播放设置状态
  const [globalPlaybackSettings, setGlobalPlaybackSettings] = useState({
    playbackRate: 1.25,
    volume: 1,
    muted: false,
  });

  // 初始化手势管理器
  useEffect(() => {
    // 确保只初始化一次
    if (!gestureManagerRef.current) {
      gestureManagerRef.current = new GestureManager({
        minSwipeDistance: 50,
        episodeChangeCooldown: 1000,
        swipeCooldown: 500,
      });
    }

    // 设置剧集切换回调
    gestureManagerRef.current.init(
      () => {
        // 切换到下一集
        const activePlayer = stateManager.getActiveInstance();
        if (activePlayer?.videoData && activePlayer.currentEpisode) {
          const nextEpisodeNo = activePlayer.currentEpisode.no + 1;
          if (nextEpisodeNo <= activePlayer.videoData.series.length) {
            moduleLoggers.MultiPlayer.info(
              `🎯 [GestureManager] 手势触发切换到下一集: ${nextEpisodeNo}`,
            );
            switchEpisode(nextEpisodeNo);
          }
        }
      },
      () => {
        // 切换到上一集
        const activePlayer = stateManager.getActiveInstance();
        if (activePlayer?.videoData && activePlayer.currentEpisode) {
          const prevEpisodeNo = activePlayer.currentEpisode.no - 1;
          if (prevEpisodeNo >= 1) {
            moduleLoggers.MultiPlayer.info(
              `🎯 [GestureManager] 手势触发切换到上一集: ${prevEpisodeNo}`,
            );
            switchEpisode(prevEpisodeNo);
          }
        }
      },
    );

    return () => {
      if (gestureManagerRef.current) {
        gestureManagerRef.current.destroy();
        gestureManagerRef.current = null;
      }
    };
  }, []); // 移除依赖项，确保只初始化一次

  // 刷新统计数据
  const refreshStatistics = useCallback(async () => {
    const activePlayer = stateManager.getActiveInstance();
    if (!activePlayer?.videoData || !activePlayer.currentEpisode) return;

    const videoId = activePlayer.videoData.id;
    const episodeId = activePlayer.currentEpisode.id;

    try {
      const newStats = await statisticsManager.refreshStatistics(
        videoId,
        episodeId,
      );
      setStatistics(newStats);
    } catch (error) {
      moduleLoggers.MultiPlayer.error("Failed to fetch statistics:", error);
      setStatistics(null);
    }
  }, [stateManager, statisticsManager]);

  // 更新点赞状态
  const updateFavorite = useCallback(
    async (videoId: string, episodeId: string, isLike: boolean) => {
      const activePlayer = stateManager.getActiveInstance();
      if (!activePlayer?.videoData) return;

      // 乐观更新
      const oldStats = statistics;
      const newStats = {
        ...(statistics || { like: 0, collect: 0 }),
        like: (statistics?.like || 0) + (isLike ? 1 : -1),
      };
      setStatistics(newStats);
      statisticsManager.updateStatisticsCache(videoId, newStats);

      // 更新当前剧集的点赞状态
      const episode = activePlayer.videoData.series.find(
        (e) => e.id === episodeId,
      );
      if (episode) {
        episode.operate.liked = isLike;
      }

      try {
        await statisticsManager.updateFavorite(videoId, episodeId, isLike);
      } catch (error) {
        moduleLoggers.MultiPlayer.error(
          "Failed to update favorite status:",
          error,
        );
        // 回滚
        setStatistics(oldStats);
        if (oldStats) {
          statisticsManager.updateStatisticsCache(videoId, oldStats);
        }
        if (episode) {
          episode.operate.liked = !isLike;
        }
      }
    },
    [stateManager, statistics, statisticsManager],
  );

  // 更新收藏状态
  const updateCollect = useCallback(
    async (videoId: string, episodeId: string, isCollect: boolean) => {
      const activePlayer = stateManager.getActiveInstance();
      if (!activePlayer?.videoData) return;

      // 乐观更新
      const oldStats = statistics;
      const newStats = {
        ...(statistics || { like: 0, collect: 0 }),
        collect: (statistics?.collect || 0) + (isCollect ? 1 : -1),
      };
      setStatistics(newStats);
      statisticsManager.updateStatisticsCache(videoId, newStats);

      // 更新所有剧集的收藏状态
      const oldCollectedStates = activePlayer.videoData.series.map(
        (e) => e.operate.collected,
      );
      activePlayer.videoData.series.forEach((e) => {
        e.operate.collected = isCollect;
      });

      try {
        await statisticsManager.updateCollect(videoId, episodeId, isCollect);
      } catch (error) {
        moduleLoggers.MultiPlayer.error(
          "Failed to update collect status:",
          error,
        );
        // 回滚
        setStatistics(oldStats);
        activePlayer.videoData.series.forEach((e, index) => {
          e.operate.collected = oldCollectedStates[index];
        });
        if (oldStats) {
          statisticsManager.updateStatisticsCache(videoId, oldStats);
        }
      }
    },
    [stateManager, statistics, statisticsManager],
  );

  // 切换剧集时刷新统计信息并同步状态
  useEffect(() => {
    const activePlayer = stateManager.getActiveInstance();
    if (activePlayer?.videoData && activePlayer.currentEpisode) {
      const videoId = activePlayer.videoData.id;

      // 总是获取最新统计数据
      refreshStatistics();

      // 同时，先从缓存加载数据以优化UI体验
      const cachedStats = statisticsManager.getStatisticsFromCache(videoId);
      if (cachedStats) {
        setStatistics(cachedStats);
        // 同步更新视频数据状态
        if (activePlayer.videoData.operate) {
          activePlayer.videoData.operate.liked = cachedStats.like > 0;
          activePlayer.videoData.operate.collected = cachedStats.collect > 0;
        }
      } else {
        // 如果没有缓存，则重置状态
        setStatistics(null);
        if (activePlayer.videoData.operate) {
          activePlayer.videoData.operate.liked = false;
          activePlayer.videoData.operate.collected = false;
        }
      }
    }
  }, [activePlayerId, stateManager, refreshStatistics, statisticsManager]);

  // 同步外部传入的视频数据到状态管理器
  useEffect(() => {
    if (externalVideoData) {
      moduleLoggers.MultiPlayer.info("接收到外部视频数据，并更新状态");
      stateManager.setVideoDetail(externalVideoData);
    }
  }, [externalVideoData]);

  // 创建播放器实例
  const createPlayer = useCallback(
    (
      id: string,
      containerId: string,
      videoData?: IPlayerVideoData,
      autoPlay = false,
      startTime = 0,
      currentEpisode?: IPlayerEpisode,
    ) => {
      if (!externalVideoData) {
        moduleLoggers.MultiPlayer.warn("缺少全局视频数据，无法创建播放器");
        return;
      }

      moduleLoggers.MultiPlayer.info(
        `尝试创建播放器实例: ${id}, startTime: ${startTime}`,
      );
      try {
        // 使用 InstanceLifecycleManager 创建播放器实例，它会自动处理活跃状态设置和预加载
        const instance =
          videoPlayerManager.instanceLifecycleManager.createPlayerInstance(
            id,
            containerId,
            videoData!,
            autoPlay,
            startTime,
            currentEpisode,
            handlePlayerEnded,
          );

        // 如果没有活跃播放器，则更新本地状态
        if (!activePlayerId) {
          setActivePlayerIdState(id);
        }

        moduleLoggers.MultiPlayer.info(
          `播放器实例创建成功: ${id}, startTime: ${startTime}`,
          instance,
        );
      } catch (error) {
        moduleLoggers.MultiPlayer.error(`创建播放器实例失败: ${id}`, error);
      }
    },
    [externalVideoData, playerFactory, activePlayerId, handlePlayerEnded],
  );

  // 销毁播放器实例
  const destroyPlayer = useCallback(
    (id: string) => {
      playerFactory.destroyPlayer(id);
    },
    [playerFactory],
  );

  // 获取播放器实例
  const getPlayer = useCallback(
    (id: string) => {
      return playerFactory.getPlayer(id);
    },
    [playerFactory],
  );

  // 获取播放器状态
  const getPlayerState = useCallback(
    (id: string) => {
      return stateManager.getInstance(id);
    },
    [stateManager],
  );

  // 获取所有播放器
  const getAllPlayersCallback = useCallback(() => {
    return allPlayers;
  }, [allPlayers]);

  // 监听全局状态变化
  useEffect(() => {
    const unsubscribe = stateManager.subscribeToGlobalState((globalState) => {
      setActivePlayerIdState(globalState.activeInstanceId);
    });

    return unsubscribe;
  }, [stateManager]);

  // 监听全局播放设置变化
  useEffect(() => {
    const globalSettings = stateManager.getGlobalPlaybackSettings();
    setGlobalPlaybackSettings(globalSettings);
  }, [stateManager]);

  // 监听全局播放设置事件
  useEffect(() => {
    const handleGlobalPlaybackSettingsChange = (event: any) => {
      if (event.playbackRate !== undefined) {
        setGlobalPlaybackSettings((prev) => ({
          ...prev,
          playbackRate: event.playbackRate,
        }));
      }
      if (event.volume !== undefined) {
        setGlobalPlaybackSettings((prev) => ({
          ...prev,
          volume: event.volume,
        }));
      }
      if (event.muted !== undefined) {
        setGlobalPlaybackSettings((prev) => ({ ...prev, muted: event.muted }));
      }
    };

    eventManager.on(
      "globalPlaybackSettingsChange",
      handleGlobalPlaybackSettingsChange,
    );

    return () => {
      eventManager.off(
        "globalPlaybackSettingsChange",
        handleGlobalPlaybackSettingsChange,
      );
    };
  }, [eventManager]);

  // 全局播放设置管理
  const setGlobalPlaybackRate = useCallback(
    (rate: number) => {
      // moduleLoggers.MultiPlayer.info(`设置全局播放速度: ${rate}x`);

      // 更新状态管理器
      stateManager.setGlobalPlaybackRate(rate);

      // 更新本地状态
      setGlobalPlaybackSettings((prev) => ({ ...prev, playbackRate: rate }));

      // 同步到所有播放器实例
      const allPlayers = playerFactory.getAllPlayers();
      allPlayers.forEach((player) => {
        if (player.xgplayer) {
          try {
            player.xgplayer.playbackRate = rate;
            // moduleLoggers.MultiPlayer.info(
            //   `同步播放速度到播放器: ${player.instanceId}`,
            // );
          } catch (error) {
            moduleLoggers.MultiPlayer.warn(
              `同步播放速度失败: ${player.instanceId}`,
              error,
            );
          }
        }
      });

      // 发送事件
      eventManager.emit("globalPlaybackSettingsChange", { playbackRate: rate });
    },
    [stateManager, playerFactory, eventManager],
  );

  const setGlobalVolume = useCallback(
    (volume: number) => {
      const clampedVolume = Math.max(0, Math.min(1, volume));
      // moduleLoggers.MultiPlayer.info(`设置全局音量: ${clampedVolume}`);

      // 更新状态管理器
      stateManager.setGlobalVolume(clampedVolume);

      // 更新本地状态
      setGlobalPlaybackSettings((prev) => ({ ...prev, volume: clampedVolume }));

      // 同步到所有播放器实例
      const allPlayers = playerFactory.getAllPlayers();
      allPlayers.forEach((player) => {
        if (player.xgplayer) {
          try {
            player.xgplayer.volume = clampedVolume;
            // moduleLoggers.MultiPlayer.info(
            //   `同步音量到播放器: ${player.instanceId}`,
            // );
          } catch (error) {
            moduleLoggers.MultiPlayer.warn(
              `同步音量失败: ${player.instanceId}`,
              error,
            );
          }
        }
      });

      // 发送事件
      eventManager.emit("globalPlaybackSettingsChange", {
        volume: clampedVolume,
      });
    },
    [stateManager, playerFactory, eventManager],
  );

  const setGlobalMuted = useCallback(
    (muted: boolean) => {
      // moduleLoggers.MultiPlayer.info(`设置全局静音: ${muted}`);

      // 更新状态管理器
      stateManager.setGlobalMuted(muted);

      // 更新本地状态
      setGlobalPlaybackSettings((prev) => ({ ...prev, muted }));

      // 同步到所有播放器实例
      const allPlayers = playerFactory.getAllPlayers();
      allPlayers.forEach((player) => {
        if (player.xgplayer) {
          try {
            player.xgplayer.muted = muted;
            // moduleLoggers.MultiPlayer.info(
            //   `同步静音状态到播放器: ${player.instanceId}`,
            // );
          } catch (error) {
            moduleLoggers.MultiPlayer.warn(
              `同步静音状态失败: ${player.instanceId}`,
              error,
            );
          }
        }
      });

      // 发送事件
      eventManager.emit("globalPlaybackSettingsChange", { muted });
    },
    [stateManager, playerFactory, eventManager],
  );

  // 设置活跃播放器
  const setActivePlayer = useCallback(
    (id: string) => {
      moduleLoggers.MultiPlayer.info(`设置活跃播放器: ${id}`);
      moduleLoggers.MultiPlayer.info(
        `设置前状态: activePlayerId=${activePlayerId}`,
      );

      stateManager.setActiveInstance(id);

      // 手动触发状态更新（作为备用方案）
      setTimeout(() => {
        const currentActiveId = stateManager.getActiveInstance()?.id;
        moduleLoggers.MultiPlayer.info(
          `设置后状态: currentActiveId=${currentActiveId}, activePlayerId=${activePlayerId}`,
        );

        if (currentActiveId !== activePlayerId) {
          moduleLoggers.MultiPlayer.info(`手动同步状态: ${currentActiveId}`);
          setActivePlayerIdState(currentActiveId || null);
        }
      }, 50);
    },
    [stateManager, activePlayerId],
  );

  // 获取活跃播放器
  const getActivePlayer = useCallback(() => {
    return stateManager.getActiveInstance();
  }, [stateManager]);

  // 共享选集抽屉操作
  const openEpisodeDrawer = useCallback(() => {
    const activePlayer = stateManager.getActiveInstance();
    if (activePlayer?.videoData) {
      setDrawerState({
        isOpen: true,
        currentPlayerId: activePlayer.id,
        playerData: activePlayer.videoData,
      });
    }
  }, [stateManager]);

  const closeEpisodeDrawer = useCallback(() => {
    setDrawerState((prev) => ({
      ...prev,
      isOpen: false,
    }));
  }, []);

  const onEpisodeSelect = useCallback(
    (episodeNo: number) => {
      // 获取当前活跃播放器的视频数据
      const activePlayer = stateManager.getActiveInstance();
      if (activePlayer?.videoData) {
        // 找到对应的剧集
        const targetEpisode = activePlayer.videoData.series.find(
          (ep) => ep.no === episodeNo,
        );
        if (targetEpisode) {
          // 更新 URL 参数
          updateUrlVariantId(targetEpisode.id);
          moduleLoggers.MultiPlayer.info(
            `切换剧集时更新 URL 参数: ${targetEpisode.id}`,
          );
        }
      }

      switchEpisode(episodeNo);
      closeEpisodeDrawer();
    },
    [switchEpisode, closeEpisodeDrawer, stateManager],
  );

  // 切换播放器实例（通过剧集数据）
  const switchToPlayer = useCallback(
    (videoData: IPlayerVideoData, episodeNo?: number) => {
      // 查找或创建对应的播放器实例
      let targetPlayerId = `player-${videoData.id}`;
      let playerInstance = playerFactory.getPlayer(targetPlayerId);

      if (!playerInstance) {
        // 创建新的播放器实例
        playerInstance = playerFactory.createPlayer({
          id: targetPlayerId,
          containerId: "main-player-container",
          videoData,
          autoPlay: true,
          onEnded: handlePlayerEnded,
        });
      }

      // 设置为活跃播放器
      stateManager.setActiveInstance(targetPlayerId);

      // 播放指定集数
      if (episodeNo !== undefined && playerInstance) {
        playerInstance.playEpisode(episodeNo);
      }

      moduleLoggers.MultiPlayer.info(
        `切换到播放器 ${targetPlayerId}${episodeNo !== undefined ? ` 第 ${episodeNo + 1} 集` : ""}`,
      );
    },
    [playerFactory, stateManager, handlePlayerEnded],
  );

  return (
    <MultiPlayerContext.Provider
      value={{
        createPlayer,
        destroyPlayer,
        getPlayer,
        getPlayerState,
        getAllPlayers: getAllPlayersCallback,
        activePlayerId,
        setActivePlayer,
        getActivePlayer,
        drawerState,
        openEpisodeDrawer,
        closeEpisodeDrawer,
        onEpisodeSelect,
        switchToPlayer,
        switchEpisode,
        switchToNextEpisode,
        isLoading,
        totalPlayers: allPlayers.length,
        statistics,
        refreshStatistics,
        updateFavorite,
        updateCollect,
        globalPlaybackSettings,
        setGlobalPlaybackRate,
        setGlobalVolume,
        setGlobalMuted,
        handlePlayerEnded,
      }}
    >
      {children}
    </MultiPlayerContext.Provider>
  );
};
