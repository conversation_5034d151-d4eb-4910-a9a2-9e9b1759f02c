import React, { createContext, ReactNode, useContext, useMemo } from "react";

import { PlayerFactory } from "../core/factories/PlayerFactory";
import { EventManager } from "../core/managers/EventManager";
import { InstanceLifecycleManager } from "../core/managers/InstanceLifecycleManager";
import { StateManager } from "../core/managers/StateManager";
import { StatisticsManager } from "../core/managers/StatisticsManager";
import { VideoPlayerManager } from "../core/managers/VideoPlayerManager";

interface CoreContextType {
  videoPlayerManager: VideoPlayerManager;
  stateManager: StateManager;
  eventManager: EventManager;
  playerFactory: PlayerFactory;
  statisticsManager: StatisticsManager;
  instanceLifecycleManager: InstanceLifecycleManager;
}

const CoreContext = createContext<CoreContextType | undefined>(undefined);

export const useCore = () => {
  const context = useContext(CoreContext);
  if (context === undefined) {
    throw new Error("useCore must be used within a CoreProvider");
  }
  return context;
};

interface CoreProviderProps {
  children: ReactNode;
}

export const CoreProvider: React.FC<CoreProviderProps> = ({ children }) => {
  const managers = useMemo(() => {
    const eventManager = EventManager.getInstance();
    const stateManager = StateManager.getInstance();
    const playerFactory = PlayerFactory.getInstance(eventManager, stateManager);
    const videoPlayerManager = VideoPlayerManager.getInstance();
    return {
      videoPlayerManager,
      stateManager: videoPlayerManager.stateManager,
      eventManager: videoPlayerManager.eventManager,
      playerFactory,
      statisticsManager: videoPlayerManager.statisticsManager,
      instanceLifecycleManager: videoPlayerManager.instanceLifecycleManager,
    };
  }, []);

  return (
    <CoreContext.Provider value={managers}>{children}</CoreContext.Provider>
  );
};
