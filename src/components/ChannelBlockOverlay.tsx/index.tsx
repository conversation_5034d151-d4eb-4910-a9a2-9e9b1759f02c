import "./index.css";

import { FC, useEffect, useMemo, useState } from "react";

import { useCore } from "@/contexts/CoreProvider";
import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";

const ChannelBlockOverlay: FC = () => {
  const { stateManager } = useCore();
  const [data, setState] = useState<{
    currentEpisode: IPlayerEpisode | null;
    videoData: IPlayerVideoData | null;
  }>({
    currentEpisode: null,
    videoData: null,
  });

  useEffect(() => {
    const unsubscribe = stateManager.subscribeToGlobalState((globalState) => {
      const instance = stateManager.getActiveInstance();
      console.log(
        "touch active instance: ",
        instance,
        instance?.currentEpisode,
      );
      setState({
        currentEpisode: instance?.currentEpisode || null,
        videoData: globalState.videoDetail,
      });

      if (instance?.currentEpisode?.status === "channel_blocked") {
        window.currentVideoPlayer.toggleGestureDisabled(true);
      }
    });

    return () => {
      unsubscribe();
    };
  }, []);

  const showChannelBlockLayer = useMemo(() => {
    return data.currentEpisode?.status === "channel_blocked";
  }, [data.currentEpisode]);

  if (!showChannelBlockLayer) {
    return null;
  }

  return (
    <div className="bs-channel-block-layer">
      <div className="channel-block-layer-content">
        <h4>解锁10集高清大片! 浏览器打开免广告畅看!</h4>
        <p>1. 点击屏幕右上角 *** 按钮</p>
        <p>2. 选择用外部浏览器打开</p>
      </div>
      <img
        className="channel-block-layer-img"
        alt="channel block layer"
        src={data.videoData?.strategy?.img}
      />
    </div>
  );
};

ChannelBlockOverlay.displayName = "ChannelBlockOverlay";

export default ChannelBlockOverlay;
