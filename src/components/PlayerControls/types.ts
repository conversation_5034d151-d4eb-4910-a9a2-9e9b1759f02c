import Player from "xgplayer";

// 播放器实例类型 - 使用xgplayer库的Player类型
export type XGPlayer = Player;

// 播放器控制组件属性
export interface PlayerControlsProps {
  player: XGPlayer | null;
  currentEpisodeId: string | undefined;
}

// 时间轴组件属性
export interface TimelineProps {
  currentTime: number;
  duration: number;
  buffered: number;
  onSeek: (event: React.MouseEvent<HTMLDivElement>) => void;
  onMouseDown: () => void;
  onTouchStart: (event: React.TouchEvent<HTMLDivElement>) => void;
  timelineRef: React.RefObject<HTMLDivElement>;
}

// 控制按钮组件属性
export interface ControlButtonsProps {
  paused: boolean;
  playbackRate: number;
  volumeLevel: number;
  isSliderVisible: boolean;
  isPlaybackRateDropdownVisible: boolean;
  onTogglePlay: () => void;
  onPlaybackRateClick: () => void;
  onSetPlaybackRate: (rate: number) => void;
  onVolumeIconClick: () => void;
  onVolumeSliderMouseDown: (event: React.MouseEvent<HTMLDivElement>) => void;
  onVolumeSliderTouchStart: (event: React.TouchEvent<HTMLDivElement>) => void;
  onMouseEnterVolume: () => void;
  onMouseLeaveVolume: () => void;
  onMouseEnterPlayback: () => void;
  onMouseLeavePlayback: () => void;
  isIOS: boolean;
  playbackRates: number[];
  volumeSliderRef: React.RefObject<HTMLDivElement>;
}
