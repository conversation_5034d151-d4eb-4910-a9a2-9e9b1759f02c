import "./index.css";

import React, { useCallback, useEffect } from "react";

import { ControlButtons, Timeline } from "./components";
import { usePlayerControls } from "./hooks";
import { PlayerControlsProps } from "./types";

const PlayerControls: React.FC<PlayerControlsProps> = ({
  player,
  currentEpisodeId,
}) => {
  const {
    // 状态
    paused,
    currentTime,
    duration,
    buffered,
    volumeLevel,
    playbackRate,
    showMutedOverlay,
    isIOS,
    playbackRates,
    isSliderVisible,
    isPlaybackRateDropdownVisible,

    // 引用
    timelineRef,
    volumeSliderRef,

    // 事件处理函数
    togglePlay,
    handleSeek,
    handleTimelineMouseDown,
    handleTimelineTouchStart,
    handleVolumeChange,
    handleVolumeSliderMouseDown,
    handleVolumeSliderTouchStart,
    toggleMute,
    handleVolumeIconClick,
    handlePlaybackRateClick,
    handleSetPlaybackRate,
    handleMouseEnterVolume,
    handleMouseLeaveVolume,
    handleMouseEnterPlayback,
    handleMouseLeavePlayback,
  } = usePlayerControls(player, currentEpisodeId);

  const handleContainerClick = useCallback(
    (e: React.MouseEvent<HTMLDivElement>) => {
      e.stopPropagation();
    },
    [],
  );

  if (!player) {
    return null;
  }

  return (
    <div
      className="custom-solution-controls-plugin"
      onClick={handleContainerClick}
    >
      {showMutedOverlay && isIOS && (
        <div className="muted-autoplay-overlay" onClick={toggleMute} />
      )}
      <Timeline
        currentTime={currentTime}
        duration={duration}
        buffered={buffered}
        onSeek={handleSeek}
        onMouseDown={handleTimelineMouseDown}
        onTouchStart={handleTimelineTouchStart}
        timelineRef={timelineRef}
      />
      <ControlButtons
        paused={paused}
        playbackRate={playbackRate}
        volumeLevel={volumeLevel}
        isSliderVisible={isSliderVisible}
        isPlaybackRateDropdownVisible={isPlaybackRateDropdownVisible}
        onTogglePlay={togglePlay}
        onPlaybackRateClick={handlePlaybackRateClick}
        onSetPlaybackRate={handleSetPlaybackRate}
        onVolumeIconClick={handleVolumeIconClick}
        onVolumeSliderMouseDown={handleVolumeSliderMouseDown}
        onVolumeSliderTouchStart={handleVolumeSliderTouchStart}
        onMouseEnterVolume={handleMouseEnterVolume}
        onMouseLeaveVolume={handleMouseLeaveVolume}
        onMouseEnterPlayback={handleMouseEnterPlayback}
        onMouseLeavePlayback={handleMouseLeavePlayback}
        isIOS={isIOS}
        playbackRates={playbackRates}
        volumeSliderRef={volumeSliderRef}
      />
    </div>
  );
};

export default PlayerControls;
