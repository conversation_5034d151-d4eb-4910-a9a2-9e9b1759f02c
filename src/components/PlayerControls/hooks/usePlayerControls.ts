import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import Player from "xgplayer";

import { StateManager } from "@/core/managers";
import { moduleLoggers } from "@/utils/LogManager";

import { useMultiPlayer } from "../../../contexts/MultiPlayerContext";
import { XGPlayer } from "../types";
import { detectIOS, PLAYBACK_RATES } from "../utils";

export const usePlayerControls = (
  player: XGPlayer | null,
  currentEpisodeId: string | undefined,
) => {
  const {
    globalPlaybackSettings,
    setGlobalPlaybackRate,
    setGlobalVolume,
    setGlobalMuted,
  } = useMultiPlayer();

  // 基础状态
  const [paused, setPaused] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [buffered, setBuffered] = useState(0);
  const [showPlaybackDropdown, setShowPlaybackDropdown] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [volumeSliderPinned, setVolumeSliderPinned] = useState(false);

  // 播放速率下拉框状态管理
  const [showPlaybackRateDropdown, setShowPlaybackRateDropdown] =
    useState(false);
  const [playbackRateDropdownPinned, setPlaybackRateDropdownPinned] =
    useState(false);

  // 拖拽状态
  const [isTimelineDragging, setIsTimelineDragging] = useState(false);
  const [isVolumeDragging, setIsVolumeDragging] = useState(false);
  const wasPlayingBeforeDrag = useRef(false);

  // 播放器聚焦状态管理
  const [isPlayerFocused, setIsPlayerFocused] = useState(false);

  // 引用
  const timelineRef = useRef<HTMLDivElement>(null);
  const volumeSliderRef = useRef<HTMLDivElement>(null);
  const isUpdatingVolume = useRef(false);

  // Muted autoplay state
  const [mutedAutoplayDetected, setMutedAutoplayDetected] = useState(false);
  const [showMutedOverlay, setShowMutedOverlay] = useState(false);

  // 计算值
  const isIOS = useMemo(() => detectIOS(), []);
  const { playbackRate, volume, muted } = globalPlaybackSettings;

  const onControlsShow = useCallback(() => {
    setIsPlayerFocused(true);
  }, []);

  const onControlsHide = useCallback(() => {
    setPlaybackRateDropdownPinned(false);
    setShowPlaybackRateDropdown(false);
    setVolumeSliderPinned(false);
    setShowVolumeSlider(false);
    setIsPlayerFocused(false);
  }, []);

  // 播放控制
  const togglePlay = useCallback(() => {
    if (!player) return;
    player.emit(Player.Events.PLAYER_FOCUS);
    if (player.paused) {
      player.play();
    } else {
      player.pause();
    }
  }, [player]);

  useEffect(() => {
    if (!currentEpisodeId) return;
    const player = document.querySelector("#custom-solution-product-container");
    function handler(e: Event) {
      if (
        StateManager.getInstance()?.getActiveInstance()?.currentEpisode?.id !==
        currentEpisodeId
      ) {
        return;
      }
      const playbackControl = (e?.target as HTMLElement).closest?.(
        ".player-instance.active .playback-control",
      );
      if (playbackControl) {
        setVolumeSliderPinned(false);
        setShowVolumeSlider(false);
        return;
      }
      // 判断是否点击了 volume-control 或其子元素
      const volumeControl = (e?.target as HTMLElement).closest?.(
        ".player-instance.active .volume-control",
      );
      if (volumeControl) {
        setPlaybackRateDropdownPinned(false);
        setShowPlaybackRateDropdown(false);
        return;
      }
      setVolumeSliderPinned(false);
      setShowVolumeSlider(false);
      setPlaybackRateDropdownPinned(false);
      setShowPlaybackRateDropdown(false);
    }
    player?.addEventListener("click", handler);
    return () => {
      player?.removeEventListener("click", handler);
    };
  }, [currentEpisodeId]);

  // 时间轴控制
  const handleSeek = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      if (!player || !timelineRef.current) return;
      player.emit(Player.Events.PLAYER_FOCUS);
      const rect = timelineRef.current.getBoundingClientRect();
      const offsetX = event.clientX - rect.left;
      const width = rect.width;
      const percentage = offsetX / width;
      const seekTime = percentage * duration;
      if (seekTime >= 0 && seekTime <= duration) {
        player.currentTime = seekTime;
      }
    },
    [player, duration],
  );

  const handleTimelineMouseDown = useCallback(() => {
    setIsTimelineDragging(true);
    // 记录拖拽开始前的播放状态
    wasPlayingBeforeDrag.current = player ? !player.paused : false;
    // 拖拽开始时暂停播放
    if (player && !player.paused) {
      player.pause();
    }
    player?.emit(Player.Events.SEEKING);
  }, [player]);

  const handleTimelineMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!isTimelineDragging || !player || !timelineRef.current) return;
      const rect = timelineRef.current.getBoundingClientRect();
      const offsetX = event.clientX - rect.left;
      const width = rect.width;
      let percentage = offsetX / width;
      percentage = Math.max(0, Math.min(1, percentage));
      const seekTime = percentage * duration;
      player.currentTime = seekTime;
    },
    [isTimelineDragging, player, duration],
  );

  const handleTimelineMouseUp = useCallback(() => {
    if (isTimelineDragging) {
      setIsTimelineDragging(false);
      // 拖拽结束时恢复播放（如果之前是播放状态）
      if (player && wasPlayingBeforeDrag.current) {
        player.play();
      }
      player?.emit(Player.Events.SEEKED);
    }
  }, [isTimelineDragging, player]);

  const handleTimelineTouchStart = useCallback(
    (event: React.TouchEvent<HTMLDivElement>) => {
      event.preventDefault();
      setIsTimelineDragging(true);
      // 记录拖拽开始前的播放状态
      wasPlayingBeforeDrag.current = player ? !player.paused : false;
      // 拖拽开始时暂停播放
      if (player && !player.paused) {
        player.pause();
      }
      player?.emit(Player.Events.SEEKING);
      if (!player || !timelineRef.current) return;
      const rect = timelineRef.current.getBoundingClientRect();
      const touch = event.touches[0];
      const offsetX = touch.clientX - rect.left;
      const width = rect.width;
      const percentage = offsetX / width;
      const seekTime = percentage * duration;

      if (seekTime >= 0 && seekTime <= duration) {
        player.currentTime = seekTime;
      }
    },
    [player, duration],
  );

  const handleTimelineTouchMove = useCallback(
    (event: TouchEvent) => {
      if (!isTimelineDragging || !player || !timelineRef.current) return;
      const rect = timelineRef.current.getBoundingClientRect();
      const touch = event.touches[0];
      const offsetX = touch.clientX - rect.left;
      const width = rect.width;
      let percentage = offsetX / width;
      percentage = Math.max(0, Math.min(1, percentage));
      const seekTime = percentage * duration;
      player.currentTime = seekTime;
    },
    [isTimelineDragging, player, duration],
  );

  const handleTimelineTouchEnd = useCallback(() => {
    if (isTimelineDragging) {
      setIsTimelineDragging(false);
      // 拖拽结束时恢复播放（如果之前是播放状态）
      if (player && wasPlayingBeforeDrag.current) {
        player.play();
      }
      player?.emit(Player.Events.SEEKED);
    }
  }, [isTimelineDragging, player]);

  // 音量控制
  const handleVolumeChange = useCallback(
    (newVolume: number) => {
      if (!player) return;
      const v = Math.max(0, Math.min(1, newVolume));

      moduleLoggers.usePlayerControls.info(
        `🎛️ [PlayerControls] 用户调整音量: ${v}, 当前全局音量: ${volume}, 当前播放器音量: ${player.volume}`,
      );

      isUpdatingVolume.current = true;
      setGlobalVolume(v);
      setGlobalMuted(v === 0);
      setShowPlaybackDropdown(false);

      setTimeout(() => {
        isUpdatingVolume.current = false;
        moduleLoggers.usePlayerControls.info(
          `🔄 [PlayerControls] 音量更新标志已重置`,
        );
      }, 100);
    },
    [player, setGlobalVolume, setGlobalMuted, volume],
  );

  const handleVolumeSliderMouseDown = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      event.preventDefault();
      player?.emit(Player.Events.PLAYER_FOCUS);
      setIsVolumeDragging(true);

      // 设置音量调整标志，避免与手势控制冲突
      window.__BS_IS_ADJUSTING_VOLUME__ = true;

      const target = event.currentTarget as HTMLDivElement;
      const rect = target.getBoundingClientRect();
      const newVolume = (rect.bottom - event.clientY) / rect.height;
      handleVolumeChange(newVolume);
    },
    [handleVolumeChange, player],
  );

  const handleVolumeSliderTouchStart = useCallback(
    (event: React.TouchEvent<HTMLDivElement>) => {
      event.preventDefault();
      player?.emit(Player.Events.PLAYER_FOCUS);
      setIsVolumeDragging(true);

      // 设置音量调整标志，避免与手势控制冲突
      window.__BS_IS_ADJUSTING_VOLUME__ = true;

      const target = event.currentTarget as HTMLDivElement;
      const rect = target.getBoundingClientRect();
      const touch = event.touches[0];
      const newVolume = (rect.bottom - touch.clientY) / rect.height;
      handleVolumeChange(newVolume);
    },
    [handleVolumeChange, player],
  );

  const handleVolumeSliderMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!isVolumeDragging || !volumeSliderRef.current) return;

      // 确保音量调整标志已设置
      window.__BS_IS_ADJUSTING_VOLUME__ = true;

      const rect = volumeSliderRef.current.getBoundingClientRect();
      const newVolume = (rect.bottom - event.clientY) / rect.height;
      const clampedVolume = Math.max(0, Math.min(1, newVolume));
      handleVolumeChange(clampedVolume);
    },
    [isVolumeDragging, handleVolumeChange],
  );

  const handleVolumeSliderTouchMove = useCallback(
    (event: TouchEvent) => {
      if (!isVolumeDragging || !volumeSliderRef.current) return;

      // 确保音量调整标志已设置
      window.__BS_IS_ADJUSTING_VOLUME__ = true;

      const rect = volumeSliderRef.current.getBoundingClientRect();
      const touch = event.touches[0];
      const newVolume = (rect.bottom - touch.clientY) / rect.height;
      const clampedVolume = Math.max(0, Math.min(1, newVolume));
      handleVolumeChange(clampedVolume);
    },
    [isVolumeDragging, handleVolumeChange],
  );

  const handleVolumeSliderMouseUp = useCallback(() => {
    if (isVolumeDragging) {
      setIsVolumeDragging(false);

      // 清除音量调整标志
      window.__BS_IS_ADJUSTING_VOLUME__ = false;
    }
  }, [isVolumeDragging]);

  const handleVolumeSliderTouchEnd = useCallback(() => {
    if (isVolumeDragging) {
      setIsVolumeDragging(false);

      // 清除音量调整标志
      window.__BS_IS_ADJUSTING_VOLUME__ = false;
    }
  }, [isVolumeDragging]);

  const toggleMute = useCallback(() => {
    if (!player) return;
    player.emit(Player.Events.PLAYER_FOCUS);
    isUpdatingVolume.current = true;
    setGlobalMuted(!muted);
    setTimeout(() => {
      isUpdatingVolume.current = false;
    }, 100);
  }, [player, muted, setGlobalMuted]);

  const handleVolumeIconClick = useCallback(() => {
    player?.emit(Player.Events.PLAYER_FOCUS);
    if (isIOS) {
      toggleMute();
    } else {
      if (volumeSliderPinned) {
        setVolumeSliderPinned(false);
        setShowVolumeSlider(false);
      } else {
        setVolumeSliderPinned(true);
        setShowVolumeSlider(true);
      }
    }
  }, [isIOS, volumeSliderPinned, toggleMute, player]);

  // 播放速率控制
  const handlePlaybackRateClick = useCallback(() => {
    player?.emit(Player.Events.PLAYER_FOCUS);
    if (isIOS) {
      setShowPlaybackRateDropdown(!showPlaybackRateDropdown);
    } else {
      if (playbackRateDropdownPinned) {
        setPlaybackRateDropdownPinned(false);
        setShowPlaybackRateDropdown(false);
      } else {
        setPlaybackRateDropdownPinned(true);
        setShowPlaybackRateDropdown(true);
      }
    }
  }, [isIOS, showPlaybackRateDropdown, playbackRateDropdownPinned, player]);

  const handleSetPlaybackRate = useCallback(
    (rate: number) => {
      if (!player) return;
      player.emit(Player.Events.PLAYER_FOCUS);
      setGlobalPlaybackRate(rate);

      if (isIOS) {
        // iOS上设置播放速率后保持下拉框显示，允许用户继续交互
        // 不隐藏下拉框，让用户可以继续选择其他速率
      } else {
        // 非iOS设备上设置播放速率后隐藏下拉框
        setShowPlaybackRateDropdown(false);
        setPlaybackRateDropdownPinned(false);
      }
    },
    [player, setGlobalPlaybackRate, isIOS],
  );

  // 鼠标悬停控制
  const handleMouseEnterVolume = useCallback(() => {
    if (!isIOS) {
      setShowVolumeSlider(true);
    }
    player?.emit(Player.Events.PLAYER_FOCUS);
  }, [isIOS, player]);

  const handleMouseLeaveVolume = useCallback(() => {
    if (!isIOS && !volumeSliderPinned) {
      setShowVolumeSlider(false);
    }
  }, [isIOS, volumeSliderPinned]);

  const handleMouseEnterPlayback = useCallback(() => {
    if (!isIOS) {
      setShowPlaybackRateDropdown(true);
    }
    player?.emit(Player.Events.PLAYER_FOCUS);
  }, [isIOS, player]);

  const handleMouseLeavePlayback = useCallback(() => {
    if (!isIOS && !playbackRateDropdownPinned) {
      setShowPlaybackRateDropdown(false);
    }
  }, [isIOS, playbackRateDropdownPinned]);

  // 播放器事件处理函数
  const onPlay = useCallback(() => setPaused(false), []);
  const onPause = useCallback(() => setPaused(true), []);
  const onTimeUpdate = useCallback(() => {
    if (player) {
      setCurrentTime(player.currentTime);
      setDuration(player.duration);
    }
  }, [player]);
  const onLoadedData = useCallback(() => {
    if (player) {
      setDuration(player.duration);
    }
  }, [player]);
  const onVolumeChange = useCallback(() => {
    if (player && !isUpdatingVolume.current) {
      moduleLoggers.usePlayerControls.info(
        `📡 [PlayerControls] 播放器音量变化: 播放器音量=${player.volume}, 播放器静音=${player.muted}, 全局音量=${volume}, 全局静音=${muted}`,
      );
      setGlobalVolume(player.volume);
      setGlobalMuted(player.muted);
      if (!player.muted && mutedAutoplayDetected && isIOS) {
        setShowMutedOverlay(false);
      }
    } else if (isUpdatingVolume.current) {
      moduleLoggers.usePlayerControls.info(
        `⏸️ [PlayerControls] 跳过播放器音量变化事件 - 正在更新音量`,
      );
    }
  }, [
    player,
    mutedAutoplayDetected,
    isIOS,
    setGlobalVolume,
    setGlobalMuted,
    volume,
    muted,
  ]);
  const onBufferChange = useCallback(() => {
    if (player && player.buffered && player.buffered.length > 0) {
      setBuffered(player.buffered.end(player.buffered.length - 1));
    }
  }, [player]);
  const onAutoplayStarted = useCallback(() => {
    if (isIOS && player && player.muted) {
      setMutedAutoplayDetected(true);
      setShowMutedOverlay(true);
    }
  }, [isIOS, player]);

  // 事件监听器
  useEffect(() => {
    if (isTimelineDragging) {
      document.addEventListener("mousemove", handleTimelineMouseMove);
      document.addEventListener("mouseup", handleTimelineMouseUp);
      document.addEventListener("touchmove", handleTimelineTouchMove, {
        passive: false,
      });
      document.addEventListener("touchend", handleTimelineTouchEnd);
    } else {
      document.removeEventListener("mousemove", handleTimelineMouseMove);
      document.removeEventListener("mouseup", handleTimelineMouseUp);
      document.removeEventListener("touchmove", handleTimelineTouchMove);
      document.removeEventListener("touchend", handleTimelineTouchEnd);
    }

    return () => {
      document.removeEventListener("mousemove", handleTimelineMouseMove);
      document.removeEventListener("mouseup", handleTimelineMouseUp);
      document.removeEventListener("touchmove", handleTimelineTouchMove);
      document.removeEventListener("touchend", handleTimelineTouchEnd);
    };
  }, [
    isTimelineDragging,
    handleTimelineMouseMove,
    handleTimelineMouseUp,
    handleTimelineTouchMove,
    handleTimelineTouchEnd,
  ]);

  useEffect(() => {
    if (isVolumeDragging) {
      document.addEventListener("mousemove", handleVolumeSliderMouseMove);
      document.addEventListener("mouseup", handleVolumeSliderMouseUp);
      document.addEventListener("touchmove", handleVolumeSliderTouchMove, {
        passive: false,
      });
      document.addEventListener("touchend", handleVolumeSliderTouchEnd);
    } else {
      document.removeEventListener("mousemove", handleVolumeSliderMouseMove);
      document.removeEventListener("mouseup", handleVolumeSliderMouseUp);
      document.removeEventListener("touchmove", handleVolumeSliderTouchMove);
      document.removeEventListener("touchend", handleVolumeSliderTouchEnd);
    }

    return () => {
      document.removeEventListener("mousemove", handleVolumeSliderMouseMove);
      document.removeEventListener("mouseup", handleVolumeSliderMouseUp);
      document.removeEventListener("touchmove", handleVolumeSliderTouchMove);
      document.removeEventListener("touchend", handleVolumeSliderTouchEnd);
    };
  }, [
    isVolumeDragging,
    handleVolumeSliderMouseMove,
    handleVolumeSliderMouseUp,
    handleVolumeSliderTouchMove,
    handleVolumeSliderTouchEnd,
  ]);

  // 播放器事件监听
  useEffect(() => {
    if (!player) return;

    player.on(Player.Events.PLAY, onPlay);
    player.on(Player.Events.PAUSE, onPause);
    player.on(Player.Events.ENDED, onPause);
    player.on(Player.Events.TIME_UPDATE, onTimeUpdate);
    player.on(Player.Events.LOADED_DATA, onLoadedData);
    player.on(Player.Events.VOLUME_CHANGE, onVolumeChange);
    player.on(Player.Events.BUFFER_CHANGE, onBufferChange);
    player.on(Player.Events.AUTOPLAY_STARTED, onAutoplayStarted);
    player.on(Player.Events.PLAYER_BLUR, onControlsHide);
    player.on(Player.Events.PLAYER_FOCUS, onControlsShow);

    // Set initial state
    setPaused(player.paused);
    setCurrentTime(player.currentTime);
    setDuration(player.duration);

    // 设置标志，避免初始化时触发循环更新
    isUpdatingVolume.current = true;
    setGlobalVolume(player.volume);
    setGlobalMuted(player.muted);
    setGlobalPlaybackRate(player.playbackRate);

    // 延迟重置标志
    setTimeout(() => {
      isUpdatingVolume.current = false;
    }, 100);

    // 初始化缓冲状态
    if (player.buffered && player.buffered.length > 0) {
      setBuffered(player.buffered.end(player.buffered.length - 1));
    }

    return () => {
      player.off(Player.Events.PLAY, onPlay);
      player.off(Player.Events.PAUSE, onPause);
      player.off(Player.Events.ENDED, onPause);
      player.off(Player.Events.TIME_UPDATE, onTimeUpdate);
      player.off(Player.Events.LOADED_DATA, onLoadedData);
      player.off(Player.Events.VOLUME_CHANGE, onVolumeChange);
      player.off(Player.Events.BUFFER_CHANGE, onBufferChange);
      player.off(Player.Events.AUTOPLAY_STARTED, onAutoplayStarted);
      player.off(Player.Events.PLAYER_BLUR, onControlsHide);
      player.off(Player.Events.PLAYER_FOCUS, onControlsShow);
    };
  }, [
    player,
    isIOS,
    mutedAutoplayDetected,
    setGlobalVolume,
    setGlobalMuted,
    setGlobalPlaybackRate,
    onPlay,
    onPause,
    onTimeUpdate,
    onLoadedData,
    onVolumeChange,
    onBufferChange,
    onAutoplayStarted,
    onControlsHide,
    onControlsShow,
  ]);

  // 同步全局播放设置到当前播放器
  useEffect(() => {
    if (!player || isUpdatingVolume.current) {
      if (isUpdatingVolume.current) {
        moduleLoggers.usePlayerControls.info(
          `⏸️ [PlayerControls] 跳过同步 - 正在更新音量`,
        );
      }
      return;
    }

    // 同步播放速度
    if (player.playbackRate !== playbackRate) {
      try {
        player.playbackRate = playbackRate;
        // moduleLoggers.usePlayerControls.info(
        //   `同步播放速度到播放器: ${playbackRate}x`,
        // );
      } catch (error) {
        moduleLoggers.usePlayerControls.warn(`同步播放速度失败:`, error);
      }
    }

    // 同步音量
    if (player.volume !== volume) {
      try {
        // moduleLoggers.usePlayerControls.info(
        //   `同步音量到播放器: 全局=${volume}, 播放器=${player.volume}`,
        // );
        player.volume = volume;
      } catch (error) {
        moduleLoggers.usePlayerControls.warn(`同步音量失败:`, error);
      }
    }

    // 同步静音状态
    if (player.muted !== muted) {
      try {
        // moduleLoggers.usePlayerControls.info(
        //   `同步静音状态到播放器: 全局=${muted}, 播放器=${player.muted}`,
        // );
        player.muted = muted;
      } catch (error) {
        moduleLoggers.usePlayerControls.warn(`同步静音状态失败:`, error);
      }
    }
  }, [player, playbackRate, volume, muted]);

  // 计算值
  const volumeLevel = useMemo(() => (muted ? 0 : volume), [muted, volume]);

  // 控件显示逻辑
  const isSliderVisible = useMemo(
    () => !isIOS && (showVolumeSlider || volumeSliderPinned),
    [isIOS, showVolumeSlider, volumeSliderPinned],
  );
  const isPlaybackRateDropdownVisible = useMemo(
    () => showPlaybackRateDropdown || playbackRateDropdownPinned,
    [showPlaybackRateDropdown, playbackRateDropdownPinned],
  );

  // 统一监听 PLAYER_FOCUS 事件，延迟隐藏控制栏
  useEffect(() => {
    if (!player) return;
    let timer: NodeJS.Timeout | null = null;
    const onFocus = () => {
      setIsPlayerFocused(true);
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => setIsPlayerFocused(false), 3000);
    };
    player.on(Player.Events.PLAYER_FOCUS, onFocus);
    return () => {
      player.off(Player.Events.PLAYER_FOCUS, onFocus);
      if (timer) clearTimeout(timer);
    };
  }, [player]);

  return {
    // 状态
    paused,
    currentTime,
    duration,
    buffered,
    volumeLevel,
    playbackRate,
    isSliderVisible,
    isPlaybackRateDropdownVisible,
    showMutedOverlay,
    isIOS,
    playbackRates: PLAYBACK_RATES,

    // 引用
    timelineRef,
    volumeSliderRef,

    // 事件处理函数
    togglePlay,
    handleSeek,
    handleTimelineMouseDown,
    handleTimelineTouchStart,
    handleVolumeChange,
    handleVolumeSliderMouseDown,
    handleVolumeSliderTouchStart,
    toggleMute,
    handleVolumeIconClick,
    handlePlaybackRateClick,
    handleSetPlaybackRate,
    handleMouseEnterVolume,
    handleMouseLeaveVolume,
    handleMouseEnterPlayback,
    handleMouseLeavePlayback,
  };
};
