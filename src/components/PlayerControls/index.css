.custom-solution-controls-plugin {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  flex-direction: column;
  padding: 10px 15px 10px;
  color: white;
  z-index: 20;
  pointer-events: auto;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.timeline-container {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
  justify-content: space-between;
}

.timeline-progress {
  position: relative;
  flex: 1;
  border-radius: 6px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.3);
  margin: 0 10px;
  cursor: pointer;
}

.timeline-buffer-bar,
.timeline-progress-bar {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
}

.timeline-buffer-bar {
  background-color: rgba(255, 255, 255, 0.5);
  z-index: 1;
}

.timeline-progress-bar {
  background-color: #fff;
  z-index: 2;
  border-radius: 6px;
}

.timeline-progress-dot {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  cursor: grab;
  width: 16px;
  height: 16px;
  background-color: white;
  border-radius: 50%;
  z-index: 3;
}

.current-time,
.total-time {
  font-size: 12px;
  text-align: center;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.control-left,
.control-right {
  display: flex;
  align-items: center;
}

.play-pause,
.playback,
.volume-control {
  cursor: pointer;
  position: relative;
}

.playback-control {
  position: relative;
  margin-right: 20px;
}

.playback {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.60);
  width: 40px;
  height: 26px;
  text-align: center;
  line-height: 26px;
  font-size: 11px;
  font-weight: 700;
  color: #000;
  cursor: pointer;
  position: relative;
}

.play-pause svg,
.volume-icon svg {
  width: 24px;
  height: 24px;
  fill: white;
}

.playback-dropdown {
  position: absolute;
  bottom: 100%;
  left: 0;
  margin-bottom: 10px;
  border-radius: 6px;
  background: #000;
  display: none;
  flex-direction: column;
  padding: 17px 0;
  min-width: 80px;
  z-index: 10;
  row-gap: 10px;
}

.playback-dropdown.show {
  display: flex;
}

.playback-option {
  color: #999;
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 110%;
  height: 15px;
  cursor: pointer;
  text-align: center;
}

.playback-option.active,
.playback-option:hover {
  color: #fff;
  font-weight: 700;
}

.volume-control {
  position: relative;
}

.volume-slider-container {
  display: none;
  position: absolute;
  bottom: 100%;
  left: 50%;
  border-radius: 6px;
  transform: translateX(-50%);
  margin-bottom: 7px;
  background: #000;
  border-radius: 6px;
  padding: 10px;
  flex-direction: column;
  align-items: center;
  width: 37px;
  height: 145px;
  z-index: 100;
  touch-action: none;
}

.volume-slider-container.show {
  display: flex;
}

.volume-slider {
  width: 4px;
  height: 125px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  position: relative;
  cursor: pointer;
  touch-action: none;
}

.volume-slider-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  border-radius: 6px;
}

.volume-slider-handle {
  position: absolute;
  left: 50%;
  transform: translate(-50%, 50%);
  width: 16px;
  height: 16px;
  background: #fff;
  border-radius: 50%;
  cursor: grab;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  touch-action: none;
}

.muted-autoplay-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: block;
}

.muted-autoplay-overlay.hidden {
  display: none;
}

.floating-volume-control {
  /* Styles will be set dynamically */
}

.shakeX {
  animation: shakeX 1s infinite;
}

@keyframes shakeX {
  from,
  to {
    transform: translate3d(0, 0, 0);
  }
  10%,
  30%,
  50%,
  70%,
  90% {
    transform: translate3d(-5px, 0, 0);
  }
  20%,
  40%,
  60%,
  80% {
    transform: translate3d(5px, 0, 0);
  }
}

.volume-tooltip {
  display: none;
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  padding: 10px;
  border-radius: 5px;
  text-align: center;
  white-space: nowrap;
  z-index: 1002;
}

.volume-tooltip.show {
  display: block;
}

.volume-tooltip-button {
  background-color: #ff0000;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 3px;
  cursor: pointer;
  margin-top: 5px;
}
