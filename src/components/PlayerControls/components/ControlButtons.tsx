import React from "react";

import { ControlButtonsProps } from "../types";
import { MutedIcon, PauseIcon, PlayIcon, VolumeIcon } from "./Icons";

// 独立的控制按钮组件，避免频繁重新渲染
export const ControlButtons = React.memo<ControlButtonsProps>(
  ({
    paused,
    playbackRate,
    volumeLevel,
    isSliderVisible,
    isPlaybackRateDropdownVisible,
    onTogglePlay,
    onPlaybackRateClick,
    onSetPlaybackRate,
    onVolumeIconClick,
    onVolumeSliderMouseDown,
    onVolumeSliderTouchStart,
    onMouseEnterVolume,
    onMouseLeaveVolume,
    onMouseEnterPlayback,
    onMouseLeavePlayback,
    isIOS,
    playbackRates,
    volumeSliderRef,
  }) => {
    return (
      <div className="control-buttons">
        <div className="control-left">
          <span className="play-pause" onClick={onTogglePlay}>
            {paused ? <PlayIcon /> : <PauseIcon />}
          </span>
        </div>
        <div className="control-right">
          <div
            className="playback-control"
            onMouseEnter={onMouseEnterPlayback}
            onMouseLeave={onMouseLeavePlayback}
          >
            <span className="playback" onClick={onPlaybackRateClick}>
              {playbackRate}x
              <div
                className={`playback-dropdown ${
                  isPlaybackRateDropdownVisible ? "show" : ""
                }`}
              >
                {playbackRates.map((rate) => (
                  <div
                    key={rate}
                    className={`playback-option ${
                      playbackRate === rate ? "active" : ""
                    }`}
                    onClick={() => onSetPlaybackRate(rate)}
                  >
                    {rate}x
                  </div>
                ))}
              </div>
            </span>
          </div>
          <div
            className="volume-control"
            onMouseEnter={onMouseEnterVolume}
            onMouseLeave={onMouseLeaveVolume}
          >
            <span className="volume-icon" onClick={onVolumeIconClick}>
              {volumeLevel > 0 ? <VolumeIcon /> : <MutedIcon />}
            </span>
            <div
              className={`volume-slider-container ${
                isSliderVisible ? "show" : ""
              }`}
            >
              <div
                className="volume-slider"
                ref={volumeSliderRef}
                onMouseDown={onVolumeSliderMouseDown}
                onTouchStart={onVolumeSliderTouchStart}
              >
                <div
                  className="volume-slider-progress"
                  style={{ height: `${volumeLevel * 100}%` }}
                />
                <div
                  className="volume-slider-handle"
                  style={{ bottom: `${volumeLevel * 100}%` }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ControlButtons.displayName = "ControlButtons";
