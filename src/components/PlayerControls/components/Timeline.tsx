import React, { useMemo } from "react";

import { TimelineProps } from "../types";
import { formatTime } from "../utils";

// 独立的进度条组件，避免频繁重新渲染
export const Timeline = React.memo<TimelineProps>(
  ({
    currentTime,
    duration,
    buffered,
    onSeek,
    onMouseDown,
    onTouchStart,
    timelineRef,
  }) => {
    const progress = useMemo(
      () => (duration > 0 ? (currentTime / duration) * 100 : 0),
      [currentTime, duration],
    );
    const bufferProgress = useMemo(
      () => (duration > 0 ? (buffered / duration) * 100 : 0),
      [buffered, duration],
    );

    return (
      <div className="timeline-container">
        <span className="current-time">{formatTime(currentTime)}</span>
        <div
          className="timeline-progress"
          ref={timelineRef}
          onClick={onSeek}
          onMouseDown={onMouseDown}
          onTouchStart={onTouchStart}
        >
          <div
            className="timeline-buffer-bar"
            style={{ width: `${bufferProgress}%` }}
          />
          <div
            className="timeline-progress-bar"
            style={{ width: `${progress}%` }}
          />
          <div
            className="timeline-progress-dot"
            style={{ left: `${progress}%` }}
          />
        </div>
        <span className="total-time">{formatTime(duration)}</span>
      </div>
    );
  },
);

Timeline.displayName = "Timeline";
