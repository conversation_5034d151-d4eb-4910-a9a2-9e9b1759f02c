import Player from "xgplayer";

import { isFacebookBrowser, isIOSDevice } from "@/utils/userAgentDetection";

// 格式化时间显示
export const formatTime = (seconds: number) => {
  return Player.Util.format(seconds);
};

// 检测是否为iOS设备 - 使用统一的 UA 检测工具
export const detectIOS = () => {
  return isIOSDevice();
};

/**
 * 检测是否为 Facebook 内置浏览器 - 使用统一的 UA 检测工具
 * @returns 是否为 Facebook 浏览器
 */
export const detectFacebook = () => {
  return isFacebookBrowser();
};

// 播放速率选项
export const PLAYBACK_RATES = [0.75, 1, 1.25, 1.5, 2];
