import { useCallback, useEffect, useMemo, useRef } from "react";

// 条形配置接口
interface BarConfig {
  x: number;
  width: number;
  minHeight: number;
  maxHeight: number;
}

// 条形状态接口
interface BarState {
  currentHeight: number;
  lastUpdate: number;
  animationSpeed: number;
}

// 动画选项接口
interface AnimationOptions {
  width: number;
  height: number;
  color: string;
  bottomOffset: number;
}

export const usePlayingAnimation = (
  containerRef: React.RefObject<HTMLDivElement>,
) => {
  const animationFrameRef = useRef<number>();
  const isAnimatingRef = useRef(false);
  const barElementsRef = useRef<SVGPathElement[]>([]);
  const barStatesRef = useRef<BarState[]>([]);
  const pathCache = useRef<Map<string, string>>(new Map());

  // 使用 useMemo 优化配置选项
  const options: AnimationOptions = useMemo(
    () => ({
      width: 17,
      height: 16,
      color: "#D52929",
      bottomOffset: 2,
    }),
    [],
  );

  // 使用 useMemo 优化条形配置
  const barConfigs: BarConfig[] = useMemo(
    () => [
      { x: 15, width: 2, minHeight: 6, maxHeight: 16 },
      { x: 10, width: 2, minHeight: 4, maxHeight: 14 },
      { x: 5, width: 2, minHeight: 5, maxHeight: 15 },
      { x: 0, width: 2, minHeight: 3, maxHeight: 11 },
    ],
    [],
  );

  // 使用 useCallback 优化路径创建函数
  const createBarPath = useCallback(
    (x: number, y: number, width: number, height: number): string => {
      const cacheKey = `${x}-${y}-${width}-${height}`;

      if (pathCache.current.has(cacheKey)) {
        return pathCache.current.get(cacheKey)!;
      }

      const radius = width / 2;
      const path = `
        M${x + radius} ${y}
        C${x + width} ${y} ${x + width} ${y} ${x + width} ${y + radius}
        V${y + height - radius}
        C${x + width} ${y + height} ${x + width} ${y + height} ${x + radius} ${y + height}
        C${x} ${y + height} ${x} ${y + height} ${x} ${y + height - radius}
        V${y + radius}
        C${x} ${y} ${x} ${y} ${x + radius} ${y}
        Z
      `
        .trim()
        .replace(/\s+/g, " ");

      // 缓存路径，但限制缓存大小
      if (pathCache.current.size < 100) {
        pathCache.current.set(cacheKey, path);
      }

      return path;
    },
    [],
  );

  // 使用 useCallback 优化目标高度生成函数
  const generateTargetHeight = useCallback(
    (configIndex: number): number => {
      const config = barConfigs[configIndex];
      return (
        config.minHeight + Math.random() * (config.maxHeight - config.minHeight)
      );
    },
    [barConfigs],
  );

  // 使用 useCallback 优化动画帧函数
  const animateFrame = useCallback(
    (timestamp: number) => {
      if (!isAnimatingRef.current) return;

      barElementsRef.current.forEach((bar, index) => {
        const state = barStatesRef.current[index];
        const config = barConfigs[index];

        // 检查是否需要新的目标高度（保持原有的随机间隔逻辑）
        if (timestamp - state.lastUpdate > state.animationSpeed) {
          // 直接跳跃到新高度（保持原有行为，不使用插值）
          const newHeight = generateTargetHeight(index);
          state.currentHeight = newHeight;
          state.lastUpdate = timestamp;
          // 保持原有的动画间隔：170-200ms
          state.animationSpeed = 170 + Math.random() * 30;

          // 立即更新路径（跳跃式动画）
          const y = options.height - state.currentHeight + options.bottomOffset;
          const path = createBarPath(
            config.x,
            y,
            config.width,
            state.currentHeight,
          );

          bar.setAttribute("d", path);
        }
      });

      // 继续动画循环
      animationFrameRef.current = requestAnimationFrame(animateFrame);
    },
    [barConfigs, options, generateTargetHeight, createBarPath],
  );

  // 使用 useCallback 优化开始动画函数
  const startAnimation = useCallback(() => {
    if (isAnimatingRef.current) return;

    isAnimatingRef.current = true;

    // 初始化每个条形的状态
    barStatesRef.current = barConfigs.map((config, index) => ({
      currentHeight: config.minHeight,
      lastUpdate: 0,
      animationSpeed: 170 + index * 50, // 错开初始动画时间，保持原有间隔基础
    }));

    // 启动动画循环
    animationFrameRef.current = requestAnimationFrame(animateFrame);
  }, [barConfigs, animateFrame]);

  // 使用 useCallback 优化停止动画函数
  const stopAnimation = useCallback(() => {
    isAnimatingRef.current = false;
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = undefined;
    }
  }, []);

  // 使用 useCallback 优化 SVG 初始化函数
  const initializeSVG = useCallback(() => {
    if (!containerRef.current) return;

    // 清理容器
    const container = containerRef.current;
    while (container.firstChild) {
      container.removeChild(container.firstChild);
    }

    // 创建SVG元素
    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
    svg.setAttribute("xmlns", "http://www.w3.org/2000/svg");
    svg.setAttribute("width", options.width.toString());
    svg.setAttribute("height", options.height.toString());
    svg.setAttribute("viewBox", `0 0 ${options.width} ${options.height}`);
    svg.setAttribute("fill", "none");

    // 创建条形元素
    const barElements: SVGPathElement[] = [];
    barConfigs.forEach((config) => {
      const bar = document.createElementNS(
        "http://www.w3.org/2000/svg",
        "path",
      );
      bar.setAttribute("fill", options.color);
      bar.setAttribute("fill-rule", "evenodd");
      bar.setAttribute("clip-rule", "evenodd");

      // 设置初始路径
      const barHeight = config.minHeight;
      const y = options.height - barHeight + options.bottomOffset;
      const path = createBarPath(config.x, y, config.width, barHeight);
      bar.setAttribute("d", path);

      svg.appendChild(bar);
      barElements.push(bar);
    });

    barElementsRef.current = barElements;
    container.appendChild(svg);
  }, [options, barConfigs, createBarPath]);

  // 使用 useMemo 优化 IntersectionObserver 配置
  const observerConfig = useMemo(() => ({ threshold: 0.1 }), []);

  // 可见性检测优化
  useEffect(() => {
    if (!containerRef.current) return;

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          startAnimation();
        } else {
          stopAnimation();
        }
      });
    }, observerConfig);

    observer.observe(containerRef.current);

    return () => {
      observer.disconnect();
      stopAnimation();
    };
  }, [observerConfig, startAnimation, stopAnimation]);

  // 初始化效果
  useEffect(() => {
    initializeSVG();

    return () => {
      stopAnimation();
      pathCache.current.clear();
    };
  }, [initializeSVG, stopAnimation]);

  // 使用 useMemo 优化返回对象
  return useMemo(
    () => ({
      startAnimation,
      stopAnimation,
    }),
    [startAnimation, stopAnimation],
  );
};
