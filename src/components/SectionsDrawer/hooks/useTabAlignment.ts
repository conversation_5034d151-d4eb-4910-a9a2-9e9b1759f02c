import { useCallback, useEffect, useMemo } from "react";

import { moduleLoggers } from "@/utils/LogManager";

export const useTabAlignment = (isOpen: boolean, activeTabIndex: number) => {
  // 使用 useMemo 优化配置常量
  const ALIGNMENT_CONFIG = useMemo(
    () => ({
      HEADER_PADDING: 18, // 头部内边距
      TOTAL_PADDING: 36, // 总内边距（左右各18px）
    }),
    [],
  );

  // 使用 useCallback 优化对齐函数
  const alignTabsHeader = useCallback(() => {
    const activePanel = document.querySelector(
      ".tab-panel.active",
    ) as HTMLDivElement;
    const tabsHeader = document.querySelector(".tabs-header") as HTMLDivElement;

    if (!activePanel || !tabsHeader) {
      moduleLoggers.SectionsDrawer.warn(
        "SectionsDrawer: 未找到必要的 DOM 元素进行对齐矫正",
      );
      return;
    }

    const firstSectionItem = activePanel.querySelector(
      ".section-item",
    ) as HTMLDivElement;
    if (!firstSectionItem) {
      // 如果没有找到第一个 section-item，重置样式
      tabsHeader.style.transform = "";
      tabsHeader.style.width = "";
      return;
    }

    const offsetLeft = firstSectionItem.offsetLeft;

    if (offsetLeft <= 0) {
      // 如果偏移量为0或负数，重置样式
      tabsHeader.style.transform = "";
      tabsHeader.style.width = "";
      return;
    }

    // 计算矫正偏移量
    const correctionOffset = Math.max(
      0,
      offsetLeft - ALIGNMENT_CONFIG.HEADER_PADDING,
    );

    // 应用矫正样式
    tabsHeader.style.transform = `translateX(${correctionOffset}px)`;
    tabsHeader.style.width = `calc(100% - ${ALIGNMENT_CONFIG.TOTAL_PADDING}px - ${correctionOffset}px)`;
  }, [ALIGNMENT_CONFIG]);

  // 使用 useCallback 优化清理函数
  const resetTabsHeaderStyle = useCallback(() => {
    const tabsHeader = document.querySelector(".tabs-header") as HTMLDivElement;
    if (tabsHeader) {
      tabsHeader.style.transform = "";
      tabsHeader.style.width = "";
    }
  }, []);

  useEffect(() => {
    if (!isOpen) return;

    // 使用 requestAnimationFrame 确保在 DOM 渲染完成后执行
    const frameId = requestAnimationFrame(alignTabsHeader);

    // 清理函数：重置样式
    return () => {
      cancelAnimationFrame(frameId);
      resetTabsHeaderStyle();
    };
  }, [isOpen, activeTabIndex, alignTabsHeader, resetTabsHeaderStyle]);
};
