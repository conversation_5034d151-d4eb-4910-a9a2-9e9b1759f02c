import { useCallback, useEffect, useMemo, useRef, useState } from "react";

interface UseDrawerStateProps {
  externalIsOpen?: boolean;
  onOpen?: () => void;
  onAfterOpen?: () => void;
  onBeforeClose?: () => void;
  onAfterClose?: () => void;
  onClose?: () => void;
}

export const useDrawerState = ({
  externalIsOpen,
  onOpen,
  onAfterOpen,
  onBeforeClose,
  onAfterClose,
  onClose,
}: UseDrawerStateProps) => {
  // 内置状态管理
  const [internalIsOpen, setInternalIsOpen] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isClosing, setIsClosing] = useState(false);
  const [shouldShow, setShouldShow] = useState(false);
  const wasOpen = useRef(false);

  // 使用外部状态或内部状态
  const isOpen = externalIsOpen !== undefined ? externalIsOpen : internalIsOpen;

  // 控制抽屉的显示和滑出动画
  useEffect(() => {
    if (isOpen && !wasOpen.current) {
      // 抽屉开始打开 - 先设置为不显示，然后在下一帧触发动画
      setShouldShow(false);
      // 使用两个requestAnimationFrame确保DOM已经渲染
      requestAnimationFrame(() => {
        requestAnimationFrame(() => {
          setShouldShow(true);
        });
      });
    } else if (!isOpen && wasOpen.current) {
      // 抽屉开始关闭
      setShouldShow(false);
    }
  }, [isOpen]);

  // 监听 isOpen 变化，触发相应的回调
  useEffect(() => {
    if (isOpen && !wasOpen.current) {
      // 抽屉从关闭变为打开
      wasOpen.current = true;
      setIsAnimating(true);

      // 立即调用 onOpen 回调
      onOpen?.();

      // 等待动画完成后调用 onAfterOpen
      const timer = setTimeout(() => {
        setIsAnimating(false);
        onAfterOpen?.();
      }, 300); // 对应 CSS 中的动画时长

      return () => clearTimeout(timer);
    } else if (!isOpen && wasOpen.current) {
      // 抽屉从打开变为关闭
      wasOpen.current = false;
      setIsAnimating(true);

      // 等待动画完成后调用 onAfterClose
      const timer = setTimeout(() => {
        setIsAnimating(false);
        onAfterClose?.();
      }, 300); // 对应 CSS 中的动画时长

      return () => clearTimeout(timer);
    }
  }, [isOpen, onOpen, onAfterOpen, onAfterClose]);

  const handleClose = useCallback(() => {
    // 调用关闭前回调
    onBeforeClose?.();

    // 设置关闭状态，开始关闭动画
    setIsClosing(true);
    setShouldShow(false);

    // 延迟关闭，等待动画完成
    setTimeout(() => {
      // 如果使用外部状态，调用外部关闭回调，否则设置内部状态
      if (externalIsOpen !== undefined) {
        onClose?.();
      } else {
        setInternalIsOpen(false);
      }

      // 重置关闭状态
      setIsClosing(false);

      // 调用兼容的 onClose 回调（如果不是外部控制）
      if (externalIsOpen === undefined) {
        onClose?.();
      }
    }, 300);
  }, [externalIsOpen, onBeforeClose, onClose]);

  const open = useCallback(() => setInternalIsOpen(true), []);

  const close = useCallback(() => {
    setIsClosing(true);
    setTimeout(() => {
      setInternalIsOpen(false);
      setIsClosing(false);
    }, 300);
  }, []);

  // 使用 useMemo 优化返回对象
  return useMemo(
    () => ({
      isOpen,
      isAnimating,
      isClosing,
      shouldShow,
      open,
      close,
      handleClose,
    }),
    [isOpen, isAnimating, isClosing, shouldShow, open, close, handleClose],
  );
};
