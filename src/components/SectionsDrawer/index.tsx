import "./index.css";

import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";

import { DrawerData } from "../../utils/dataTransforms";
import { CloseIcon, SectionItem } from "./components";
import { useDrawerState, useTabAlignment } from "./hooks";

// 暴露给父组件的方法
export interface SectionsDrawerRef {
  open: () => void;
  close: () => void;
  isOpen: boolean;
}

interface ReactSectionsDrawerProps {
  data: DrawerData;
  onEpisodeChange?: (episodeNo: number) => void;

  // 外部控制状态（可选，用于共享抽屉）
  isOpen?: boolean;

  // 事件回调 - 全部可选
  onOpen?: () => void; // 抽屉开始打开时调用
  onAfterOpen?: () => void; // 抽屉完全打开后调用（动画结束后）
  onBeforeClose?: () => void; // 抽屉开始关闭前调用
  onAfterClose?: () => void; // 抽屉完全关闭后调用（动画结束后）
  onClose?: () => void; // 抽屉关闭时调用（兼容之前的API）
}

const ReactSectionsDrawer = forwardRef<
  SectionsDrawerRef,
  ReactSectionsDrawerProps
>(
  (
    {
      data,
      onEpisodeChange,
      isOpen: externalIsOpen,
      onOpen,
      onAfterOpen,
      onBeforeClose,
      onAfterClose,
      onClose,
    },
    ref,
  ) => {
    const [activeTabIndex, setActiveTabIndex] = useState(data.activeGroupIndex);

    // 使用 useMemo 优化 useDrawerState 的配置对象
    const drawerStateConfig = useMemo(
      () => ({
        externalIsOpen,
        onOpen,
        onAfterOpen,
        onBeforeClose,
        onAfterClose,
        onClose,
      }),
      [
        externalIsOpen,
        onOpen,
        onAfterOpen,
        onBeforeClose,
        onAfterClose,
        onClose,
      ],
    );

    // 使用抽屉状态管理hook
    const { isOpen, isClosing, shouldShow, open, close, handleClose } =
      useDrawerState(drawerStateConfig);

    // 使用标签页对齐hook
    useTabAlignment(isOpen, activeTabIndex);

    // 使用 useCallback 优化事件处理函数
    const handleEpisodeClick = useCallback(
      (episodeNo: number) => {
        if (!isClosing) {
          onEpisodeChange?.(episodeNo);
          handleClose();
        }
      },
      [isClosing, onEpisodeChange, handleClose],
    );

    const handleBackdropClick = useCallback(
      (e: React.MouseEvent) => {
        // 移除遮罩层点击关闭功能，只允许通过X按钮关闭
        // if (e.target === e.currentTarget && !isClosing) {
        //   handleClose();
        // }
      },
      [isClosing, handleClose],
    );

    const handleTabClick = useCallback((index: number) => {
      setActiveTabIndex(index);
    }, []);

    // 暴露给父组件的方法
    const imperativeHandle = useMemo(
      () => ({
        open,
        close,
        isOpen,
      }),
      [isOpen, open, close],
    );

    useImperativeHandle(ref, () => imperativeHandle, [imperativeHandle]);

    useEffect(() => {
      setActiveTabIndex(data.activeGroupIndex);
    }, [data.activeGroupIndex]);

    if (!isOpen && !isClosing) return null;

    return (
      <div
        className={`sections-drawer-backdrop ${isClosing ? "closing" : ""}`}
        onClick={handleBackdropClick}
      >
        <div
          className={`sections-drawer ${
            shouldShow && !isClosing ? "open" : ""
          }`}
        >
          <div className="drawer-content">
            {/* 头部 */}
            <div className="drawer-header">
              <div className="tabs-header">
                {data.groups.map((group, index) => (
                  <button
                    key={index}
                    className={`tab-button ${
                      index === activeTabIndex ? "active" : ""
                    }`}
                    onClick={() => handleTabClick(index)}
                  >
                    {group.start}-{group.end}
                  </button>
                ))}
              </div>
              <button className="close-button" onClick={handleClose}>
                <CloseIcon />
              </button>
            </div>

            {/* 内容区域 */}
            <div className="drawer-body">
              {data.groups.map((group, groupIndex) => (
                <div
                  key={groupIndex}
                  className={`tab-panel ${
                    groupIndex === activeTabIndex ? "active" : ""
                  }`}
                >
                  <div className="sections-grid">
                    {group.indexList.map((sectionIndex) => {
                      const section = data.sections[sectionIndex - 1];
                      const isPlaying = sectionIndex === data.current;
                      const isLocked = section?.status === "locked";
                      const isPaid = section?.status === "paid";

                      return (
                        <SectionItem
                          key={sectionIndex}
                          sectionIndex={sectionIndex}
                          section={section}
                          isPlaying={isPlaying}
                          isLocked={isLocked}
                          isPaid={isPaid}
                          onEpisodeClick={handleEpisodeClick}
                        />
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  },
);

ReactSectionsDrawer.displayName = "ReactSectionsDrawer";

export default ReactSectionsDrawer;
