import React from "react";

// 锁定图标组件
export const LockIcon: React.FC = () => (
  <span className="lock-icon">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
    >
      <path
        d="M0 0H14C15.1046 0 16 0.895431 16 2V16H8C3.58172 16 0 12.4183 0 8V0Z"
        fill="#E50113"
      ></path>
      <path
        d="M7.14286 6.28571H10.5714V5C10.5714 4.05586 9.80129 3.28571 8.85714 3.28571C7.913 3.28571 7.14286 4.05586 7.14286 5V6.28571ZM12.7143 6.92857V10.7857C12.7143 10.9562 12.6466 11.1197 12.526 11.2403C12.4054 11.3608 12.2419 11.4286 12.0714 11.4286H5.64286C5.55844 11.4286 5.47484 11.4119 5.39685 11.3796C5.31885 11.3473 5.24798 11.3 5.18829 11.2403C5.12859 11.1806 5.08124 11.1097 5.04893 11.0317C5.01663 10.9537 5 10.8701 5 10.7857V6.92857C5 6.75807 5.06773 6.59456 5.18829 6.474C5.30885 6.35344 5.47236 6.28571 5.64286 6.28571H5.85714V5C5.85714 3.35257 7.20971 2 8.85714 2C10.5046 2 11.8571 3.35257 11.8571 5V6.28571H12.0714C12.2419 6.28571 12.4054 6.35344 12.526 6.474C12.6466 6.59456 12.7143 6.75807 12.7143 6.92857Z"
        fill="white"
      ></path>
    </svg>
  </span>
);

// VIP 图标组件
export const VipIcon: React.FC = () => (
  <span className="vip-icon">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="22"
      height="11"
      viewBox="0 0 22 11"
      fill="none"
    >
      <path
        d="M1.375 0H20.625C21.45 0 22 0.55 22 1.375V9.625C22 10.45 21.45 11 20.625 11H1.375C0.55 11 0 10.45 0 9.625V1.375C0 0.6875 0.55 0 1.375 0Z"
        fill="#FF6600"
      />
      <path
        d="M5.0875 8.9373C4.8125 8.9373 4.5375 8.66231 4.4 8.11231L2.0625 1.9248H3.85L5.5 6.1873L8.1125 1.9248H10.0375L6.05 8.11231C5.6375 8.79981 5.3625 8.9373 5.0875 8.9373ZM9.4875 8.79981L10.45 2.0623H12.1L11.1375 8.79981H9.4875ZM19.9375 4.5373C19.8 5.2248 19.525 5.9123 18.8375 6.4623C18.2875 6.8748 17.6 7.1498 16.9125 7.1498H14.025L13.75 8.79981H12.1L12.65 5.3623H17.325C17.6 5.3623 17.7375 5.22481 17.875 5.08731C18.0125 4.94981 18.15 4.6748 18.15 4.5373C18.15 4.2623 18.15 4.1248 18.0125 3.9873C17.875 3.8498 17.6 3.7123 17.4625 3.7123H12.7875L14.1625 1.9248H17.6C18.2875 1.9248 18.975 2.1998 19.3875 2.6123C19.9375 3.2998 20.075 3.9873 19.9375 4.5373Z"
        fill="white"
      />
    </svg>
  </span>
);

// 关闭图标组件
export const CloseIcon: React.FC = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="19"
    height="18"
    viewBox="0 0 19 18"
    fill="none"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.6775 16.2931C18.068 16.6836 18.068 17.3168 17.6775 17.7073C17.287 18.0978 16.6538 18.0978 16.2633 17.7073L9.38462 10.8286L2.50595 17.7073C2.11542 18.0978 1.48226 18.0978 1.09173 17.7073C0.701207 17.3168 0.701207 16.6836 1.09173 16.2931L7.97041 9.41441L0.706956 2.15095C0.316431 1.76043 0.316431 1.12727 0.706956 0.736741C1.09748 0.346216 1.73065 0.346217 2.12117 0.736741L9.38462 8.0002L16.6481 0.736741C17.0386 0.346217 17.6718 0.346216 18.0623 0.736741C18.4528 1.12727 18.4528 1.76043 18.0623 2.15095L10.7988 9.41441L17.6775 16.2931Z"
      fill="white"
      fillOpacity="0.5"
    ></path>
  </svg>
);
