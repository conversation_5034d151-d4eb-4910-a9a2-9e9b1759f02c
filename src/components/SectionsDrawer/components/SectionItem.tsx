import React, { useCallback, useMemo } from "react";

import { LockIcon, PlayingIcon, VipIcon } from "./";

interface SectionItemProps {
  sectionIndex: number;
  section: any;
  isPlaying: boolean;
  isLocked: boolean;
  isPaid: boolean;
  onEpisodeClick: (episodeNo: number) => void;
}

export const SectionItem: React.FC<SectionItemProps> = ({
  sectionIndex,
  section,
  isPlaying,
  isLocked,
  isPaid,
  onEpisodeClick,
}) => {
  // 使用 useMemo 优化类名计算
  const className = useMemo(
    () =>
      `section-item ${isPlaying ? "playing" : ""} ${
        isLocked ? "locked" : ""
      } ${isPaid ? "paid" : ""}`,
    [isPlaying, isLocked, isPaid],
  );

  // 使用 useCallback 优化点击处理函数
  const handleClick = useCallback(
    () => onEpisodeClick(sectionIndex),
    [sectionIndex, onEpisodeClick],
  );

  return (
    <div className={className} onClick={handleClick}>
      <div className="section-content">
        {isPlaying ? (
          <PlayingIcon />
        ) : (
          <span className="section-number">{sectionIndex}</span>
        )}
        {isLocked && <LockIcon />}
        {isPaid && <VipIcon />}
      </div>
    </div>
  );
};
