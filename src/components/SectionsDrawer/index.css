/* 背景遮罩 */
.sections-drawer-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  opacity: 0;
  animation: backdropFadeIn 0.2s ease-out forwards;
}

.sections-drawer-backdrop.closing {
  /* 移除关闭动画，立即隐藏 */
  opacity: 0 !important;
  background-color: rgba(0, 0, 0, 0) !important;
  animation: none !important;
  transition: none !important;
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    background-color: rgba(0, 0, 0, 0);
  }
  to {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.5);
  }
}

@keyframes backdropFadeOut {
  from {
    opacity: 1;
    background-color: rgba(0, 0, 0, 0.5);
  }
  to {
    opacity: 0;
    background-color: rgba(0, 0, 0, 0);
  }
}

/* 抽屉主体 */
.sections-drawer {
  width: 100%;
  height: 360px;
  background-color: #000;
  border-radius: 10px 10px 0 0;
  transform: translateY(100%);
  /* 仅在打开时有动画，关闭时立即隐藏 */
  transition: transform 0.2s ease-in;
  display: flex;
  flex-direction: column;
  will-change: transform;
}

.sections-drawer.open {
  transform: translateY(0);
}

/* 当抽屉关闭时，立即隐藏（无动画） */
.sections-drawer:not(.open) {
  transition: none;
}

/* 抽屉内容 */
.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 头部区域 */
.drawer-header {
  position: relative;
  padding: 15px 18px 18px;
}

.tabs-header {
  display: flex;
  gap: 18px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.tab-button {
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  position: relative;
  color: rgba(255, 255, 255, 0.5);
  white-space: nowrap;
  line-height: 130%;
  border: none;
  background: transparent;
  padding: 0;
}

.tab-button.active {
  color: #ffffff;
}

.tab-button.active::after {
  content: "";
  display: block;
  width: 100%;
  height: 4px;
  background: #d52929;
  bottom: -4px;
  position: absolute;
  overflow: hidden;
  border-radius: 4px;
}

.close-button {
  position: absolute;
  top: 0;
  right: 0;
  padding: 15px 18px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  background: transparent;
  border: none;
}

/* 内容主体 */
.drawer-body {
  flex: 1;
  overflow-y: auto;
  /* padding: 0 20px 20px; */
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: flex;
}

/* 章节网格 */
.sections-grid {
  justify-content: center;
  display: grid;
  overflow-y: auto;
  grid-template-columns: repeat(auto-fill, 48px);
  grid-auto-rows: 48px;
  gap: 10px;
  flex: 1;
  padding: 0 18px 18px;
}

.section-item {
  width: 48px;
  height: 48px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  font-size: 16px;
  font-weight: 400;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.section-item.playing {
  border: 2px solid #d52929;
  background: rgba(212, 41, 41, 0.3);
}

.section-item.locked {
  /* background-color: #f1f5f9; */
  /* border-color: #cbd5e1; */
  cursor: not-allowed;
}

.section-item.paid {
  /* background-color: #fffbeb; */
  /* border-color: #f59e0b; */
}

.section-content {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.section-number {
  font-size: 16px;
}

/* 播放状态图标 */
.playing-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 移除原来的CSS动画相关样式，现在使用JavaScript控制 */

/* 状态图标 */
.lock-icon,
.vip-icon {
  display: flex;
  position: absolute;
  top: 0;
  right: 0;
  background-color: transparent;
}