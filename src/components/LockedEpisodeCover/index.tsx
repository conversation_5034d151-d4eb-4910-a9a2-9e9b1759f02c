import "./index.css";

import React, { useCallback } from "react";

import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

interface LockedEpisodeCoverProps {
  videoData: IPlayerVideoData;
  currentEpisode: IPlayerEpisode;
}

const LockedEpisodeCover: React.FC<LockedEpisodeCoverProps> = ({
  videoData,
  currentEpisode,
}) => {
  const handleCoverClick = useCallback(() => {
    moduleLoggers.LockedEpisodeCover.info(
      `用户点击需要权限的剧集封面: 视频ID=${videoData.id}, 集数ID=${currentEpisode.id}`,
    );

    // 调用第三方购买弹窗
    window.SPZ.whenApiDefined(
      document.getElementById("subscribe_check_right_logic"),
    ).then((api: any) => {
      api.checkright(videoData.id, currentEpisode.id);
    });
  }, [videoData.id, currentEpisode.id]);

  return (
    <div className="locked-episode-cover">
      {/* 背景封面图 */}
      <div
        className="cover-background"
        style={{
          backgroundImage: videoData.cover?.src
            ? `url(${videoData.cover.src})`
            : "none",
        }}
      />

      {/* 遮罩层 */}
      <div className="cover-overlay" />

      {/* 中央播放按钮 */}
      <div className="cover-center" onClick={handleCoverClick}>
        <div className="play-button-container">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="play-button"
            width="28"
            height="40"
            viewBox="3 -4 28 40"
          >
            <path
              fill="#fff"
              transform="scale(0.0320625 0.0320625)"
              d="M576,363L810,512L576,661zM342,214L576,363L576,661L342,810z"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default LockedEpisodeCover;
