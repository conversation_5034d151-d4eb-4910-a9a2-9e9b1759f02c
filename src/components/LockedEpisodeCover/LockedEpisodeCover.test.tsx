import { cleanup, fireEvent, render, screen } from "@testing-library/react";
import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import LockedEpisodeCover from "./index";

// Mock window.SPZ
const mockSPZ = {
  whenApiDefined: vi.fn().mockResolvedValue({
    checkright: vi.fn(),
  }),
};

Object.defineProperty(window, "SPZ", {
  value: mockSPZ,
  writable: true,
});

// Mock window.C_SETTINGS
Object.defineProperty(window, "C_SETTINGS", {
  value: {
    routes: {
      root: "/",
    },
  },
  writable: true,
});

const mockVideoData = {
  id: "test-video-id",
  title: "测试视频标题",
  description: "测试视频描述",
  tags: ["测试", "视频"],
  cover: {
    src: "https://example.com/cover.jpg",
    alt: "测试封面",
    width: 1920,
    height: 1080,
    is_last_play: false,
  },
  operate: {
    liked: false,
    collected: false,
    subscribed: false,
  },
  metaFields: {},
  series: [
    {
      id: "episode-1",
      no: 1,
      currentTime: "0",
      url: "https://example.com/episode1.mp4",
      is_last_play: true,
      play_duration: "1200",
      subTitles: [],
      operate: {
        liked: false,
        collected: false,
        subscribed: false,
      },
      status: "locked" as const,
      metaFields: {},
    },
  ],
};

const mockCurrentEpisode = mockVideoData.series[0];

describe("LockedEpisodeCover", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    cleanup();
  });

  it("应该正确渲染封面组件", () => {
    render(
      <LockedEpisodeCover
        videoData={mockVideoData}
        currentEpisode={mockCurrentEpisode}
      />,
    );
  });

  it("点击播放按钮时应该调用购买弹窗", async () => {
    const { container } = render(
      <LockedEpisodeCover
        videoData={mockVideoData}
        currentEpisode={mockCurrentEpisode}
      />,
    );

    // 点击播放按钮
    const playButton = container.querySelector(".play-button");
    fireEvent.click(playButton as SVGElement);

    // 验证 SPZ API 被调用
    expect(mockSPZ.whenApiDefined).toHaveBeenCalledWith(
      document.getElementById("subscribe_check_right_logic"),
    );
  });

  it("应该正确应用背景图片样式", () => {
    const { container } = render(
      <LockedEpisodeCover
        videoData={mockVideoData}
        currentEpisode={mockCurrentEpisode}
      />,
    );

    const coverBackground = container.querySelector(".cover-background");
    expect(coverBackground).toHaveStyle({
      backgroundImage: "url(https://example.com/cover.jpg)",
    });
  });

  it("当没有封面图片时应该正确处理", () => {
    const videoDataWithoutCover = {
      ...mockVideoData,
      cover: undefined,
    } as any; // 使用类型断言避免类型检查错误

    const { container } = render(
      <LockedEpisodeCover
        videoData={videoDataWithoutCover}
        currentEpisode={mockCurrentEpisode}
      />,
    );

    const playButton = container.querySelector(".play-button");

    // 组件应该仍然正常渲染
    expect(playButton).toBeInTheDocument();
  });
});
