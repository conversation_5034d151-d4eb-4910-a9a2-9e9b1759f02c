import "./index.css";

import React, { useCallback } from "react";

import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

interface EmptyEpisodeCoverProps {
  videoData: IPlayerVideoData;
  currentEpisode: IPlayerEpisode;
}

const EmptyEpisodeCover: React.FC<EmptyEpisodeCoverProps> = ({
  videoData,
  currentEpisode,
}) => {
  return (
    <div className="empty-episode-cover">
      {/* 背景封面图 */}
      <div
        className="cover-background"
        style={{
          backgroundImage: videoData.cover?.src
            ? `url(${videoData.cover.src})`
            : "none",
        }}
      />

      {/* 遮罩层 */}
      <div className="cover-overlay" />
    </div>
  );
};

export default EmptyEpisodeCover;
