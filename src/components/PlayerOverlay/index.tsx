import "./index.css";

import React, { useCallback, useMemo } from "react";
import Player from "xgplayer";

import { useMultiPlayer } from "@/contexts/MultiPlayerContext";
import { StatisticsManager } from "@/core/managers";
import { IPlayerEpisode } from "@/services/AuthManager";
import { formatCount } from "@/utils/dataTransforms";
import { moduleLoggers } from "@/utils/LogManager";

import PlayerControls from "../PlayerControls";
import ChannelOverlay from "../ChannelBlockOverlay.tsx";

// 使用xgplayer库的Player类型
type XGPlayer = Player;

interface EpisodeOperate {
  liked: boolean;
  collected: boolean;
}

interface PlayerOverlayProps {
  player: XGPlayer | null;
  title: string;
  totalEpisodes: number;
  currentEpisode: IPlayerEpisode | null;
  videoId: string;
  operate: EpisodeOperate;
  onLike: () => void;
  onCollect: () => void;
  onShare: () => void;
  onShowEpisodeList: () => void;
  children: React.ReactNode;
}

const PlayerOverlay: React.FC<PlayerOverlayProps> = ({
  player,
  title,
  totalEpisodes,
  currentEpisode,
  videoId,
  operate,
  onLike,
  onCollect,
  onShare,
  onShowEpisodeList,
  children,
}) => {
  const { statistics, updateFavorite, updateCollect, getPlayer } =
    useMultiPlayer();

  const handleContentClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
  }, []);

  const handleLike = useCallback(async () => {
    if (!currentEpisode) return;
    const newLikedState = !operate.liked;
    await updateFavorite(videoId, currentEpisode.id, newLikedState);
    onLike();
  }, [currentEpisode, operate, videoId, onLike, updateFavorite]);

  const handleCollect = useCallback(async () => {
    if (!currentEpisode) return;
    const newCollectedState = !operate.collected;
    await updateCollect(videoId, currentEpisode.id, newCollectedState);
    onCollect();
  }, [currentEpisode, operate, videoId, onCollect, updateCollect]);

  const handleBack = useCallback(() => {
    moduleLoggers.PlayerOverlay.info("handleBack", currentEpisode);
    const homeUrl = window.C_SETTINGS.routes.root || "/";
    const player = getPlayer(currentEpisode?.id || "");
    // 退出前立刻上报一次播放进度
    if (currentEpisode) {
      moduleLoggers.PlayerOverlay.info("handleBack: reportPlayProgress", {
        currentTime: player?.xgplayer?.currentTime,
      });

      StatisticsManager.getInstance().reportPlaybackProgress(
        currentEpisode.id,
        true,
      );
    }

    try {
      const referrer = document.referrer;
      if (referrer && referrer.startsWith(window.location.origin)) {
        const referrerUrl = new URL(referrer);
        if (referrerUrl.pathname === homeUrl) {
          window.location.href = homeUrl;
          return;
        }
      }
    } catch (e) {
      moduleLoggers.PlayerOverlay.warn(
        "Error processing document.referrer for back navigation",
        e,
      );
    }

    if (window.history.length > 1) {
      if (window.BSPlayerContainer) {
        window.BSPlayerContainer.destroySpaContainer();
        history.go(-1);
        return;
      }
    }
    window.location.href = homeUrl;
  }, [currentEpisode, getPlayer]);

  return (
    <div className="player-overlay">
      {children}
      {currentEpisode && (
        <div className="overlay-content" onClick={handleContentClick}>
          {/* 顶部标题区域 */}
          <div className="overlay-header">
            <div className="video-title">
              <span className="video-title-back" onClick={handleBack}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="11"
                  height="20"
                  viewBox="0 0 11 20"
                  fill="none"
                >
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M3.38182 9.51768L10.4707 16.6065C11.0565 17.1923 11.0565 18.1421 10.4707 18.7278C9.88488 19.3136 8.93514 19.3136 8.34935 18.7278L0.571174 10.9497C0.279575 10.6581 0.133138 10.2763 0.131851 9.89412C-0.116503 9.3422 -0.014008 8.67086 0.439338 8.21751L8.21751 0.43934C8.8033 -0.146447 9.75305 -0.146447 10.3388 0.43934C10.9246 1.02513 10.9246 1.97488 10.3388 2.56066L3.38182 9.51768Z"
                    fill="white"
                  />
                </svg>
              </span>
              <div className="video-title-text">
                <h2>{title}</h2>
                <span>
                  &nbsp;
                  {currentEpisode.no || 1}/{totalEpisodes}
                </span>
              </div>
            </div>
          </div>

          {/* 右侧操作按钮区域 */}
          <div className="overlay-actions">
            <button
              className={`action-btn like-btn ${operate.liked ? "active" : ""}`}
              onClick={handleLike}
              aria-label="like"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="24"
                viewBox="0 0 28 24"
                fill="currentColor"
              >
                <path
                  d="M21.1051 0.00806153C15.5395 0.352461 14 4.82942 14 4.82942C14 4.82942 12.4605 0.352461 6.89489 0.00806153C1.32934 -0.336338 -8.24067 10.3969 14 24C36.2406 10.3969 26.6707 -0.3363 21.1051 0.00806153Z"
                  fill="currentColor"
                ></path>
              </svg>
              <span>{formatCount(statistics?.like || 0)}</span>
            </button>

            <button
              className={`action-btn collect-btn ${operate.collected ? "active" : ""}`}
              onClick={handleCollect}
              aria-label="collect"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="27"
                viewBox="0 0 28 27"
                fill="currentColor"
              >
                <path
                  d="M8.19261 26.6307C7.01414 27.3549 5.47307 26.9803 4.75731 25.8014C4.418 25.2416 4.31254 24.5731 4.46331 23.9377L5.98863 17.5045C6.00679 17.4297 6.00412 17.3513 5.98089 17.2778C5.95765 17.2043 5.91473 17.1384 5.85668 17.0871L0.863859 12.7845C-0.173205 11.8914 -0.292906 10.3296 0.593655 9.28837C0.804609 9.03996 1.06323 8.83534 1.35459 8.68634C1.64595 8.53733 1.96428 8.44689 2.29118 8.42023L8.84362 7.89235C8.98642 7.88059 9.11417 7.78747 9.17262 7.6459L11.6972 1.5395C12.2233 0.267401 13.6863 -0.340088 14.9627 0.194018C15.5715 0.449885 16.0532 0.933588 16.3026 1.5395L18.8271 7.64555C18.8856 7.78747 19.0134 7.88093 19.1562 7.89235L25.7093 8.42058C27.0834 8.53134 28.1009 9.73352 27.992 11.0984C27.9409 11.7497 27.6337 12.3553 27.1363 12.7852L22.1434 17.0878C22.0272 17.1878 21.9747 17.3498 22.0115 17.5049L23.5368 23.9381C23.8525 25.269 23.0328 26.61 21.6947 26.9308C21.3757 27.0074 21.0445 27.0203 20.7204 26.9689C20.3963 26.9174 20.0859 26.8026 19.8072 26.6311L14.1969 23.1834C14.1379 23.1468 14.0696 23.1274 13.9999 23.1274C13.9302 23.1274 13.8619 23.1468 13.8028 23.1834L8.19226 26.6311L8.19261 26.6307Z"
                  fill="currentColor"
                ></path>
              </svg>
              <span>{formatCount(statistics?.collect || 0)}</span>
            </button>

            <button
              className="action-btn share-btn"
              onClick={onShare}
              aria-label="share"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="23"
                viewBox="0 0 28 23"
                fill="none"
              >
                <path
                  d="M1.24089 22.9519C1.18135 22.9551 1.1217 22.9483 1.06436 22.9318C0.744745 22.8853 0.455653 22.7149 0.258433 22.4567C0.0612127 22.1985 -0.0286133 21.8729 0.00801675 21.5489C0.614489 15.7989 2.63037 11.5526 5.97594 8.89898C8.59227 6.90919 11.7601 5.79674 15.0346 5.71781L15.7777 5.679V1.58933C15.7777 -0.0307203 17.2455 -0.385781 18.0271 0.403402L27.164 9.78735C27.4301 10.0057 27.6435 10.2821 27.7883 10.5959C27.933 10.9096 28.0053 11.2525 27.9997 11.5985C27.9941 11.9446 27.9107 12.2849 27.7558 12.5937C27.6009 12.9025 27.3786 13.1717 27.1057 13.3811L18.2036 22.5278C18.0152 22.7432 17.7657 22.8946 17.4889 22.9616C17.2122 23.0285 16.9218 23.0077 16.6572 22.9019C16.3926 22.7961 16.1668 22.6106 16.0104 22.3705C15.8541 22.1305 15.7748 21.8476 15.7834 21.5604V17.1833H14.8638C11.5182 17.1833 5.43353 17.8546 2.24456 22.3798C2.14668 22.5587 2.00194 22.707 1.82617 22.8084C1.65039 22.9098 1.45038 22.9605 1.24801 22.9548L1.24089 22.9519Z"
                  fill="white"
                ></path>
              </svg>
              <span>Share</span>
            </button>

            <button
              className="action-btn episodes-btn"
              onClick={onShowEpisodeList}
              aria-label="episodes"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="26"
                height="26"
                viewBox="0 0 26 26"
                fill="none"
              >
                <path
                  d="M18.902 0.1144C17.3264 -1.67079e-07 15.3972 0 13 0H12.87L8.32 6.825H14.4274L18.9007 0.1144H18.902ZM10.5235 0.00519986C6.0073 0.0402999 3.5308 0.2756 1.9032 1.9032C0.7878 3.0186 0.3263 4.5318 0.1352 6.825H5.9774L10.5235 0.00519986ZM25.8635 6.825C25.6724 4.5318 25.2096 3.0186 24.0955 1.9032C23.3181 1.1271 22.3496 0.6682 21.0574 0.3952L16.7713 6.825H25.8635ZM0 13C0 11.3919 -1.45286e-07 9.9957 0.0337999 8.775H25.9662C26 9.9957 26 11.3919 26 13C26 19.1282 26 22.1923 24.0955 24.0955C22.1936 26 19.1282 26 13 26C6.8718 26 3.8077 26 1.9032 24.0955C-1.16229e-07 22.1936 0 19.1282 0 13ZM5.85 14.625C5.59141 14.625 5.34342 14.7277 5.16057 14.9106C4.97772 15.0934 4.875 15.3414 4.875 15.6C4.875 15.8586 4.97772 16.1066 5.16057 16.2894C5.34342 16.4723 5.59141 16.575 5.85 16.575H16.25C16.5086 16.575 16.7566 16.4723 16.9394 16.2894C17.1223 16.1066 17.225 15.8586 17.225 15.6C17.225 15.3414 17.1223 15.0934 16.9394 14.9106C16.7566 14.7277 16.5086 14.625 16.25 14.625H5.85ZM4.875 20.15C4.875 20.6882 5.3118 21.125 5.85 21.125H13C13.2586 21.125 13.5066 21.0223 13.6894 20.8394C13.8723 20.6566 13.975 20.4086 13.975 20.15C13.975 19.8914 13.8723 19.6434 13.6894 19.4606C13.5066 19.2777 13.2586 19.175 13 19.175H5.85C5.59141 19.175 5.34342 19.2777 5.16057 19.4606C4.97772 19.6434 4.875 19.8914 4.875 20.15Z"
                  fill="white"
                ></path>
              </svg>
              <span>
                {currentEpisode.no || 1}/{totalEpisodes}
              </span>
            </button>
          </div>

          {/* 播放进度和其他信息 */}
          <div className="overlay-footer">
            <div className="episode-info">
              {/* <span>当前播放: 第{currentEpisode?.no || 1} 集</span> */}
            </div>
          </div>
        </div>
      )}
      {/* 总是显示PlayerControls，即使没有currentEpisode */}
      <PlayerControls player={player} currentEpisodeId={currentEpisode?.id} />
      <ChannelOverlay currentEpisode={currentEpisode} />
    </div>
  );
};

export default PlayerOverlay;
