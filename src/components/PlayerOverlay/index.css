.player-overlay {
  position: relative;
  width: 100%;
  height: 100%;
}

.overlay-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.overlay-content > * {
  pointer-events: auto;
}

/* 顶部标题区域 - 允许事件 */
.overlay-header {
  pointer-events: auto;
  flex: 0 0 auto;
  margin-bottom: 20px;
}

.video-title {
  color: white;
  padding: 28px 10px;
  display: flex;
  align-items: center;
  line-height: 130%;
}

.video-title-back {
  display: flex;
  align-items: center;
  cursor: pointer;
  position: relative;
}

.video-title-back::after {
  content: "";
  position: absolute;
  left: -5px;
  right: -5px;
  top: -5px;
  bottom: -5px;
}

.video-title-text {
  display: flex;
  align-items: center;
  margin: 0 10px;
  display: flex;
  flex: 1;
  overflow: hidden;
}

.video-title h2 {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  margin: 0;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.video-title-text span {
  margin-left: 2px;
  font-size: 12px;
}

.video-description {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin: 0;
  line-height: 1.4;
  max-width: 70%;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 右侧操作按钮区域 - 允许事件 */
.overlay-actions {
  pointer-events: auto;
  position: absolute;
  right: 18px;
  bottom: 114px;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  border: none;
  color: white;
  cursor: pointer;
  border: none;
  background: transparent;
  width: 28px;
  row-gap: 5px;
  position: relative;
}

.action-btn::after {
  position: absolute;
  content: "";
  left: -5px;
  right: -5px;
  top: -5px;
  bottom: -5px;
}

.action-btn:hover {
  /* background: rgba(255, 255, 255, 0.2); */
  /* transform: scale(1.05); */
}

.action-btn svg {
  margin-bottom: 4px;
}

.action-btn span {
  font-size: 12px;
  text-align: center;
  line-height: 1.2;
}

.like-btn.active svg {
  color: #f00;
}

.collect-btn.active svg {
  color: #FFA048;
}

/* 底部信息区域 - 允许事件 */
.overlay-footer {
  pointer-events: auto;
  flex: 0 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.episode-info {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.video-title {
  display: flex;
  /* justify-content: center; */
  align-items: center;
}

/* 设置封面图片的背景大小为cover */
.player-overlay .player-wrapper .xgplayer-poster {
  background-size: cover;
}

/* 当播放器不活跃时，隐藏自定义控制条和操作内容 */
.player-overlay:has(.xgplayer-inactive:not(.xgplayer-nostart))
  .custom-solution-controls-plugin,
.player-overlay:has(.xgplayer-inactive:not(.xgplayer-nostart)) .overlay-footer,
.player-overlay:has(.xgplayer-inactive:not(.xgplayer-nostart))
  .overlay-actions {
  opacity: 0;
  pointer-events: none;
}

.player-overlay:has(.xgplayer-inactive:not(.xgplayer-nostart)) .overlay-content {
  background: transparent;
}