import { render } from "@testing-library/react";
import React from "react";
import { describe, expect, it } from "vitest";

import WebComponent from "./WebComponent";

describe("WebComponent", () => {
  it("should render custom element with props", () => {
    const { container } = render(
      <WebComponent tag="spz-loading" layout="container" />,
    );

    const element = container.firstChild as HTMLElement;
    expect(element.tagName.toLowerCase()).toBe("spz-loading");
    expect(element.getAttribute("layout")).toBe("container");
  });

  it("should render with children", () => {
    const { container } = render(
      <WebComponent tag="custom-element">
        <span>Child content</span>
      </WebComponent>,
    );

    const element = container.firstChild as HTMLElement;
    expect(element.tagName.toLowerCase()).toBe("custom-element");
    expect(element.innerHTML).toContain("<span>Child content</span>");
  });
});
