import React, { forwardRef, useImperativeHandle, useRef } from "react";

interface WebComponentProps {
  tag: string;
  [key: string]: any;
}

const WebComponent = forwardRef<HTMLElement, WebComponentProps>(
  ({ tag, children, ...props }, ref) => {
    const elementRef = useRef<HTMLElement>(null);

    useImperativeHandle(ref, () => elementRef.current!, []);

    return React.createElement(
      tag,
      {
        ref: elementRef,
        ...props,
      },
      children,
    );
  },
);

WebComponent.displayName = "WebComponent";

export default WebComponent;
