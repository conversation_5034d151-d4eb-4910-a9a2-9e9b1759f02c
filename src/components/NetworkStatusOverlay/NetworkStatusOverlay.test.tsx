/**
 * @file NetworkStatusOverlay.test.tsx
 * @description Network Status Overlay Component Tests
 */

import { fireEvent, render, screen, waitFor } from "@testing-library/react";
import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import { NetworkStatusOverlay } from "./index";

// Mock NetworkStateManager
vi.mock("../../../core/managers/NetworkStateManager", () => ({
  NetworkStateManager: {
    getInstance: vi.fn(() => ({
      subscribe: vi.fn(() => vi.fn()),
      manualRecovery: vi.fn(),
    })),
  },
}));

describe("NetworkStatusOverlay", () => {
  const defaultProps = {
    playerId: "test-player",
    onRetry: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  // it("should render component correctly", () => {
  //   render(<NetworkStatusOverlay {...defaultProps} />);
  //   expect(screen.getByRole("button", { name: /close/i })).toBeInTheDocument();
  // });

  it("should display network error message", async () => {
    render(<NetworkStatusOverlay {...defaultProps} />);

    // Simulate network state change event
    const networkEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Connection Error",
        message:
          "Maximum retry attempts exceeded. Please click retry button to manually recover",
        type: "error",
        showRetryButton: true,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(networkEvent);

    await waitFor(() => {
      expect(screen.getByText("Network Connection Error")).toBeInTheDocument();
      expect(
        screen.getByText(
          "Maximum retry attempts exceeded. Please click retry button to manually recover",
        ),
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /retry/i }),
      ).toBeInTheDocument();
    });
  });

  it("should call retry callback function", async () => {
    const onRetry = vi.fn();
    render(<NetworkStatusOverlay {...defaultProps} onRetry={onRetry} />);

    // Display error message
    const networkEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Connection Error",
        message: "Please retry",
        type: "error",
        showRetryButton: true,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(networkEvent);

    await waitFor(() => {
      const retryButton = screen.getByRole("button", { name: /retry/i });
      fireEvent.click(retryButton);
      expect(onRetry).toHaveBeenCalledTimes(1);
    });
  });

  it("should close notification", async () => {
    render(<NetworkStatusOverlay {...defaultProps} />);

    // Display error message
    const networkEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Connection Error",
        message: "Please retry",
        type: "error",
        showRetryButton: true,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(networkEvent);

    await waitFor(() => {
      const closeButton = screen.getByRole("button", { name: /close/i });
      fireEvent.click(closeButton);

      // Notification should disappear
      expect(
        screen.queryByText("Network Connection Error"),
      ).not.toBeInTheDocument();
    });
  });

  it("should display different icons and styles based on error type", async () => {
    render(<NetworkStatusOverlay {...defaultProps} />);

    // Test error type
    const errorEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Error",
        message: "Error message",
        type: "error",
        showRetryButton: true,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(errorEvent);

    await waitFor(() => {
      expect(screen.getByText("❌")).toBeInTheDocument();
    });

    // Test warning type
    const warningEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Warning",
        message: "Warning message",
        type: "warning",
        showRetryButton: false,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(warningEvent);

    await waitFor(() => {
      expect(screen.getByText("⚠️")).toBeInTheDocument();
    });

    // Test success type
    const successEvent = new CustomEvent("networkStateChange", {
      detail: {
        title: "Network Restored",
        message: "Success message",
        type: "success",
        showRetryButton: false,
        timestamp: Date.now(),
      },
    });

    window.dispatchEvent(successEvent);

    await waitFor(() => {
      expect(screen.getByText("✅")).toBeInTheDocument();
    });
  });
});
