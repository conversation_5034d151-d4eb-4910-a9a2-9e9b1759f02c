/**
 * @file NetworkStatusOverlay.css
 * @description 网络状态提示组件样式
 */

.network-status-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  pointer-events: none;
}

.network-status-card {
  background: rgba(0, 0, 0, 0.9);
  border-radius: 12px;
  padding: 20px;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  pointer-events: auto;
  animation: networkStatusSlideIn 0.3s ease-out;
}

@keyframes networkStatusSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.network-status-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  gap: 8px;
}

.network-status-icon {
  font-size: 20px;
  line-height: 1;
}

.network-status-title {
  flex: 1;
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
}

.network-status-close {
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  line-height: 1;
}

.network-status-close:hover {
  color: #ffffff;
  background: rgba(255, 255, 255, 0.1);
}

.network-status-message {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
  margin-bottom: 16px;
}

.network-status-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.network-status-retry-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  color: #ffffff;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.network-status-retry-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.network-status-retry-btn:active {
  transform: translateY(0);
}

/* 不同状态的颜色主题 */
.network-status-success {
  border-left: 4px solid #10b981;
}

.network-status-success .network-status-icon {
  color: #10b981;
}

.network-status-warning {
  border-left: 4px solid #f59e0b;
}

.network-status-warning .network-status-icon {
  color: #f59e0b;
}

.network-status-error {
  border-left: 4px solid #ef4444;
}

.network-status-error .network-status-icon {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .network-status-card {
    margin: 20px;
    padding: 16px;
  }
  
  .network-status-title {
    font-size: 15px;
  }
  
  .network-status-message {
    font-size: 13px;
  }
  
  .network-status-retry-btn {
    padding: 10px 20px;
    font-size: 14px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .network-status-card {
    background: rgba(17, 24, 39, 0.95);
    border-color: rgba(75, 85, 99, 0.3);
  }
}

/* 动画效果 */
.network-status-overlay {
  animation: networkStatusFadeIn 0.2s ease-out;
}

@keyframes networkStatusFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 退出动画 */
.network-status-overlay.network-status-exiting {
  animation: networkStatusFadeOut 0.2s ease-in;
}

@keyframes networkStatusFadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.network-status-overlay.network-status-exiting .network-status-card {
  animation: networkStatusSlideOut 0.2s ease-in;
}

@keyframes networkStatusSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
} 