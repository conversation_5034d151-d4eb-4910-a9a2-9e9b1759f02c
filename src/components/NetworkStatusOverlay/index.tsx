/**
 * @file NetworkStatusOverlay.tsx
 * @description Network Status Overlay Component
 * - Display network connection status
 * - Provide network error notifications and recovery buttons
 * - Support auto-hide and manual operations
 */

import "./index.css";

import React, { useCallback, useEffect, useState } from "react";

import { StateManager } from "@/core/managers";

import { NetworkStateManager } from "../../core/managers/NetworkStateManager";

interface NetworkStatusOverlayProps {
  playerId: string;
  onRetry?: () => void;
  className?: string;
}

interface NetworkMessage {
  title: string;
  message: string;
  type: "success" | "warning" | "error";
  showRetryButton: boolean;
  timestamp: number;
  instanceId?: string;
}

export const NetworkStatusOverlay: React.FC<NetworkStatusOverlayProps> = ({
  playerId,
  onRetry,
  className = "",
}) => {
  const [networkState, setNetworkState] = useState<any>(null);
  const [currentMessage, setCurrentMessage] = useState<NetworkMessage | null>(
    null,
  );
  const [isVisible, setIsVisible] = useState(false);
  const [autoHideTimer, setAutoHideTimer] = useState<NodeJS.Timeout | null>(
    null,
  );

  useEffect(() => {
    const networkStateManager = NetworkStateManager.getInstance();

    // 订阅网络状态变化 - 只用于更新状态，不直接显示弹窗
    const unsubscribe = networkStateManager.subscribe((state) => {
      setNetworkState(state);

      // 只有在网络恢复时才隐藏错误提示
      if (state.isOnline && !state.isBlocked) {
        setIsVisible(false);
        setCurrentMessage(null);
        if (autoHideTimer) {
          clearTimeout(autoHideTimer);
          setAutoHideTimer(null);
        }
      }
      // 注意：不再直接根据 currentError 显示弹窗
      // 弹窗只通过 networkStateChange 事件触发
    });

    // Listen for custom network state change events - 这是显示弹窗的唯一入口
    const handleNetworkStateChange = (event: CustomEvent) => {
      const { title, message, type, showRetryButton, timestamp, instanceId } =
        event.detail;
      showNetworkMessage({
        title,
        message,
        type,
        showRetryButton,
        timestamp,
        instanceId,
      });
    };

    window.addEventListener(
      "networkStateChange",
      handleNetworkStateChange as EventListener,
    );

    return () => {
      unsubscribe();
      window.removeEventListener(
        "networkStateChange",
        handleNetworkStateChange as EventListener,
      );
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
      }
    };
  }, []);

  const showNetworkMessage = useCallback(
    (message: NetworkMessage) => {
      if (message.instanceId && message.instanceId !== playerId) {
        return;
      }

      setCurrentMessage(message);
      setIsVisible(true);

      // Clear previous auto-hide timer
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
      }

      // Set auto-hide (success messages hide after 3 seconds, error messages don't auto-hide)
      if (message.type === "success") {
        const timer = setTimeout(() => {
          setIsVisible(false);
          setCurrentMessage(null);
        }, 3000);
        setAutoHideTimer(timer);
      }
    },
    [playerId, autoHideTimer],
  );

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      // Default recovery logic
      const networkStateManager = NetworkStateManager.getInstance();
      networkStateManager.manualRecovery();
    }

    // Hide notification
    setIsVisible(false);
    setCurrentMessage(null);
  };

  const handleClose = () => {
    setIsVisible(false);
    setCurrentMessage(null);
    if (autoHideTimer) {
      clearTimeout(autoHideTimer);
      setAutoHideTimer(null);
    }
  };

  const getErrorTitle = (errorType: string): string => {
    switch (errorType) {
      case "offline":
        return "Network Connection Lost";
      case "timeout":
        return "Connection Timeout";
      case "server_error":
        return "Server Connection Error";
      default:
        return "Network Connection Error";
    }
  };

  const getErrorType = (errorType: string): "success" | "warning" | "error" => {
    switch (errorType) {
      case "offline":
        return "warning";
      case "timeout":
      case "server_error":
        return "error";
      default:
        return "error";
    }
  };

  const getIcon = (type: string): string => {
    switch (type) {
      case "success":
        return "✅";
      case "warning":
        return "⚠️";
      case "error":
        return "❌";
      default:
        return "ℹ️";
    }
  };

  if (!isVisible || !currentMessage) {
    return null;
  }

  return (
    <div className={`network-status-overlay ${className}`}>
      <div
        className={`network-status-card network-status-${currentMessage.type}`}
      >
        <div className="network-status-header">
          <span className="network-status-icon">
            {getIcon(currentMessage.type)}
          </span>
          <span className="network-status-title">{currentMessage.title}</span>
          <button
            className="network-status-close"
            onClick={handleClose}
            aria-label="Close"
          >
            ×
          </button>
        </div>

        <div className="network-status-message">{currentMessage.message}</div>

        {currentMessage.showRetryButton && (
          <div className="network-status-actions">
            <button className="network-status-retry-btn" onClick={handleRetry}>
              Retry
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
