import "./index.css";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { moduleLoggers } from "@/utils/LogManager";

import { useMultiPlayer } from "../../contexts/MultiPlayerContext";
import {
  PlayerInstanceState,
  StateManager,
} from "../../core/managers/StateManager";
import { useViewportHeight } from "../../hooks/useViewportHeight";
import EmptyEpisodeCover from "../EmptyEpisodeCover";
import LockedEpisodeCover from "../LockedEpisodeCover";
import { NetworkStatusOverlay } from "../NetworkStatusOverlay";
import PlayerOverlay from "../PlayerOverlay";

interface ShortDramaPlayerProps {
  playerId: string;
  containerId?: string;
  autoInitialize?: boolean;
  showOverlay?: boolean;
  isFullscreen?: boolean;
}

const ShortDramaPlayer: React.FC<ShortDramaPlayerProps> = ({
  playerId,
  containerId,
  autoInitialize = true,
  isFullscreen = false,
}) => {
  const playerRef = useRef<HTMLDivElement>(null);
  const initializationRef = useRef(false);
  const [isPlayerReady, setIsPlayerReady] = useState(false);
  const [playerState, setPlayerState] = useState<PlayerInstanceState | null>(
    null,
  );

  // 使用视口高度兼容性Hook
  const { viewportHeight, isCompatibilityEnabled } = useViewportHeight();

  const {
    createPlayer,
    getPlayerState,
    activePlayerId,
    openEpisodeDrawer,
    destroyPlayer,
    getPlayer,
  } = useMultiPlayer();

  const isActive = activePlayerId === playerId;

  // 检查当前剧集是否需要权限
  const isCurrentEpisodeLocked =
    playerState?.currentEpisode?.status === "locked";

  // 订阅播放器状态变化
  useEffect(() => {
    // moduleLoggers.ShortDramaPlayer.info(`设置状态订阅: ${playerId}`);

    // 立即获取初始状态
    const initialState = getPlayerState(playerId);
    setPlayerState(initialState || null);

    // 订阅状态变化
    const stateManager = StateManager.getInstance();
    const unsubscribe = stateManager.subscribeToInstance(
      playerId,
      (newState) => {
        setPlayerState(newState);
      },
    );

    return () => {
      moduleLoggers.ShortDramaPlayer.info(`取消状态订阅: ${playerId}`);
      unsubscribe();
    };
  }, [playerId, getPlayerState]);

  // 生成容器ID
  const finalContainerId = containerId || `player-${playerId}`;

  // 检查播放器是否已经存在且正常工作
  const checkPlayerState = useCallback(() => {
    const state = getPlayerState(playerId);
    if (state && state.status !== "error") {
      setIsPlayerReady(true);
      return true;
    }
    return false;
  }, [playerId, getPlayerState]);

  // 初始化播放器
  const initializePlayer = useCallback(async () => {
    // 如果当前剧集需要权限，不初始化播放器
    if (isCurrentEpisodeLocked) {
      moduleLoggers.ShortDramaPlayer.info(
        `当前剧集需要权限，跳过播放器初始化: ${playerId}`,
      );
      return;
    }

    // 防止重复初始化
    if (initializationRef.current) {
      moduleLoggers.ShortDramaPlayer.info(
        `播放器 ${playerId} 已在初始化中，跳过`,
      );
      return;
    }

    // 检查是否已经初始化完成
    if (checkPlayerState()) {
      moduleLoggers.ShortDramaPlayer.info(`播放器 ${playerId} 已初始化，跳过`);
      return;
    }

    moduleLoggers.ShortDramaPlayer.info(`开始初始化播放器 ${playerId}`);
    initializationRef.current = true;

    try {
      // 等待DOM容器确实存在
      let containerElement = document.getElementById(finalContainerId);
      let attempts = 0;
      const maxAttempts = 20; // 最多等待2秒

      while (!containerElement && attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 100));
        containerElement = document.getElementById(finalContainerId);
        attempts++;
        moduleLoggers.ShortDramaPlayer.info(
          `等待容器渲染... 尝试 ${attempts}/${maxAttempts}`,
        );
      }

      if (!containerElement) {
        throw new Error(`播放器容器 ${finalContainerId} 等待超时`);
      }

      moduleLoggers.ShortDramaPlayer.info(
        `找到容器 ${finalContainerId}，开始初始化播放器`,
      );

      // 不需要重新初始化VideoPlayerManager，使用全局数据
      createPlayer(playerId, finalContainerId);

      // 等待一小段时间，然后检查是否需要强制初始化
      setTimeout(() => {
        const playerInstance = getPlayer(playerId);

        if (playerInstance && !playerInstance.xgplayer) {
          moduleLoggers.ShortDramaPlayer.info(
            `检测到播放器实例未完全初始化，尝试强制初始化`,
          );
          playerInstance.forceInitialize();
        }

        setIsPlayerReady(true);
      }, 500);

      moduleLoggers.ShortDramaPlayer.info(
        `播放器 ${playerId} 创建完成，等待验证`,
      );
    } catch (error) {
      moduleLoggers.ShortDramaPlayer.error(
        `播放器 ${playerId} 初始化失败:`,
        error,
      );
      setIsPlayerReady(false);
    } finally {
      initializationRef.current = false;
    }
  }, [
    playerId,
    finalContainerId,
    createPlayer,
    checkPlayerState,
    getPlayer,
    isCurrentEpisodeLocked,
  ]);

  // 使用useLayoutEffect确保DOM渲染完成后再初始化
  useEffect(() => {
    // 总是渲染DOM结构，但只有在需要时才初始化播放器
    if (autoInitialize && !isPlayerReady && !isCurrentEpisodeLocked) {
      const timer = setTimeout(() => {
        initializePlayer();
      }, 50);

      return () => clearTimeout(timer);
    }
  }, [autoInitialize, initializePlayer, isPlayerReady, isCurrentEpisodeLocked]);

  // 监听活跃播放器变化
  useEffect(() => {
    if (isActive) {
      moduleLoggers.ShortDramaPlayer.info(`播放器 ${playerId} 被设置为活跃。`);

      // 如果当前剧集需要权限，不进行播放器相关操作
      if (isCurrentEpisodeLocked) {
        moduleLoggers.ShortDramaPlayer.info(
          `当前剧集需要权限，跳过播放器操作: ${playerId}`,
        );
        return;
      }

      const playerInstance = getPlayer(playerId);

      if (playerInstance) {
        // 实例已存在，检查是否需要附加到当前容器
        if (playerInstance.containerId !== finalContainerId) {
          moduleLoggers.ShortDramaPlayer.info(
            `实例已存在但容器不匹配，正在附加到容器 ${finalContainerId}`,
          );
          playerInstance.attachToContainer(finalContainerId, undefined, true);
        } else {
          moduleLoggers.ShortDramaPlayer.info(
            `实例已存在且容器匹配，无需重新附加`,
          );
        }
        setIsPlayerReady(true);

        // 播放器变为活跃状态后，如果网络已恢复，确保HLS开始加载
        setTimeout(() => {
          if (playerInstance.checkNetworkStatus()) {
            const hls = (playerInstance as any).getCustomHlsInstance?.();
            if (hls && typeof hls.resumeHlsLoading === "function") {
              hls.resumeHlsLoading();
              moduleLoggers.ShortDramaPlayer.info(
                `Player became active: ensured HLS loading for ${playerId}`,
              );
            }
          }
        }, 100);
      } else {
        // 实例不存在，需要创建。
        // initializePlayer内部有防止重复初始化的逻辑
        moduleLoggers.ShortDramaPlayer.info(
          `实例不存在，开始创建和初始化 ${playerId}`,
        );
        initializePlayer();
      }
    }
  }, [
    isActive,
    playerId,
    getPlayer,
    finalContainerId,
    initializePlayer,
    isCurrentEpisodeLocked,
  ]);

  // 清理函数
  useEffect(() => {
    return () => {
      initializationRef.current = false;

      // 组件卸载时销毁播放器实例
      if (isPlayerReady) {
        destroyPlayer(playerId);
        moduleLoggers.ShortDramaPlayer.info(`组件卸载，销毁播放器 ${playerId}`);
      }
    };
  }, [playerId, isPlayerReady, destroyPlayer]);

  // 操作蒙层事件处理
  const handleLike = useCallback(() => {
    moduleLoggers.ShortDramaPlayer.info(`👍 点赞播放器 ${playerId}`);
    // TODO: 实现点赞逻辑
  }, [playerId]);

  const handleCollect = useCallback(() => {
    moduleLoggers.ShortDramaPlayer.info(`⭐ 收藏播放器 ${playerId}`);
    // TODO: 实现收藏逻辑
  }, [playerId]);

  const handleShare = useCallback(() => {
    moduleLoggers.ShortDramaPlayer.info(`🔗 分享播放器 ${playerId}`);
    window.SPZ.whenApiDefined(
      document.getElementById("custom-solution-product-share"),
    ).then((api: any) => {
      api.open();
    });
  }, [playerId]);

  const handleShowEpisodeList = useCallback(() => {
    openEpisodeDrawer();
  }, [openEpisodeDrawer]);

  // 网络恢复处理
  const handleNetworkRetry = useCallback(() => {
    moduleLoggers.ShortDramaPlayer.info(`手动恢复网络状态: ${playerId}`);
    const playerInstance = getPlayer(playerId);
    if (playerInstance) {
      playerInstance.manualRecovery();
    }
  }, [playerId, getPlayer]);

  // 添加手势控制调试信息
  useEffect(() => {
    if (isActive) {
      moduleLoggers.ShortDramaPlayer.info(
        `播放器 ${playerId} 已激活，手势控制已启用`,
      );

      // 监听触摸事件以验证手势控制是否工作
      const handleTouchStart = (e: TouchEvent) => {
        moduleLoggers.ShortDramaPlayer.info(`检测到触摸开始事件: ${playerId}`);
      };

      const handleTouchEnd = (e: TouchEvent) => {
        moduleLoggers.ShortDramaPlayer.info(`检测到触摸结束事件: ${playerId}`);
      };

      document.addEventListener("touchstart", handleTouchStart);
      document.addEventListener("touchend", handleTouchEnd);

      return () => {
        document.removeEventListener("touchstart", handleTouchStart);
        document.removeEventListener("touchend", handleTouchEnd);
      };
    }
  }, [isActive, playerId]);

  const PlayerContent = useMemo(() => {
    const showEmptyCover =
      !isCurrentEpisodeLocked &&
      playerState?.videoData &&
      playerState?.currentEpisode &&
      !playerState?.currentEpisode.url;

    if (showEmptyCover) {
      return (
        <EmptyEpisodeCover
          videoData={playerState.videoData!}
          currentEpisode={playerState.currentEpisode!}
        />
      );
    }

    const showLockedCover =
      isCurrentEpisodeLocked &&
      playerState?.videoData &&
      playerState?.currentEpisode;

    if (showLockedCover) {
      return (
        <LockedEpisodeCover
          videoData={playerState.videoData!}
          currentEpisode={playerState.currentEpisode!}
        />
      );
    }

    return (
      <div id={finalContainerId} ref={playerRef} className="player-wrapper" />
    );
  }, [isCurrentEpisodeLocked, playerState, finalContainerId, playerRef]);

  // 总是渲染DOM结构，不管播放器是否初始化
  return (
    <div
      className={`player-container ${isActive ? "active" : ""} ${
        isFullscreen ? "fullscreen" : ""
      }`}
      style={{
        height: isCompatibilityEnabled ? `${viewportHeight}px` : "100%",
      }}
    >
      {/* PlayerOverlay现在包装了播放器div */}
      <PlayerOverlay
        player={getPlayer(playerId)?.xgplayer || null}
        title={playerState?.videoData?.title || ""}
        totalEpisodes={playerState?.videoData?.series?.length || 0}
        currentEpisode={playerState?.currentEpisode || null}
        videoId={playerState?.videoData?.id || ""}
        operate={{
          liked: playerState?.currentEpisode?.operate?.liked || false,
          collected: playerState?.currentEpisode?.operate?.collected || false,
        }}
        onLike={handleLike}
        onCollect={handleCollect}
        onShare={handleShare}
        onShowEpisodeList={handleShowEpisodeList}
      >
        {PlayerContent}
      </PlayerOverlay>

      {/* 网络状态提示组件 */}
      <NetworkStatusOverlay playerId={playerId} onRetry={handleNetworkRetry} />
    </div>
  );
};

export default ShortDramaPlayer;
