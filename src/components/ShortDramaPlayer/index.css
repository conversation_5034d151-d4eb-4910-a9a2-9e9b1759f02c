.player-container {
  position: relative;
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  background: #000;
  cursor: pointer;
  border: 2px solid transparent;
}

.player-container.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.player-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: white;
  font-size: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.player-wrapper {
  width: 100% !important;
  height: 100% !important;
  position: relative;
}

.active-indicator {
  position: absolute;
  top: 16px;
  left: 16px;
  background: rgba(238, 82, 155, 0.9);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  z-index: 15;
  backdrop-filter: blur(10px);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* 多播放器网格布局 */
.players-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 16px;
  padding: 16px;
}

.players-grid .player-container {
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
}

/* 字幕 */
.player-container .xg-text-track {
  bottom: 129px !important;
  font-weight: 600;
  font-size: 20px !important;
}

.player-container .xg-text-track .xg-text-track-inner {
  text-shadow: none;
}

/* 全屏模式 */
.player-container.fullscreen {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: calc(var(--csp-product-vh, 1vh) * 100) !important;
  border-radius: 0 !important;
  border: none !important;
}

.player-container.fullscreen:hover {
  transform: none !important;
  border-color: transparent !important;
}

.player-wrapper-fullscreen {
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
}

/* 焦点状态 */
.player-container:focus-within {
  outline: none;
  border-color: rgba(238, 82, 155, 0.6);
}

/* 悬停效果 */
.player-container:hover:not(.active) {
  border-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.02);
}

/* 播放器加载状态覆盖层 */
.player-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}