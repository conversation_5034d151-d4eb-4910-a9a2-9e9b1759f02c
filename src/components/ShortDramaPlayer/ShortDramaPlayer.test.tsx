import { render, screen } from "@testing-library/react";
import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";

import ShortDramaPlayer from "./index";

// Mock dependencies
vi.mock("@/contexts/MultiPlayerContext", () => ({
  useMultiPlayer: vi.fn(),
}));

vi.mock("@/core/managers/StateManager", () => ({
  StateManager: {
    getInstance: vi.fn(() => ({
      subscribeToInstance: vi.fn(() => vi.fn()),
    })),
  },
}));

// Mock window.SPZ
const mockSPZ = {
  whenApiDefined: vi.fn().mockResolvedValue({
    checkright: vi.fn(),
  }),
};

Object.defineProperty(window, "SPZ", {
  value: mockSPZ,
  writable: true,
});

// Mock window.C_SETTINGS
Object.defineProperty(window, "C_SETTINGS", {
  value: {
    routes: {
      root: "/",
    },
  },
  writable: true,
});

const mockVideoData = {
  id: "test-video-id",
  title: "测试视频标题",
  description: "测试视频描述",
  tags: ["测试", "视频"],
  cover: {
    src: "https://example.com/cover.jpg",
    alt: "测试封面",
    width: 1920,
    height: 1080,
    is_last_play: false,
  },
  operate: {
    liked: false,
    collected: false,
    subscribed: false,
  },
  metaFields: {},
  series: [
    {
      id: "episode-1",
      no: 1,
      currentTime: "0",
      url: "https://example.com/episode1.mp4",
      is_last_play: true,
      play_duration: "1200",
      subTitles: [],
      operate: {
        liked: false,
        collected: false,
        subscribed: false,
      },
      status: "locked" as const,
      metaFields: {},
    },
  ],
};

// 创建完整的 useMultiPlayer mock
const createMockUseMultiPlayer = (overrides = {}) => ({
  createPlayer: vi.fn(),
  destroyPlayer: vi.fn(),
  getPlayer: vi.fn(),
  getPlayerState: vi.fn(),
  getAllPlayers: vi.fn(() => []),
  activePlayerId: "test-player",
  setActivePlayer: vi.fn(),
  getActivePlayer: vi.fn(),
  drawerState: {
    isOpen: false,
    currentPlayerId: null,
    playerData: null,
  },
  openEpisodeDrawer: vi.fn(),
  closeEpisodeDrawer: vi.fn(),
  onEpisodeSelect: vi.fn(),
  switchToPlayer: vi.fn(),
  switchEpisode: vi.fn(),
  switchToNextEpisode: vi.fn(),
  isLoading: false,
  totalPlayers: 0,
  statistics: null,
  refreshStatistics: vi.fn(),
  updateFavorite: vi.fn(),
  updateCollect: vi.fn(),
  globalPlaybackSettings: {
    playbackRate: 1.25,
    volume: 1,
    muted: false,
  },
  setGlobalPlaybackRate: vi.fn(),
  setGlobalVolume: vi.fn(),
  setGlobalMuted: vi.fn(),
  ...overrides,
});

describe("ShortDramaPlayer", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("当剧集需要权限时应该显示 LockedEpisodeCover", async () => {
    // Mock getPlayerState 返回锁定状态的剧集
    const mockGetPlayerState = vi.fn(() => ({
      id: "test-player",
      containerId: "test-container",
      videoData: mockVideoData,
      currentEpisode: mockVideoData.series[0],
      status: "locked" as const,
      isLoading: false,
      isActive: true,
      currentTime: 0,
    }));

    const { useMultiPlayer } = await import("@/contexts/MultiPlayerContext");
    vi.mocked(useMultiPlayer).mockReturnValue(
      createMockUseMultiPlayer({
        getPlayerState: mockGetPlayerState,
      }),
    );

    const { container } = render(
      <ShortDramaPlayer
        playerId="test-player"
        containerId="test-container"
        autoInitialize={false}
      />,
    );

    const playButton = container.querySelector(".play-button");

    // 应该显示锁定剧集的提示
    expect(playButton).toBeInTheDocument();
  });

  it("当剧集不需要权限时应该显示播放器", async () => {
    // Mock getPlayerState 返回正常状态的剧集
    const mockGetPlayerState = vi.fn(() => ({
      id: "test-player",
      containerId: "test-container",
      videoData: {
        ...mockVideoData,
        series: [
          {
            ...mockVideoData.series[0],
            status: "free" as const,
          },
        ],
      },
      currentEpisode: {
        ...mockVideoData.series[0],
        status: "free" as const,
      },
      status: "ready" as const,
      isLoading: false,
      isActive: true,
      currentTime: 0,
    }));

    const { useMultiPlayer } = await import("@/contexts/MultiPlayerContext");
    vi.mocked(useMultiPlayer).mockReturnValue(
      createMockUseMultiPlayer({
        getPlayerState: mockGetPlayerState,
      })
    );

    const { container } = render(
      <ShortDramaPlayer
        playerId="test-player"
        containerId="test-container"
        autoInitialize={false}
      />,
    );

    const playButton = container.querySelector(".play-button");

    // 不应该显示锁定剧集的提示
    expect(playButton).not.toBeInTheDocument();
  });
});
