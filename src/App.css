.app {
  width: 100%;
  height: calc(var(--csp-product-vh, 1vh) * 100);
  background-color: #000;
  position: relative;
  overflow: hidden;
}

/* 多实例播放器容器 */
.multi-player-container {
  position: relative;
  width: 100%;
  height: 100%;
}

/* 渠道阻断显示时, 屏蔽事件 */
.multi-player-container:has(.bs-channel-block-layer) {
  pointer-events: none;
}

/* 播放器实例 */
.player-instance {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.player-instance.active {
  z-index: 10;
}

.player-instance.hidden {
  z-index: 1;
  visibility: hidden;
  pointer-events: none;
}

/* 全屏播放器布局 */
.fullscreen-player {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 错误状态 */
.fullscreen-player.error {
  background-color: #1a1a1a;
}

.error-content {
  text-align: center;
  color: #fff;
  padding: 2rem;
}

.error-content h2 {
  margin-bottom: 1rem;
  color: #ff4444;
}

.retry-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 1rem;
}

.retry-button:hover {
  background-color: #0056b3;
}

/* 加载状态 */
.fullscreen-player.loading {
  background-color: #1a1a1a;
}

.loading-content {
  text-align: center;
  color: #fff;
  padding: 2rem;
}

.loading-content h2 {
  margin-bottom: 1rem;
  color: #fff;
}

.loading-spinner {
  border: 3px solid #333;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 1rem auto 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式布局 */
@media (max-width: 768px) {
  .loading-content h2 {
    font-size: 20px;
  }
  
  .loading-content p {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .loading-content h2 {
    font-size: 18px;
  }
} 