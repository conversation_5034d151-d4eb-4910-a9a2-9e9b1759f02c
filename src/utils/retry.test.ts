import { describe, expect, it, vi } from "vitest";

import { retryOperation } from "./retry";

describe("retryOperation", () => {
  it("should resolve if operation succeeds first time", async () => {
    const op = vi.fn().mockResolvedValue("ok");
    const result = await retryOperation(op);
    expect(result).toBe("ok");
    expect(op).toHaveBeenCalledTimes(1);
  });

  it("should retry if operation fails, then succeeds", async () => {
    const op = vi.fn();
    op.mockRejectedValueOnce(new Error("fail")).mockResolvedValueOnce("ok");
    const result = await retryOperation(op, 2, 1);
    expect(result).toBe("ok");
    expect(op).toHaveBeenCalledTimes(2);
  });

  it("should throw if operation always fails", async () => {
    const op = vi.fn().mockRejectedValue(new Error("fail"));
    await expect(retryOperation(op, 2, 1)).rejects.toThrow("fail");
    expect(op).toHaveBeenCalledTimes(2);
  });

  it("should wait between retries", async () => {
    const op = vi.fn();
    op.mockRejectedValueOnce(new Error("fail")).mockResolvedValueOnce("ok");
    const start = Date.now();
    const result = await retryOperation(op, 2, 50);
    const duration = Date.now() - start;
    expect(result).toBe("ok");
    expect(duration).toBeGreaterThanOrEqual(50);
  });
});
