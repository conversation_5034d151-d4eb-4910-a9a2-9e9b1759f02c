import { describe, expect, it } from "vitest";

import { formatCount, transformToDrawerData } from "./dataTransforms";

const mockVideoData = {
  series: [
    { status: "free", is_last_play: false, no: 1 },
    { status: "locked", is_last_play: false, no: 2 },
    { status: "paid", is_last_play: true, no: 3 },
    { status: "free", is_last_play: false, no: 4 },
  ],
};

describe("transformToDrawerData", () => {
  it("should return default values when videoData is null", () => {
    const result = transformToDrawerData(null);
    expect(result).toEqual({
      groups: [],
      activeGroupIndex: 0,
      current: 1,
      sections: [],
    });
  });

  it("should transform videoData to DrawerData", () => {
    const result = transformToDrawerData(mockVideoData as any);
    expect(result.groups.length).toBe(1);
    expect(result.groups[0].start).toBe(1);
    expect(result.groups[0].end).toBe(4);
    expect(result.groups[0].indexList).toEqual([1, 2, 3, 4]);
    expect(result.sections).toHaveLength(4);
    expect(result.sections[0].status).toBe("free");
    expect(result.sections[1].status).toBe("locked");
    expect(result.sections[2].status).toBe("paid");
    expect(result.current).toBe(3); // is_last_play
    expect(result.activeGroupIndex).toBe(0);
  });

  it("should use currentEpisode if provided", () => {
    const result = transformToDrawerData(mockVideoData as any, 2);
    expect(result.current).toBe(2);
    expect(result.activeGroupIndex).toBe(0);
  });

  it("should handle empty series", () => {
    const emptyData = { series: [] };
    const result = transformToDrawerData(emptyData as any);
    expect(result.groups).toEqual([]);
    expect(result.sections).toEqual([]);
    expect(result.current).toBe(1);
    expect(result.activeGroupIndex).toBe(0);
  });
});

describe("formatCount", () => {
  it("should handle invalid inputs", () => {
    expect(formatCount(NaN)).toBe("0");
    expect(formatCount(-1)).toBe("0");
    expect(formatCount(-100)).toBe("0");
  });

  it("should format numbers less than 1000", () => {
    expect(formatCount(0)).toBe("0");
    expect(formatCount(1)).toBe("1");
    expect(formatCount(100)).toBe("100");
    expect(formatCount(999)).toBe("999");
  });

  it("should format thousands with k", () => {
    expect(formatCount(1000)).toBe("1k");
    expect(formatCount(1500)).toBe("1.5k");
    expect(formatCount(9999)).toBe("10k"); // 边界值向上进位
    expect(formatCount(10000)).toBe("10k");
    expect(formatCount(15000)).toBe("15k");
    expect(formatCount(999999)).toBe("1m"); // 接近1m时向上进位
  });

  it("should format millions with m", () => {
    expect(formatCount(1000000)).toBe("1m");
    expect(formatCount(1500000)).toBe("1.5m");
    expect(formatCount(9999999)).toBe("10m"); // 边界值向上进位
    expect(formatCount(10000000)).toBe("10m");
    expect(formatCount(15000000)).toBe("15m");
    expect(formatCount(99999999)).toBe("99m"); // 实际值，不是100m
    expect(formatCount(999999999)).toBe("1b"); // 接近1b时向上进位
  });

  it("should format billions with b", () => {
    expect(formatCount(1000000000)).toBe("1b");
    expect(formatCount(1500000000)).toBe("1.5b");
    expect(formatCount(9999999999)).toBe("10b"); // 边界值向上进位
    expect(formatCount(10000000000)).toBe("10b");
    expect(formatCount(15000000000)).toBe("15b");
    expect(formatCount(99999999999)).toBe("100b");
    expect(formatCount(999999999999)).toBe("999b+"); // 超过阈值显示999b+
  });

  it("should handle very large numbers", () => {
    expect(formatCount(1000000000000)).toBe("999b+");
    expect(formatCount(999999999999999)).toBe("999b+");
  });

  it("should handle decimal precision correctly", () => {
    expect(formatCount(1500)).toBe("1.5k");
    expect(formatCount(1600)).toBe("1.6k");
    expect(formatCount(1000)).toBe("1k"); // 整数不显示小数点
    expect(formatCount(2000)).toBe("2k");
    expect(formatCount(2500)).toBe("2.5k");
    expect(formatCount(2500.5)).toBe("2.5k"); // 去除尾部零
  });

  it("should handle boundary cases", () => {
    // 测试接近阈值的边界情况
    expect(formatCount(999)).toBe("999");
    expect(formatCount(999.9)).toBe("999.9"); // 实际值，不满足进位条件
    expect(formatCount(999999)).toBe("1m"); // 非常接近1m时向上进位
    expect(formatCount(999999999)).toBe("1b"); // 非常接近1b时向上进位
  });
});
