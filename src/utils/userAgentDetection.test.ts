/**
 * @file userAgentDetection.test.ts
 * @description UA 检测工具的单元测试
 */

import { describe, expect, it } from "vitest";

import { getDeviceInfo, getFeatureSupport } from "./userAgentDetection";

describe("userAgentDetection", () => {
  describe("getDeviceInfo", () => {
    it("should return device information object", () => {
      const info = getDeviceInfo();

      expect(info).toBeDefined();
      expect(typeof info.isIOS).toBe("boolean");
      expect(typeof info.isAndroid).toBe("boolean");
      expect(typeof info.isHarmony).toBe("boolean");
      expect(typeof info.isMobile).toBe("boolean");
      expect(typeof info.isFacebook).toBe("boolean");
      expect(info.browser).toBeDefined();
      expect(typeof info.userAgent).toBe("string");
    });
  });

  describe("getFeatureSupport", () => {
    it("should return feature support information", () => {
      const support = getFeatureSupport();

      expect(support).toBeDefined();
      expect(typeof support.visualViewport).toBe("boolean");
      expect(typeof support.touchEvents).toBe("boolean");
      expect(typeof support.orientationChange).toBe("boolean");
      expect(typeof support.fullscreen).toBe("boolean");
    });
  });

  describe("basic functionality", () => {
    it("should not throw errors when called", () => {
      expect(() => getDeviceInfo()).not.toThrow();
      expect(() => getFeatureSupport()).not.toThrow();
    });
  });
});
