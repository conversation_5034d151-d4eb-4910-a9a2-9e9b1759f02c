import { moduleLoggers } from "@/utils/LogManager";

/**
 * @file urlParams.ts
 * @description URL 参数管理工具
 * - 处理 variant_id 参数的读取和更新
 * - 确保分享时 URL 携带单集 id 信息
 */

const VARIANT_ID_KEY = "variant_id";

/**
 * 初始化 URL 中的 variant_id 参数
 * @param episodes 剧集列表
 * @param currentEpisodeId 当前剧集ID
 * @returns 当前剧集索引，如果 URL 中的 variant_id 无效则返回 0
 */
export function initUrlVariantId(
  episodes: Array<{ id: string; no: number }>,
  currentEpisodeId: string,
): number {
  const url = new URL(window.location.href);
  const variantId = url.searchParams.get(VARIANT_ID_KEY);

  if (variantId) {
    const episodeIndex = episodes.findIndex((item) => item.id === variantId);
    if (episodeIndex !== -1) {
      moduleLoggers.urlParams.info(
        `从 URL 参数找到剧集索引: ${episodeIndex}, variant_id: ${variantId}`,
      );
      return episodeIndex;
    } else {
      moduleLoggers.urlParams.warn(
        `URL 中的 variant_id 无效: ${variantId}，使用默认索引 0`,
      );
      updateUrlVariantId(currentEpisodeId);
      return 0;
    }
  } else {
    moduleLoggers.urlParams.info(
      `URL 中没有 variant_id 参数，使用当前剧集ID: ${currentEpisodeId}`,
    );
    updateUrlVariantId(currentEpisodeId);
    return 0;
  }
}

/**
 * 更新 URL 中的 variant_id 参数
 * @param variantId 剧集ID
 */
export function updateUrlVariantId(variantId: string): void {
  moduleLoggers.urlParams.info(`更新 URL variant_id: ${variantId}`);
  const url = new URL(window.location.href);

  if (url.searchParams.get("variant_id") !== variantId) {
    // 使用 replace 方法更新 URL 参数，不修改历史记录
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set("variant_id", variantId);
    window.history.replaceState(null, "", newUrl.toString());
    moduleLoggers.urlParams.info(`URL 参数更新成功: ${newUrl.toString()}`);
  } else {
    moduleLoggers.urlParams.info(`URL 参数已是最新: ${variantId}`);
  }
}

/**
 * 获取当前 URL 中的 variant_id 参数
 * @returns variant_id 参数值，如果不存在则返回 null
 */
export function getCurrentVariantId(): string | null {
  const url = new URL(window.location.href);
  return url.searchParams.get(VARIANT_ID_KEY);
}

/**
 * 根据剧集编号获取对应的 variant_id
 * @param episodes 剧集列表
 * @param episodeNo 剧集编号
 * @returns 对应的 variant_id，如果找不到则返回 null
 */
export function getVariantIdByEpisodeNo(
  episodes: Array<{ id: string; no: number }>,
  episodeNo: number,
): string | null {
  const episode = episodes.find((ep) => ep.no === episodeNo);
  return episode ? episode.id : null;
}

/**
 * 根据 variant_id 获取对应的剧集编号
 * @param episodes 剧集列表
 * @param variantId 剧集ID
 * @returns 对应的剧集编号，如果找不到则返回 null
 */
export function getEpisodeNoByVariantId(
  episodes: Array<{ id: string; no: number }>,
  variantId: string,
): number | null {
  const episode = episodes.find((ep) => ep.id === variantId);
  return episode ? episode.no : null;
}
