import { LogLevel, LogManager } from "./LogManager";

/**
 * 日志配置管理
 * 用于在不同环境下设置不同的日志级别和配置
 */

/**
 * 开发环境日志配置
 */
const devConfig = {
  level: LogLevel.DEBUG,
  enableConsole: true,
  enableProductionLogs: false,
  prefix: "[ShortPlayer]",
};

/**
 * 生产环境日志配置
 */
const prodConfig = {
  level: LogLevel.WARN,
  enableConsole: true,
  enableProductionLogs: true,
  prefix: "[ShortPlayer]",
};

/**
 * 测试环境日志配置
 */
const testConfig = {
  level: LogLevel.WARN,
  enableConsole: true,
  enableProductionLogs: false,
  prefix: "[ShortPlayer]",
};

/**
 * 将日志管理器暴露到window对象，方便生产环境调试
 */
function exposeLoggerToWindow(): void {
  const logger = LogManager.getInstance();

  // 只在开发环境或明确启用生产日志时暴露
  if (!import.meta.env.PROD || logger.getConfig().enableProductionLogs) {
    (window as any).shortPlayerLogger = {
      // 基本日志方法
      error: (message: string, ...args: any[]) =>
        logger.error(message, ...args),
      warn: (message: string, ...args: any[]) => logger.warn(message, ...args),
      info: (message: string, ...args: any[]) => logger.info(message, ...args),
      debug: (message: string, ...args: any[]) =>
        logger.debug(message, ...args),
      trace: (message: string, ...args: any[]) =>
        logger.trace(message, ...args),

      // 配置方法
      getConfig: () => logger.getConfig(),
      updateConfig: (config: any) => logger.updateConfig(config),
      setLogLevel: (level: LogLevel) => {
        logger.updateConfig({ level });
        logger.info(`日志级别已设置为: ${LogLevel[level]}`);
      },

      // 生产环境控制方法
      enableProductionLogs: () => {
        logger.updateConfig({
          enableProductionLogs: true,
          level: LogLevel.DEBUG,
        });
        logger.info("已启用生产环境详细日志输出");
      },
      disableProductionLogs: () => {
        logger.updateConfig({
          enableProductionLogs: false,
          level: LogLevel.ERROR,
        });
        logger.info("已禁用生产环境详细日志输出");
      },

      // 便捷方法
      enableAllLogs: () => {
        logger.updateConfig({
          enableProductionLogs: true,
          level: LogLevel.TRACE,
        });
        logger.info("已启用所有日志输出");
      },
      disableAllLogs: () => {
        logger.updateConfig({
          enableProductionLogs: false,
          level: LogLevel.ERROR,
        });
        logger.info("已禁用所有日志输出（仅保留错误日志）");
      },

      // 模块日志器
      modules: {
        PlayerInstance: {
          error: (message: string, ...args: any[]) =>
            logger.module("PlayerInstance", LogLevel.ERROR, message, ...args),
          warn: (message: string, ...args: any[]) =>
            logger.module("PlayerInstance", LogLevel.WARN, message, ...args),
          info: (message: string, ...args: any[]) =>
            logger.module("PlayerInstance", LogLevel.INFO, message, ...args),
          debug: (message: string, ...args: any[]) =>
            logger.module("PlayerInstance", LogLevel.DEBUG, message, ...args),
          trace: (message: string, ...args: any[]) =>
            logger.module("PlayerInstance", LogLevel.TRACE, message, ...args),
        },
        StateManager: {
          error: (message: string, ...args: any[]) =>
            logger.module("StateManager", LogLevel.ERROR, message, ...args),
          warn: (message: string, ...args: any[]) =>
            logger.module("StateManager", LogLevel.WARN, message, ...args),
          info: (message: string, ...args: any[]) =>
            logger.module("StateManager", LogLevel.INFO, message, ...args),
          debug: (message: string, ...args: any[]) =>
            logger.module("StateManager", LogLevel.DEBUG, message, ...args),
          trace: (message: string, ...args: any[]) =>
            logger.module("StateManager", LogLevel.TRACE, message, ...args),
        },
        App: {
          error: (message: string, ...args: any[]) =>
            logger.module("App", LogLevel.ERROR, message, ...args),
          warn: (message: string, ...args: any[]) =>
            logger.module("App", LogLevel.WARN, message, ...args),
          info: (message: string, ...args: any[]) =>
            logger.module("App", LogLevel.INFO, message, ...args),
          debug: (message: string, ...args: any[]) =>
            logger.module("App", LogLevel.DEBUG, message, ...args),
          trace: (message: string, ...args: any[]) =>
            logger.module("App", LogLevel.TRACE, message, ...args),
        },
      },

      // 日志级别常量
      LogLevel,
    };

    if (!import.meta.env.PROD) {
      logger.info("日志管理器已暴露到 window.shortPlayerLogger");
    }
  }
}

/**
 * 根据环境初始化日志配置
 */
export function initializeLogConfig(): void {
  const logger = LogManager.getInstance();
  const isProduction = import.meta.env.PROD;
  const isTest = import.meta.env.MODE === "test";

  let config;
  if (isTest) {
    config = testConfig;
  } else if (isProduction) {
    config = prodConfig;
  } else {
    config = devConfig;
  }

  logger.updateConfig(config);

  // 暴露日志管理器到window对象
  exposeLoggerToWindow();

  // 输出初始化信息
  if (!isProduction) {
    logger.info(
      `日志管理器初始化完成 - 环境: ${import.meta.env.MODE}, 级别: ${LogLevel[config.level]}`,
    );
  }
}

/**
 * 启用生产环境日志（用于调试）
 * 可以通过 URL 参数或 localStorage 控制
 */
export function enableProductionLogs(): void {
  const logger = LogManager.getInstance();
  logger.updateConfig({
    enableProductionLogs: true,
    level: LogLevel.DEBUG,
  });
  logger.info("已启用生产环境详细日志输出");

  // 重新暴露到window对象
  exposeLoggerToWindow();
}

/**
 * 禁用生产环境日志
 */
export function disableProductionLogs(): void {
  const logger = LogManager.getInstance();
  logger.updateConfig({
    enableProductionLogs: false,
    level: LogLevel.ERROR,
  });
  logger.info("已禁用生产环境详细日志输出");
}

/**
 * 设置日志级别
 */
export function setLogLevel(level: LogLevel): void {
  const logger = LogManager.getInstance();
  logger.updateConfig({ level });
  logger.info(`日志级别已设置为: ${LogLevel[level]}`);
}

/**
 * 检查是否应该启用生产环境日志
 * 可以通过 URL 参数 ?debug=true 或 localStorage 中的 debug 标志来控制
 */
export function checkProductionLogsFlag(): void {
  if (!import.meta.env.PROD) return;

  // 检查 URL 参数
  const urlParams = new URLSearchParams(window.location.search);
  const debugParam = urlParams.get("debug");

  // 检查 localStorage
  const debugStorage = localStorage.getItem("short-player-debug");

  if (debugParam === "true" || debugStorage === "true") {
    enableProductionLogs();

    // 如果通过 URL 参数启用，保存到 localStorage
    if (debugParam === "true") {
      localStorage.setItem("short-player-debug", "true");
    }
  }
}

/**
 * 导出配置常量
 */
export const LOG_CONFIG = {
  DEV: devConfig,
  PROD: prodConfig,
  TEST: testConfig,
};
