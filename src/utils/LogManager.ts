/**
 * 日志级别枚举
 */
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
  TRACE = 4,
}

/**
 * 日志配置接口
 */
export interface LogConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableProductionLogs: boolean;
  prefix?: string;
}

/**
 * 日志管理器类
 * 用于统一管理项目中的日志输出，支持不同环境下的日志控制
 */
export class LogManager {
  private static instance: LogManager;
  private config: LogConfig;
  private isProduction: boolean;

  private constructor() {
    this.isProduction = import.meta.env.PROD;

    // 默认配置
    this.config = {
      level: this.isProduction ? LogLevel.ERROR : LogLevel.DEBUG,
      enableConsole: true,
      enableProductionLogs: false, // 生产环境默认不输出日志
      prefix: "[ShortPlayer]",
    };
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  /**
   * 更新日志配置
   */
  public updateConfig(config: Partial<LogConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  public getConfig(): LogConfig {
    return { ...this.config };
  }

  /**
   * 检查是否应该输出指定级别的日志
   */
  private shouldLog(level: LogLevel): boolean {
    // 生产环境且未启用生产日志时，只输出错误日志
    if (
      this.isProduction &&
      !this.config.enableProductionLogs &&
      level !== LogLevel.ERROR
    ) {
      return false;
    }

    return this.config.enableConsole && level <= this.config.level;
  }

  /**
   * 格式化日志消息
   */
  private formatMessage(
    level: string,
    message: string,
    ...args: any[]
  ): string {
    const timestamp = new Date().toISOString();
    const prefix = this.config.prefix || "[ShortPlayer]";
    return `${prefix} [${level}] ${message}`;
  }

  /**
   * 输出错误日志
   */
  public error(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.ERROR)) {
      const formattedMessage = this.formatMessage("ERROR", message);
      console.error(formattedMessage, ...args);
    }
  }

  /**
   * 输出警告日志
   */
  public warn(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.WARN)) {
      const formattedMessage = this.formatMessage("WARN", message);
      console.warn(formattedMessage, ...args);
    }
  }

  /**
   * 输出信息日志
   */
  public info(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.INFO)) {
      const formattedMessage = this.formatMessage("INFO", message);
      console.log(formattedMessage, ...args);
    }
  }

  /**
   * 输出调试日志
   */
  public debug(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      const formattedMessage = this.formatMessage("DEBUG", message);
      console.log(formattedMessage, ...args);
    }
  }

  /**
   * 输出跟踪日志
   */
  public trace(message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.TRACE)) {
      const formattedMessage = this.formatMessage("TRACE", message);
      console.log(formattedMessage, ...args);
    }
  }

  /**
   * 输出带模块前缀的日志
   */
  public module(
    moduleName: string,
    level: LogLevel,
    message: string,
    ...args: any[]
  ): void {
    if (this.shouldLog(level)) {
      const timestamp = new Date().toISOString();
      const prefix = this.config.prefix || "[ShortPlayer]";
      const levelName = LogLevel[level];
      const formattedMessage = `${prefix} [${moduleName}] [${levelName}] ${message}`;

      switch (level) {
        case LogLevel.ERROR:
          console.error(formattedMessage, ...args);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage, ...args);
          break;
        default:
          console.log(formattedMessage, ...args);
      }
    }
  }

  /**
   * 输出带表情符号的日志（用于开发环境）
   */
  public emoji(emoji: string, message: string, ...args: any[]): void {
    if (this.shouldLog(LogLevel.INFO) && !this.isProduction) {
      const formattedMessage = this.formatMessage(
        "INFO",
        `${emoji} ${message}`,
      );
      console.log(formattedMessage, ...args);
    }
  }

  /**
   * 输出分组日志
   */
  public group(label: string, collapsed: boolean = false): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      if (collapsed) {
        console.groupCollapsed(label);
      } else {
        console.group(label);
      }
    }
  }

  /**
   * 结束分组日志
   */
  public groupEnd(): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.groupEnd();
    }
  }

  /**
   * 输出表格日志
   */
  public table(data: any): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.table(data);
    }
  }

  /**
   * 输出时间日志
   */
  public time(label: string): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.time(label);
    }
  }

  /**
   * 结束时间日志
   */
  public timeEnd(label: string): void {
    if (this.shouldLog(LogLevel.DEBUG)) {
      console.timeEnd(label);
    }
  }

  /**
   * 清理日志管理器
   */
  public destroy(): void {
    // 清理资源
    LogManager.instance = null as any;
  }
}

/**
 * 创建模块日志器
 * 为特定模块创建带前缀的日志器
 */
export function createModuleLogger(moduleName: string) {
  const logManager = LogManager.getInstance();

  return {
    error: (message: string, ...args: any[]) =>
      logManager.module(moduleName, LogLevel.ERROR, message, ...args),
    warn: (message: string, ...args: any[]) =>
      logManager.module(moduleName, LogLevel.WARN, message, ...args),
    info: (message: string, ...args: any[]) =>
      logManager.module(moduleName, LogLevel.INFO, message, ...args),
    debug: (message: string, ...args: any[]) =>
      logManager.module(moduleName, LogLevel.DEBUG, message, ...args),
    trace: (message: string, ...args: any[]) =>
      logManager.module(moduleName, LogLevel.TRACE, message, ...args),
    emoji: (emoji: string, message: string, ...args: any[]) => {
      if (logManager.getConfig().enableConsole && !import.meta.env.PROD) {
        const formattedMessage = `${logManager.getConfig().prefix || "[ShortPlayer]"} [${moduleName}] [INFO] ${emoji} ${message}`;
        console.log(formattedMessage, ...args);
      }
    },
  };
}

/**
 * 全局日志管理器实例
 */
export const logger = LogManager.getInstance();

/**
 * 导出常用的模块日志器
 */
export const moduleLoggers = {
  PlayerInstance: createModuleLogger("PlayerInstance"),
  PlayerFactory: createModuleLogger("PlayerFactory"),
  StateManager: createModuleLogger("StateManager"),
  VideoPlayerManager: createModuleLogger("VideoPlayerManager"),
  MultiPlayer: createModuleLogger("MultiPlayer"),
  Compatibility: createModuleLogger("Compatibility"),
  NetworkManager: createModuleLogger("NetworkManager"),
  NetworkStateManager: createModuleLogger("NetworkStateManager"),
  StatisticsManager: createModuleLogger("StatisticsManager"),
  EventManager: createModuleLogger("EventManager"),
  TrackingManager: createModuleLogger("TrackingManager"),
  AuthManager: createModuleLogger("AuthManager"),
  GestureManager: createModuleLogger("GestureManager"),
  CustomHlsJsPlugin: createModuleLogger("CustomHlsJsPlugin"),
  PlayerEventHandler: createModuleLogger("PlayerEventHandler"),
  LifecycleController: createModuleLogger("LifecycleController"),
  App: createModuleLogger("App"),
  usePlayerControls: createModuleLogger("usePlayerControls"),
  PlayerOverlay: createModuleLogger("PlayerOverlay"),
  SectionsDrawer: createModuleLogger("SectionsDrawer"),
  ShortDramaPlayer: createModuleLogger("ShortDramaPlayer"),
  LockedEpisodeCover: createModuleLogger("LockedEpisodeCover"),
  urlParams: createModuleLogger("urlParams"),
  useEpisodeManager: createModuleLogger("useEpisodeManager"),
  PreloadManager: createModuleLogger("PreloadManager"),
  InstanceLifecycleManager: createModuleLogger("InstanceLifecycleManager"),
};
