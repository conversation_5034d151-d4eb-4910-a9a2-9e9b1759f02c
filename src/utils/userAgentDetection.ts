/**
 * @file userAgentDetection.ts
 * @description 用户代理检测工具
 * 提供统一的设备和浏览器检测功能
 */

/**
 * 检测是否为 iOS 设备
 * 包括 iPad、iPhone、iPod
 * @returns 是否为 iOS 设备
 */
export const isIOSDevice = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  return (
    /iPad|iPhone|iPod/.test(navigator.userAgent) && !(window as any).MSStream
  );
};

/**
 * 检测是否为 Harmony 设备（鸿蒙系统）
 * @returns 是否为 Harmony 设备
 */
export const isHarmonyDevice = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  return /Harmony/.test(navigator.userAgent);
};

/**
 * 检测是否在 Facebook 内置浏览器中
 * 支持多种 Facebook 应用的内置浏览器标识
 * @returns 是否在 Facebook 浏览器中
 */
export const isFacebookBrowser = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  // Facebook 内置浏览器的多种标识
  // FBAN: Facebook App for Android
  // FBAV: Facebook App Version
  // FB_IAB: Facebook In-App Browser
  // FB4A: Facebook for Android
  // FBIOS: Facebook for iOS
  // FBBV: Facebook Browser Version
  return /FBAN|FBAV|FB_IAB|FB4A|FBIOS|FBBV/.test(navigator.userAgent);
};

/**
 * 检测是否为 Android 设备
 * @returns 是否为 Android 设备
 */
export const isAndroidDevice = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  return /Android/.test(navigator.userAgent);
};

/**
 * 检测是否为移动设备
 * @returns 是否为移动设备
 */
export const isMobileDevice = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent,
  );
};

/**
 * 检测是否为平板设备
 * @returns 是否为平板设备
 */
export const isTabletDevice = (): boolean => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return false;
  }

  return /iPad|Android(?=.*Mobile)|Tablet/i.test(navigator.userAgent);
};

/**
 * 检测浏览器类型
 * @returns 浏览器类型信息
 */
export const getBrowserInfo = () => {
  if (typeof window === "undefined" || typeof navigator === "undefined") {
    return {
      name: "unknown",
      version: "unknown",
      isChrome: false,
      isSafari: false,
      isFirefox: false,
      isEdge: false,
    };
  }

  const userAgent = navigator.userAgent;

  const isChrome = /Chrome/.test(userAgent) && !/Edge/.test(userAgent);
  const isSafari = /Safari/.test(userAgent) && !/Chrome/.test(userAgent);
  const isFirefox = /Firefox/.test(userAgent);
  const isEdge = /Edge/.test(userAgent);

  let name = "unknown";
  let version = "unknown";

  if (isChrome) {
    name = "Chrome";
    const match = userAgent.match(/Chrome\/(\d+)/);
    version = match ? match[1] : "unknown";
  } else if (isSafari) {
    name = "Safari";
    const match = userAgent.match(/Version\/(\d+)/);
    version = match ? match[1] : "unknown";
  } else if (isFirefox) {
    name = "Firefox";
    const match = userAgent.match(/Firefox\/(\d+)/);
    version = match ? match[1] : "unknown";
  } else if (isEdge) {
    name = "Edge";
    const match = userAgent.match(/Edge\/(\d+)/);
    version = match ? match[1] : "unknown";
  }

  return {
    name,
    version,
    isChrome,
    isSafari,
    isFirefox,
    isEdge,
  };
};

/**
 * 获取完整的设备和浏览器信息
 * @returns 完整的检测结果
 */
export const getDeviceInfo = () => {
  const browserInfo = getBrowserInfo();

  return {
    // 设备类型
    isIOS: isIOSDevice(),
    isAndroid: isAndroidDevice(),
    isHarmony: isHarmonyDevice(),
    isMobile: isMobileDevice(),
    isTablet: isTabletDevice(),

    // 浏览器环境
    isFacebook: isFacebookBrowser(),
    browser: browserInfo,

    // 原始信息
    userAgent: typeof navigator !== "undefined" ? navigator.userAgent : "",

    // 屏幕信息
    screen:
      typeof window !== "undefined"
        ? {
            width: window.screen?.width || 0,
            height: window.screen?.height || 0,
            availWidth: window.screen?.availWidth || 0,
            availHeight: window.screen?.availHeight || 0,
          }
        : null,

    // 视口信息
    viewport:
      typeof window !== "undefined"
        ? {
            width: window.innerWidth || 0,
            height: window.innerHeight || 0,
          }
        : null,
  };
};

/**
 * 检测是否支持特定功能
 * @returns 功能支持情况
 */
export const getFeatureSupport = () => {
  if (typeof window === "undefined") {
    return {
      visualViewport: false,
      touchEvents: false,
      orientationChange: false,
      fullscreen: false,
    };
  }

  return {
    // Visual Viewport API 支持
    visualViewport: "visualViewport" in window,

    // 触摸事件支持
    touchEvents: "ontouchstart" in window,

    // 屏幕方向变化支持
    orientationChange: "onorientationchange" in window,

    // 全屏 API 支持
    fullscreen: !!(
      document.fullscreenEnabled ||
      (document as any).webkitFullscreenEnabled ||
      (document as any).mozFullScreenEnabled ||
      (document as any).msFullscreenEnabled
    ),
  };
};

// 导出常用的检测结果作为常量（用于向后兼容）
export const UA_DETECTION = {
  get isIOS() {
    return isIOSDevice();
  },
  get isHarmony() {
    return isHarmonyDevice();
  },
  get isFacebook() {
    return isFacebookBrowser();
  },
  get isAndroid() {
    return isAndroidDevice();
  },
  get isMobile() {
    return isMobileDevice();
  },
};

// 默认导出
export default {
  isIOSDevice,
  isHarmonyDevice,
  isFacebookBrowser,
  isAndroidDevice,
  isMobileDevice,
  isTabletDevice,
  getBrowserInfo,
  getDeviceInfo,
  getFeatureSupport,
  UA_DETECTION,
};
