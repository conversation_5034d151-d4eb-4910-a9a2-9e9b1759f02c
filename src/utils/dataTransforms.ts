import { IPlayerVideoData } from "@/services/AuthManager";

// 声明数据类型接口
interface SectionData {
  status?: "locked" | "paid" | "free";
}

interface GroupData {
  start: number;
  end: number;
  indexList: number[];
}

export interface DrawerData {
  groups: GroupData[];
  activeGroupIndex: number;
  current: number;
  sections: SectionData[];
}

/**
 * 将 IPlayerVideoData 转换为 DrawerData 格式
 */
export function transformToDrawerData(
  videoData: IPlayerVideoData | null,
  currentEpisode?: number,
): DrawerData {
  if (!videoData) {
    return {
      groups: [],
      activeGroupIndex: 0,
      current: 1,
      sections: [],
    };
  }

  // 转换状态映射
  const sections: SectionData[] = videoData.series.map((episode) => ({
    status: episode.status,
  }));

  // 创建分组逻辑 - 可以根据实际需求调整
  const groupSize = 30; // 每组30集
  const groups: GroupData[] = [];

  for (let i = 0; i < sections.length; i += groupSize) {
    const end = Math.min(i + groupSize - 1, sections.length - 1);
    groups.push({
      start: i + 1,
      end: end + 1,
      indexList: Array.from({ length: end - i + 1 }, (_, idx) => i + idx + 1),
    });
  }

  // 确定当前播放的集数
  let currentEpisodeIndex: number;
  if (currentEpisode !== undefined) {
    // 优先使用传入的currentEpisode参数
    currentEpisodeIndex = currentEpisode;
  } else {
    // 回退到查找is_last_play标记
    currentEpisodeIndex =
      videoData.series.find((episode) => episode.is_last_play)?.no || 1;
  }

  const current = currentEpisodeIndex >= 0 ? currentEpisodeIndex : 1;
  const activeGroupIndex = Math.floor(
    (currentEpisodeIndex >= 0 ? currentEpisodeIndex : 0) / groupSize,
  );

  return {
    groups,
    activeGroupIndex,
    current,
    sections,
  };
}

/**
 * 格式化数字显示，将大数字转换为带单位的形式
 * 例如：1000 -> 1k, 1000k -> 1m, 1000m -> 1b
 * @param {number} num - 要格式化的数字
 * @returns {string} 格式化后的数字字符串
 */
export function formatCount(num: number): string {
  // 处理非数字类型输入和负数
  if (typeof num !== 'number' || isNaN(num)) return '0';
  if (num < 0) num = 0;

  // 处理边界值，如果非常接近下一个单位，向上进位
  const thresholds = [1e3, 1e6, 1e9, 1e12];
  for (let i = 0; i < thresholds.length; i++) {
    const threshold = thresholds[i];
    if (num > threshold * 0.9999 && num < threshold) {
      num = threshold;
      break;
    }
  }

  // 定义单位和阈值
  const units = [
    { threshold: 1e12, divisor: 1e9, unit: 'b', maxValue: '999b+' },
    { threshold: 1e11, divisor: 1e9, unit: 'b', useFloor: true },
    { threshold: 1e9, divisor: 1e9, unit: 'b' },
    { threshold: 1e7, divisor: 1e6, unit: 'm', useFloor: true },
    { threshold: 1e6, divisor: 1e6, unit: 'm' },
    { threshold: 1e3, divisor: 1e3, unit: 'k' }
  ];

  // 根据数值大小选择适当的单位进行格式化
  for (const { threshold, divisor, unit, maxValue, useFloor } of units) {
    if (num >= threshold) {
      if (maxValue) return maxValue;

      const value = num / divisor;
      if (useFloor) {
        return `${Math.floor(value)}${unit}`;
      }

      // 如果是整数则不显示小数点，否则保留一位小数并去除尾部的零
      return `${value % 1 === 0 ? value : value.toFixed(1).replace(/\.0$/, '')}${unit}`;
    }
  }

  // 小于1000的数字直接返回
  return num.toString();
}
