import { useCallback, useEffect, useRef } from "react";

import { PlayerFactory } from "@/core/factories/PlayerFactory";
import { EventManager, PlayerEvents } from "@/core/managers/EventManager";
import { InstanceLifecycleManager } from "@/core/managers/InstanceLifecycleManager";
import { PreloadManager } from "@/core/managers/PreloadManager";
import { StateManager } from "@/core/managers/StateManager";
import { TrackingManager } from "@/services/TrackingManager";
import { moduleLoggers } from "@/utils/LogManager";

interface EpisodeManagerOptions {
  playerFactory: PlayerFactory;
  stateManager: StateManager;
  activePlayerId: string | null;
  instanceLifecycleManager: InstanceLifecycleManager;
  handlePlayerEnded: (playerId: string, currentEpisodeNo: number) => void;
  onSwitchEpisode?: (episodeNo: number) => void;
}

export const useEpisodeManager = ({
  playerFactory,
  stateManager,
  activePlayerId,
  instanceLifecycleManager,
  handlePlayerEnded,
}: EpisodeManagerOptions) => {
  const timeUpdateListeners = useRef<
    Map<string, (event: PlayerEvents["timeUpdate"]) => void>
  >(new Map());

  // 使用 useRef 来存储播放结束回调，避免循环依赖
  const handlePlayerEndedRef =
    useRef<(playerId: string, currentEpisodeNo: number) => void>();

  // 添加防重复调用机制
  const isSwitchingRef = useRef(false);
  const lastSwitchTimeRef = useRef(0);

  // 包装函数，用于传递给播放器
  const handlePlayerEndedWrapper = useCallback(
    (playerId: string, currentEpisodeNo: number) => {
      if (handlePlayerEndedRef.current) {
        handlePlayerEndedRef.current(playerId, currentEpisodeNo);
      }
    },
    [],
  );

  // 设置全局的 handlePlayerEnded 函数
  useEffect(() => {
    handlePlayerEndedRef.current = handlePlayerEnded;
  }, [handlePlayerEnded]);

  const switchEpisode = useCallback(
    (episodeNo: number) => {
      // 防重复调用检查
      const now = Date.now();
      if (isSwitchingRef.current || now - lastSwitchTimeRef.current < 1000) {
        moduleLoggers.useEpisodeManager.info(
          `🎯 [EpisodeManager] 防重复调用: 正在切换中或间隔过短，跳过切换到第 ${episodeNo} 集`,
        );
        return;
      }

      isSwitchingRef.current = true;
      lastSwitchTimeRef.current = now;

      moduleLoggers.useEpisodeManager.info(
        `🎯 [EpisodeManager] 开始切换到第 ${episodeNo} 集`,
      );

      const activePlayer = stateManager.getActiveInstance();

      // 在切换前，检查上一个视频的播放时长
      if (activePlayer) {
        const activePlayerInstance = playerFactory.getPlayer(activePlayer.id);
        const oldEpisode = activePlayer.currentEpisode;

        if (
          oldEpisode &&
          oldEpisode.no !== episodeNo &&
          activePlayerInstance?.xgplayer
        ) {
          // 优先使用状态管理器中的 cumulatedWatchedTime，如果为空则使用播放器的 cumulateTime
          const cumulatedWatchedTime =
            activePlayer.cumulatedWatchedTime ??
            activePlayerInstance.xgplayer.cumulateTime ??
            0;

          if (cumulatedWatchedTime < 10 * 1000) {
            moduleLoggers.useEpisodeManager.info(
              `⏱️ [Tracking] Manager: 播放时间 < 10s (切换): ${cumulatedWatchedTime.toFixed(
                2,
              )}s, 上报 tenSecExitPlay`,
            );
            const trackingManager = TrackingManager.getInstance();
            if (activePlayer.videoData) {
              trackingManager.track10ExitPlay(
                activePlayer.videoData,
                oldEpisode,
              );
            }
          }
        }
      }

      if (!activePlayer?.videoData) {
        moduleLoggers.useEpisodeManager.warn("没有活跃的播放器实例");
        isSwitchingRef.current = false;
        return;
      }

      // 使用实例生命周期管理器进行剧集切换，传递全局的 onEnded 处理函数
      instanceLifecycleManager.switchEpisode(episodeNo, handlePlayerEnded);

      // 延迟重置切换状态
      setTimeout(() => {
        isSwitchingRef.current = false;
        moduleLoggers.useEpisodeManager.info(
          `🎯 [EpisodeManager] 切换到第 ${episodeNo} 集完成`,
        );
      }, 500);
    },
    [stateManager, playerFactory, instanceLifecycleManager, handlePlayerEnded],
  );

  // 播放结束回调
  handlePlayerEndedRef.current = useCallback(
    (playerId: string, currentEpisodeNo: number) => {
      if (playerId === activePlayerId) {
        const activePlayer = stateManager.getActiveInstance();
        if (!activePlayer?.videoData || !activePlayer.currentEpisode) return;

        const nextEpisodeNo = currentEpisodeNo + 1;
        if (nextEpisodeNo <= activePlayer.videoData.series.length) {
          setTimeout(() => switchEpisode(nextEpisodeNo), 500);
        }
      }
    },
    [activePlayerId, stateManager, switchEpisode],
  );

  const switchToNextEpisode = useCallback(() => {
    const activePlayer = stateManager.getActiveInstance();
    if (!activePlayer?.videoData || !activePlayer.currentEpisode) return;
    const nextEpisodeNo = activePlayer.currentEpisode.no + 1;
    if (nextEpisodeNo <= activePlayer.videoData.series.length) {
      switchEpisode(nextEpisodeNo);
    }
  }, [stateManager, switchEpisode]);

  // 清理函数
  useEffect(() => {
    return () => {
      timeUpdateListeners.current.forEach((listener, playerId) => {
        EventManager.getInstance().off("timeUpdate", listener);
      });
      timeUpdateListeners.current.clear();
    };
  }, []);

  return {
    switchEpisode,
    switchToNextEpisode,
    handlePlayerEndedWrapper,
  };
};
