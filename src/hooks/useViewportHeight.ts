import { useCallback, useEffect, useState } from "react";

import { moduleLoggers } from "@/utils/LogManager";

import {
  isHarmony,
  isIOS,
  viewportHeightCompatibility,
} from "../compatibility/ViewportHeightCompatibility";

// 全局初始化状态，避免多个组件重复初始化
let globalInitialized = false;

/**
 * 视口高度兼容性Hook
 * 用于在React组件中管理iOS Safari和HarmonyOS的视口高度适配
 */
export function useViewportHeight() {
  const [viewportHeight, setViewportHeight] = useState<number>(
    window.innerHeight,
  );
  const [isCompatibilityEnabled, setIsCompatibilityEnabled] = useState<boolean>(
    isIOS || isHarmony,
  );

  // 初始化兼容性模块
  useEffect(() => {
    if (isCompatibilityEnabled && !globalInitialized) {
      moduleLoggers.Compatibility.info("初始化视口高度兼容性Hook");
      viewportHeightCompatibility.init();
      globalInitialized = true;
    }
  }, [isCompatibilityEnabled]);

  // 监听视口高度变化
  useEffect(() => {
    if (!isCompatibilityEnabled) return;

    const removeListener =
      viewportHeightCompatibility.addViewportHeightListener((vh: number) => {
        setViewportHeight(vh);
        // moduleLoggers.Compatibility.debug("视口高度已更新", { vh });
      });

    // 设置初始值
    setViewportHeight(viewportHeightCompatibility.getCurrentVH());

    return () => {
      removeListener();
    };
  }, [isCompatibilityEnabled]);

  // 手动更新视口高度
  const updateViewportHeight = useCallback(() => {
    if (isCompatibilityEnabled) {
      viewportHeightCompatibility.updateViewportHeight();
    }
  }, [isCompatibilityEnabled]);

  // 获取设备信息
  const getDeviceInfo = useCallback(() => {
    return viewportHeightCompatibility.getDeviceInfo();
  }, []);

  // 清理资源
  useEffect(() => {
    return () => {
      if (isCompatibilityEnabled) {
        // moduleLoggers.Compatibility.info("清理视口高度兼容性Hook");
        // 注意：这里不调用cleanup，因为可能有其他组件在使用
        // 只清理当前组件的监听器，不清理全局状态
      }
    };
  }, [isCompatibilityEnabled]);

  return {
    viewportHeight,
    isCompatibilityEnabled,
    isIOS,
    isHarmony,
    updateViewportHeight,
    getDeviceInfo,
  };
}

/**
 * 获取CSS变量形式的视口高度
 * 用于在CSS中使用
 */
export function useViewportHeightCSS() {
  const { viewportHeight } = useViewportHeight();

  return {
    "--csp-product-vh": `${viewportHeight * 0.01}px`,
    height: `${viewportHeight}px`,
  };
}

// 导出重置函数，用于在重置过程中清理全局状态
export function resetViewportHeightGlobalState() {
  globalInitialized = false;
}
