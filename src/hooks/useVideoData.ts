import { useEffect, useState } from "react";

import { useCore } from "@/contexts/CoreProvider";
import { IPlayerVideoData } from "@/services/AuthManager";

export const useVideoData = () => {
  const { stateManager } = useCore();

  const [videoData, setVideoData] = useState<IPlayerVideoData | null>(null);

  useEffect(() => {
    const unsubscribe = stateManager.subscribeToGlobalState((globalState) => {
      setVideoData(globalState.videoDetail);
    });

    return () => {
      unsubscribe();
    };
  }, [stateManager]);

  return videoData;
};
