/**
 * @file PlayerInstance.test.ts
 * @description PlayerInstance 的单元测试，特别是 startTime 重置功能的测试
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { PlayerInstance, PlayerInstanceConfig } from '../core/instance/PlayerInstance';
import { EventManager } from '../core/managers/EventManager';
import { StateManager } from '../core/managers/StateManager';
import { IPlayerVideoData, IPlayerEpisode } from '../services/AuthManager';

// Mock dependencies
vi.mock('../core/managers/StateManager');
vi.mock('../core/managers/EventManager');
vi.mock('../services/AuthManager');
vi.mock('../services/TrackingManager');
vi.mock('../core/managers/NetworkStateManager');
vi.mock('../utils/LogManager');

// Mock XGPlayer
vi.mock('xgplayer', () => ({
  default: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    pause: vi.fn(),
    play: vi.fn(),
    seek: vi.fn(),
    once: vi.fn(),
    video: {},
    src: '',
    currentTime: 0,
    paused: false,
    plugins: [],
  })),
  Events: {
    LOADED_DATA: 'loadeddata',
  },
}));

vi.mock('xgplayer-subtitles', () => ({
  default: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
  })),
}));

describe('PlayerInstance startTime 重置功能', () => {
  let playerInstance: PlayerInstance;
  let mockEventManager: any;
  let mockStateManager: any;
  let mockVideoData: IPlayerVideoData;
  let mockEpisode1: IPlayerEpisode;
  let mockEpisode2: IPlayerEpisode;

  beforeEach(() => {
    // 重置所有 mocks
    vi.clearAllMocks();

    // Mock DOM
    const mockContainer = document.createElement('div');
    mockContainer.id = 'test-container';
    document.body.appendChild(mockContainer);

    // Mock EventManager
    mockEventManager = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    };

    // Mock StateManager
    mockStateManager = {
      getInstance: vi.fn(),
      updateInstance: vi.fn(),
      getGlobalPlaybackSettings: vi.fn().mockReturnValue({
        playbackRate: 1.25,
        volume: 1,
        muted: false,
      }),
    };

    // Mock StateManager.getInstance
    (StateManager.getInstance as any).mockReturnValue(mockStateManager);
    (EventManager.getInstance as any).mockReturnValue(mockEventManager);

    // Mock video data
    mockEpisode1 = {
      id: 'episode-1',
      no: 1,
      url: 'https://example.com/episode1.m3u8',
      status: 'free',
      is_last_play: true,
      subTitles: [],
    };

    mockEpisode2 = {
      id: 'episode-2',
      no: 2,
      url: 'https://example.com/episode2.m3u8',
      status: 'free',
      is_last_play: false,
      subTitles: [],
    };

    mockVideoData = {
      id: 'video-1',
      title: 'Test Video',
      series: [mockEpisode1, mockEpisode2],
      cover: { src: 'https://example.com/cover.jpg' },
    };

    // Mock state manager getInstance response
    mockStateManager.getInstance.mockReturnValue({
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      currentEpisode: mockEpisode1,
      isActive: true,
      status: 'ready',
      currentTime: 0,
    });

    // 创建 PlayerInstance 配置
    const config: PlayerInstanceConfig = {
      id: 'test-player',
      containerId: 'test-container',
      eventManager: mockEventManager,
      autoPlay: false,
      startTime: 30, // 设置初始 startTime 为 30 秒
    };

    playerInstance = new PlayerInstance(config);
  });

  afterEach(() => {
    // 清理 DOM
    const container = document.getElementById('test-container');
    if (container) {
      document.body.removeChild(container);
    }
  });

  it('应该在创建时正确设置 startTime', () => {
    expect(playerInstance.startTime).toBe(30);
  });

  it('应该在切换剧集时重置 startTime 为 0', async () => {
    // 模拟播放器已经初始化
    playerInstance.hasAuthSucceeded = true;
    playerInstance.originalVideoUrl = mockEpisode1.url;

    // 验证初始 startTime
    expect(playerInstance.startTime).toBe(30);

    // 切换到第二集
    await playerInstance.playEpisode(2);

    // 验证 startTime 被重置为 0
    expect(playerInstance.startTime).toBe(0);
  });

  it('应该在切换剧集时调用正确的日志记录', async () => {
    // 模拟播放器已经初始化
    playerInstance.hasAuthSucceeded = true;
    playerInstance.originalVideoUrl = mockEpisode1.url;

    // 切换到第二集
    await playerInstance.playEpisode(2);

    // 这里我们无法直接验证日志，但可以验证 startTime 被重置
    expect(playerInstance.startTime).toBe(0);
  });

  it('应该在重新播放同一集时也重置 startTime', async () => {
    // 模拟播放器已经初始化
    playerInstance.hasAuthSucceeded = true;
    playerInstance.originalVideoUrl = mockEpisode1.url;

    // 验证初始 startTime
    expect(playerInstance.startTime).toBe(30);

    // 重新播放第一集
    await playerInstance.playEpisode(1);

    // 验证 startTime 被重置为 0
    expect(playerInstance.startTime).toBe(0);
  });

  it('应该在 attachToContainer 时正确处理 newStartTime', () => {
    // 验证初始 startTime
    expect(playerInstance.startTime).toBe(30);

    // 附加到新容器并设置新的 startTime
    playerInstance.attachToContainer('new-container', 15);

    // 验证 startTime 被更新
    expect(playerInstance.startTime).toBe(15);
  });

  it('应该在 attachToContainer 时不改变 startTime 如果没有提供 newStartTime', () => {
    // 验证初始 startTime
    expect(playerInstance.startTime).toBe(30);

    // 附加到新容器但不提供 newStartTime
    playerInstance.attachToContainer('new-container');

    // 验证 startTime 保持不变
    expect(playerInstance.startTime).toBe(30);
  });
});
