/**
 * @file PlayerFactory.test.ts
 * @description PlayerFactory 的单元测试，特别是 startTime 更新功能的测试
 */

import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { PlayerFactory } from '../core/factories/PlayerFactory';
import { EventManager } from '../core/managers/EventManager';
import { StateManager } from '../core/managers/StateManager';
import { IPlayerVideoData, IPlayerEpisode } from '../services/AuthManager';

// Mock dependencies
vi.mock('../core/managers/StateManager');
vi.mock('../core/managers/EventManager');
vi.mock('../services/AuthManager');
vi.mock('../services/TrackingManager');
vi.mock('../core/managers/NetworkStateManager');
vi.mock('../utils/LogManager');
vi.mock('../core/instance/PlayerInstance');

// Mock XGPlayer
vi.mock('xgplayer', () => ({
  default: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
    pause: vi.fn(),
    play: vi.fn(),
    seek: vi.fn(),
    once: vi.fn(),
    video: {},
    src: '',
    currentTime: 0,
    paused: false,
    plugins: [],
  })),
  Events: {
    LOADED_DATA: 'loadeddata',
  },
}));

vi.mock('xgplayer-subtitles', () => ({
  default: vi.fn().mockImplementation(() => ({
    destroy: vi.fn(),
  })),
}));

describe('PlayerFactory startTime 更新功能', () => {
  let playerFactory: PlayerFactory;
  let mockEventManager: any;
  let mockStateManager: any;
  let mockVideoData: IPlayerVideoData;
  let mockEpisode: IPlayerEpisode;

  beforeEach(() => {
    // 重置所有 mocks
    vi.clearAllMocks();

    // 重置 PlayerFactory 单例
    (PlayerFactory as any).instance = null;

    // Mock DOM
    const mockContainer = document.createElement('div');
    mockContainer.id = 'test-container';
    document.body.appendChild(mockContainer);

    // Mock EventManager
    mockEventManager = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    };

    // Mock StateManager
    mockStateManager = {
      createInstance: vi.fn(),
      getActiveInstance: vi.fn().mockReturnValue(null),
      setActiveInstance: vi.fn(),
      setPlayer: vi.fn(),
      getGlobalPlaybackSettings: vi.fn().mockReturnValue({
        playbackRate: 1.25,
        volume: 1,
        muted: false,
      }),
    };

    // Mock StateManager.getInstance
    (StateManager.getInstance as any).mockReturnValue(mockStateManager);
    (EventManager.getInstance as any).mockReturnValue(mockEventManager);

    // Mock video data
    mockEpisode = {
      id: 'episode-1',
      no: 1,
      url: 'https://example.com/episode1.m3u8',
      status: 'free',
      is_last_play: true,
      subTitles: [],
    };

    mockVideoData = {
      id: 'video-1',
      title: 'Test Video',
      series: [mockEpisode],
      cover: { src: 'https://example.com/cover.jpg' },
    };

    // 创建 PlayerFactory 实例
    playerFactory = PlayerFactory.getInstance(mockEventManager, mockStateManager);
  });

  afterEach(() => {
    // 清理 DOM
    const container = document.getElementById('test-container');
    if (container) {
      document.body.removeChild(container);
    }

    // 重置 PlayerFactory 单例
    (PlayerFactory as any).instance = null;
  });

  it('应该创建新的播放器实例', () => {
    const config = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 30,
      currentEpisode: mockEpisode,
    };

    const instance = playerFactory.createPlayer(config);

    expect(instance).toBeDefined();
    expect(mockStateManager.createInstance).toHaveBeenCalledWith(
      'test-player',
      'test-container',
      mockVideoData
    );
  });

  it('应该更新现有实例的 startTime 当传入不同的值时', () => {
    // 首先创建一个实例
    const config1 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 30,
      currentEpisode: mockEpisode,
    };

    const instance1 = playerFactory.createPlayer(config1);

    // 验证实例被创建
    expect(instance1).toBeDefined();

    // 再次创建相同 ID 的实例，但使用不同的 startTime
    const config2 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 0, // 不同的 startTime
      currentEpisode: mockEpisode,
    };

    const instance2 = playerFactory.createPlayer(config2);

    // 应该返回相同的实例
    expect(instance2).toBe(instance1);

    // 验证 startTime 被更新
    expect(instance2.startTime).toBe(0);
    expect(instance2.config.startTime).toBe(0);
    expect(instance2.isFirstInitialization).toBe(true);
  });

  it('应该更新现有实例的 startTime 从非零值到零', () => {
    // 首先创建一个实例，startTime 为 30
    const config1 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 30,
      currentEpisode: mockEpisode,
    };

    const instance1 = playerFactory.createPlayer(config1);

    // 验证初始 startTime
    expect(instance1.startTime).toBe(30);

    // 再次创建相同 ID 的实例，startTime 为 0
    const config2 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 0,
      currentEpisode: mockEpisode,
    };

    const instance2 = playerFactory.createPlayer(config2);

    // 应该返回相同的实例
    expect(instance2).toBe(instance1);

    // 验证 startTime 被更新为 0
    expect(instance2.startTime).toBe(0);
    expect(instance2.config.startTime).toBe(0);
  });

  it('应该更新现有实例的 startTime 从零到非零值', () => {
    // 首先创建一个实例，startTime 为 0
    const config1 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 0,
      currentEpisode: mockEpisode,
    };

    const instance1 = playerFactory.createPlayer(config1);

    // 验证初始 startTime
    expect(instance1.startTime).toBe(0);

    // 再次创建相同 ID 的实例，startTime 为 45
    const config2 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 45,
      currentEpisode: mockEpisode,
    };

    const instance2 = playerFactory.createPlayer(config2);

    // 应该返回相同的实例
    expect(instance2).toBe(instance1);

    // 验证 startTime 被更新为 45
    expect(instance2.startTime).toBe(45);
    expect(instance2.config.startTime).toBe(45);
  });

  it('应该不更新 startTime 如果值相同', () => {
    // 首先创建一个实例
    const config1 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 30,
      currentEpisode: mockEpisode,
    };

    const instance1 = playerFactory.createPlayer(config1);
    const originalIsFirstInit = instance1.isFirstInitialization;

    // 再次创建相同 ID 的实例，使用相同的 startTime
    const config2 = {
      id: 'test-player',
      containerId: 'test-container',
      videoData: mockVideoData,
      autoPlay: false,
      startTime: 30, // 相同的 startTime
      currentEpisode: mockEpisode,
    };

    const instance2 = playerFactory.createPlayer(config2);

    // 应该返回相同的实例
    expect(instance2).toBe(instance1);

    // startTime 应该保持不变
    expect(instance2.startTime).toBe(30);
    expect(instance2.config.startTime).toBe(30);

    // isFirstInitialization 不应该被重置
    expect(instance2.isFirstInitialization).toBe(originalIsFirstInit);
  });
});
