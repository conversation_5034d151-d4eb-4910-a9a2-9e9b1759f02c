import "./App.css";
import "./compatibility/ViewportHeightCompatibility.css";

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";

import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";

import ChannelBlockOverlay from "./components/ChannelBlockOverlay.tsx";
import SectionsDrawer from "./components/SectionsDrawer";
import ShortDramaPlayer from "./components/ShortDramaPlayer";
import SpzLoading from "./components/WebComponents/SpzLoading";
import { CoreProvider, useCore } from "./contexts/CoreProvider";
import {
  MultiPlayerProvider,
  useMultiPlayer,
} from "./contexts/MultiPlayerContext";
import { StatisticsManager } from "./core/managers/StatisticsManager";
import { useViewportHeight } from "./hooks/useViewportHeight";
import { transformToDrawerData } from "./utils/dataTransforms";
import {
  checkProductionLogsFlag,
  initializeLogConfig,
} from "./utils/logConfig";
import { moduleLoggers } from "./utils/LogManager";
import { initUrlVariantId, updateUrlVariantId } from "./utils/urlParams";

// 初始化日志配置
initializeLogConfig();
checkProductionLogsFlag();

// 多实例播放器管理组件
const MultiInstancePlayerManager: React.FC<{ videoData: IPlayerVideoData }> = ({
  videoData,
}) => {
  const { activePlayerId, setActivePlayer, handlePlayerEnded } =
    useMultiPlayer();
  const { instanceLifecycleManager } = useCore();

  const [firstPlayerCreated, setFirstPlayerCreated] = useState(false);
  const firstPlayerCreationStarted = useRef(false);

  // 创建并激活第一个播放器
  useEffect(() => {
    if (
      videoData &&
      !firstPlayerCreated &&
      !firstPlayerCreationStarted.current
    ) {
      firstPlayerCreationStarted.current = true;

      // 1. 首先判断地址栏参数的 variant_id
      const episodeIndex = initUrlVariantId(
        videoData.series.map((ep) => ({ id: ep.id, no: ep.no })),
        videoData.series[0]?.id || "",
      );

      let currentEpisode: IPlayerEpisode;
      let seekTime = 0;

      // 如果 URL 参数指定了有效剧集，优先使用 URL 参数
      if (episodeIndex !== 0) {
        currentEpisode = videoData.series[episodeIndex];
        moduleLoggers.App.info(
          `使用 URL 参数指定的剧集: 第${currentEpisode.no}集`,
        );
      } else {
        // 2. 否则从 API 查询 is_last_play 视为当前播放集数
        const lastPlayEpisode = videoData.series.find((ep) => ep.is_last_play);

        if (lastPlayEpisode) {
          currentEpisode = lastPlayEpisode;
          moduleLoggers.App.info(
            `使用 API 返回的 is_last_play 剧集: 第${currentEpisode.no}集`,
          );
        } else {
          // 3. 若 API 查询没有 is_last_play，从本地存储获取最后一集集数
          const lastPlayedInfo =
            StatisticsManager.getInstance().getLastPlayedFromStorage(
              videoData.id,
            );

          if (lastPlayedInfo) {
            const foundEpisode = videoData.series.find(
              (ep) => ep.id === lastPlayedInfo.episodeId,
            );
            if (foundEpisode) {
              currentEpisode = foundEpisode;
              moduleLoggers.App.info(
                `使用本地存储的最后一集: 第${currentEpisode.no}集`,
              );
            } else {
              // 本地存储的剧集不存在，回退到第一集
              currentEpisode = videoData.series[0];
              moduleLoggers.App.info(
                `本地存储的剧集不存在，回退到第一集: 第${currentEpisode.no}集`,
              );
            }
          } else {
            // 4. 本地存储也没有数据，回退到第一集
            currentEpisode = videoData.series[0];
            moduleLoggers.App.info(
              `本地存储无数据，使用第一集: 第${currentEpisode.no}集`,
            );
          }
        }
      }

      // 5. 查询当前集数对应的 API 返回中的剧集进度作为起播时间
      try {
        const apiPlayDuration = Number(currentEpisode.play_duration);
        if (!isNaN(apiPlayDuration) && apiPlayDuration > 0) {
          seekTime = apiPlayDuration;
          moduleLoggers.App.info(
            `使用 API 返回的剧集进度作为起播时间: ${seekTime}秒`,
          );
        } else {
          throw new Error("API 返回的剧集进度无效");
        }
      } catch (error) {
        // 6. 若 API 查询失败，则从本地存储查询当前集数的最新进度作为起播时间
        moduleLoggers.App.warn(
          `API 返回的剧集进度无效，尝试从本地存储获取: ${error}`,
        );

        const lastPlayedInfo =
          StatisticsManager.getInstance().getLastPlayedFromStorage(
            videoData.id,
          );

        if (lastPlayedInfo && lastPlayedInfo.episodeId === currentEpisode.id) {
          seekTime = lastPlayedInfo.currentTime || 0;
          moduleLoggers.App.info(
            `使用本地存储的当前集数进度作为起播时间: ${seekTime}秒`,
          );
        } else {
          seekTime = 0;
          moduleLoggers.App.info(
            `本地存储无当前集数进度，使用默认起播时间: ${seekTime}秒`,
          );
        }
      }

      // 更新 URL 参数以反映当前选择的剧集
      updateUrlVariantId(currentEpisode.id);

      const firstPlayerId = `episode-${videoData.id}-${currentEpisode.no}`;
      const firstContainerId = `container-${firstPlayerId}`;

      moduleLoggers.App.info(
        `创建第一个播放器实例: ${firstPlayerId}, seekTime: ${seekTime}`,
      );

      // 延迟创建，确保DOM已渲染
      setTimeout(() => {
        try {
          // 第一个播放器自动播放，会在PlayerInstance中进行鉴权检查
          instanceLifecycleManager.createPlayerInstance(
            firstPlayerId,
            firstContainerId,
            videoData,
            true, // 自动播放
            seekTime, // 直接使用计算出的seekTime，而不是状态
            currentEpisode, // 新增参数，确保选中的剧集被传递
            handlePlayerEnded, // 使用全局的 handlePlayerEnded 函数
          );
          setActivePlayer(firstPlayerId);
          setFirstPlayerCreated(true);

          // 添加调试日志，验证预加载功能
          moduleLoggers.App.info(
            `🎯 [App] 首集播放器创建完成，当前集数: ${currentEpisode.no}, 总集数: ${videoData.series.length}`,
          );

          moduleLoggers.App.info(
            `第一个播放器创建并激活成功（自动播放）: ${firstPlayerId}, seekTime: ${seekTime}`,
          );
        } catch (error) {
          moduleLoggers.App.error(`创建第一个播放器失败:`, error);
        }
      }, 50); // 减少延迟时间，避免初始加载动画
    }
  }, [
    videoData,
    firstPlayerCreated,
    instanceLifecycleManager,
    setActivePlayer,
    handlePlayerEnded,
  ]);

  // 预渲染所有播放器实例，通过CSS控制可见性
  return (
    <div className="multi-player-container">
      {/* 渲染所有剧集的播放器实例 */}
      {videoData.series.map((episode) => {
        const playerId = `episode-${videoData.id}-${episode.no}`;
        const containerId = `container-${playerId}`;
        const isActive = activePlayerId === playerId;

        return (
          <div
            key={playerId}
            className={`player-instance ${isActive ? "active" : "hidden"}`}
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              visibility: isActive ? "visible" : "hidden",
              opacity: isActive ? 1 : 0,
              zIndex: isActive ? 10 : 1,
              // 移除过渡效果，避免初始加载动画
              // transition: "opacity 0.3s ease-in-out",
            }}
          >
            <ShortDramaPlayer
              playerId={playerId}
              containerId={containerId}
              isFullscreen={true}
              autoInitialize={isActive} // 只有活跃的实例才自动初始化
              showOverlay={true}
            />
          </div>
        );
      })}

      <ChannelBlockOverlay />
    </div>
  );
};

// 共享选集抽屉组件
const SharedEpisodeDrawer: React.FC = () => {
  const { drawerState, closeEpisodeDrawer, onEpisodeSelect, getActivePlayer } =
    useMultiPlayer();

  // 获取当前活跃播放器的状态，从中获取currentEpisode
  const activePlayer = getActivePlayer();
  const currentEpisode = activePlayer?.currentEpisode;

  // 转换数据格式以适配SectionsDrawer，传入当前播放的集数
  const drawerData = transformToDrawerData(
    drawerState.playerData,
    currentEpisode?.no || 1,
  );

  const onDrawerClose = useCallback(() => {
    closeEpisodeDrawer();
    window.currentVideoPlayer.toggleGestureDisabled(false);
  }, [closeEpisodeDrawer]);

  const onDrawerOpen = useCallback(() => {
    window.currentVideoPlayer.toggleGestureDisabled(true);
  }, []);

  return (
    <SectionsDrawer
      data={drawerData}
      onEpisodeChange={onEpisodeSelect}
      isOpen={drawerState.isOpen}
      onOpen={onDrawerOpen}
      onClose={onDrawerClose}
    />
  );
};

const AppContent: React.FC<{ videoData: IPlayerVideoData }> = ({
  videoData,
}) => {
  moduleLoggers.App.info("App started");

  // 使用视口高度兼容性Hook
  const { viewportHeight, isCompatibilityEnabled, isIOS, isHarmony } =
    useViewportHeight();

  // 验证视口高度值的合理性
  const validatedViewportHeight = useMemo(() => {
    if (!isCompatibilityEnabled) {
      return window.innerHeight;
    }

    // 验证 viewportHeight 的合理性
    if (
      viewportHeight < 100 ||
      viewportHeight > 10000 ||
      isNaN(viewportHeight)
    ) {
      moduleLoggers.App.warn(
        "视口高度值异常，使用 window.innerHeight 作为回退",
        {
          viewportHeight,
          innerHeight: window.innerHeight,
          isIOS,
          isHarmony,
        },
      );
      return window.innerHeight;
    }

    return viewportHeight;
  }, [viewportHeight, isCompatibilityEnabled, isIOS, isHarmony]);

  // 记录设备信息
  useEffect(() => {
    if (isCompatibilityEnabled) {
      moduleLoggers.App.info("视口高度兼容性已启用", {
        viewportHeight: validatedViewportHeight,
        isIOS,
        isHarmony,
        userAgent: navigator.userAgent,
      });
    }
  }, [isCompatibilityEnabled, validatedViewportHeight, isIOS, isHarmony]);

  return (
    <div
      id="custom-solution-product-container"
      className="app"
      style={
        {
          // 移除动态高度计算，使用固定高度避免初始动画
          height: isCompatibilityEnabled
            ? `${validatedViewportHeight}px`
            : "100vh",
          "--csp-product-vh": isCompatibilityEnabled
            ? `${validatedViewportHeight * 0.01}px`
            : "1vh",
        } as React.CSSProperties
      }
    >
      {/* 多实例播放器管理器 */}
      <MultiInstancePlayerManager videoData={videoData} />

      {/* 全局共享的选集抽屉 - 用于切换播放器实例和集数 */}
      <SharedEpisodeDrawer />
    </div>
  );
};

const AppInitializer: React.FC<{
  children: (videoData: IPlayerVideoData) => React.ReactNode;
  productId: string;
}> = ({ children, productId }) => {
  const { videoPlayerManager } = useCore();
  const [globalVideoData, setGlobalVideoData] =
    useState<IPlayerVideoData | null>(null);
  const [isGlobalInitializing, setIsGlobalInitializing] = useState(true);
  const [globalError, setGlobalError] = useState<string | null>(null);
  const globalInitializationRef = useRef(false);

  useEffect(() => {
    const initializeGlobalVideoData = async () => {
      if (globalInitializationRef.current) return;

      moduleLoggers.App.info("开始全局初始化视频数据");
      globalInitializationRef.current = true;
      setIsGlobalInitializing(true);

      try {
        const data = await videoPlayerManager.initData(productId);
        setGlobalVideoData(data);
        moduleLoggers.App.info("全局视频数据加载完成");
      } catch (error) {
        moduleLoggers.App.error("全局初始化失败:", error);
        setGlobalError(
          `全局初始化失败: ${error instanceof Error ? error.message : String(error)}`,
        );
      } finally {
        setIsGlobalInitializing(false);
      }
    };

    initializeGlobalVideoData();
  }, [videoPlayerManager]);

  if (globalError) {
    return <div className="fullscreen-player error"></div>;
  }

  if (isGlobalInitializing || !globalVideoData) {
    return (
      <div className="fullscreen-player loading">
        <SpzLoading layout="container" />
      </div>
    );
  }

  return <>{children(globalVideoData)}</>;
};

const App: React.FC<{ productId: string }> = ({ productId }) => {
  return (
    <CoreProvider>
      <AppInitializer productId={productId}>
        {(videoData) => (
          <MultiPlayerProvider videoData={videoData}>
            <AppContent videoData={videoData} />
          </MultiPlayerProvider>
        )}
      </AppInitializer>
    </CoreProvider>
  );
};

export default App;
