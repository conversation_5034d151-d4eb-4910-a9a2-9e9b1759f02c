import "xgplayer/dist/index.min.css";
import "xgplayer-subtitles/dist/index.min.css";

import React from "react";
import ReactDOM from "react-dom/client";

import { moduleLoggers } from "@/utils/LogManager";

import App from "./App";
import { initCurrentVideoPlayerCompatibility } from "./compatibility";
import { resetViewportHeightGlobalState } from "./hooks/useViewportHeight";

// 存储当前的 React 根节点，用于重新挂载时清理
let currentRoot: ReactDOM.Root | null = null;

/**
 * 深度清理所有播放器相关资源
 */
const deepCleanup = () => {
  moduleLoggers.App.info("开始深度清理所有播放器资源");

  // 1. 先进行类级别的清理
  moduleLoggers.App.info("开始类级别清理");

  // 1.1 重置所有管理器实例
  if (window.__BS_VIDEO_PLAYER_MANAGER_CLASS__) {
    moduleLoggers.App.info("重置 VideoPlayerManager");
    window.__BS_VIDEO_PLAYER_MANAGER_CLASS__.resetGlobalInstance();
  }

  // 1.2 清理兼容层
  if (window.__BS_CLEANUP_COMPATIBILITY__) {
    moduleLoggers.App.info("清理兼容层");
    window.__BS_CLEANUP_COMPATIBILITY__();
  }

  // 1.3 重置视口高度全局状态
  moduleLoggers.App.info("重置视口高度全局状态");
  resetViewportHeightGlobalState();

  // 1.4 清理全局变量
  const globalVarsToClean = [
    "__BS_VIDEO_PLAYER_MANAGER_INSTANCE__",
    "__BS_VIDEO_PLAYER_MANAGER_CLASS__",
    "__BS_PRELOAD_MANAGER_CLASS__",
    "__BS_STATE_MANAGER_CLASS__",
    "__BS_EVENT_MANAGER_CLASS__",
    "__BS_STATISTICS_MANAGER_CLASS__",
    "__BS_TRACKING_MANAGER_CLASS__",
    "__BS_PLAYER_FACTORY_CLASS__",
    "__BS_INSTANCE_LIFECYCLE_MANAGER_CLASS__",
    "__BS_CLEANUP_COMPATIBILITY__",
    "__BS_IS_GESTURE_DISABLED__",
    "__BS_IS_ADJUSTING_VOLUME__",
    "__BS_PLAYER_MOUNTED__",
    "currentVideoPlayer",
  ];

  globalVarsToClean.forEach((varName) => {
    if (window[varName as keyof Window] !== undefined) {
      delete (window as any)[varName];
      moduleLoggers.App.info(`清理全局变量: ${varName}`);
    }
  });

  // 2. 最后进行 DOM 清理（只清理已知的播放器容器）
  moduleLoggers.App.info("开始 DOM 清理");

  // 3.1 清理 React 根节点
  if (currentRoot) {
    moduleLoggers.App.info("卸载 React 根节点");
    currentRoot.unmount();
    currentRoot = null;
  }

  // 3.2 清理播放器相关的 DOM 容器（只清空内容，不删除元素）
  // const playerSelectors = ['[id*="bs-player"]', '[id*="episode-"]'];

  // playerSelectors.forEach((selector) => {
  //   const elements = document.querySelectorAll(selector);
  //   elements.forEach((element) => {
  //     if (element instanceof HTMLElement) {
  //       // 只清空内容，保留元素本身
  //       element.innerHTML = "";
  //       moduleLoggers.App.info(`清空容器: ${element.id}`);
  //     }
  //   });
  // });

  moduleLoggers.App.info("深度清理完成");
};

const render = (productId: string, id: string) => {
  // 如果存在之前的根节点，先卸载
  if (currentRoot) {
    moduleLoggers.App.info("卸载之前的 React 根节点");
    currentRoot.unmount();
    currentRoot = null;
  }

  const root = ReactDOM.createRoot(document.getElementById(id) as HTMLElement);
  currentRoot = root;
  root.render(<App productId={productId} />);
};

window.mountBSPlayer = (productId: string, id: string) => {
  moduleLoggers.App.info("mountBSPlayer", productId, id);

  // 先卸载之前的播放器，但保留管理器实例
  // 只清理 DOM 和 React 根节点，不清理管理器
  moduleLoggers.App.info("开始 DOM 清理");

  // 1. 卸载 React 根节点
  if (currentRoot) {
    moduleLoggers.App.info("卸载之前的 React 根节点");
    currentRoot.unmount();
    currentRoot = null;
  }

  // 2. 清理指定容器
  const container = document.getElementById(id);
  if (container) {
    moduleLoggers.App.info(`清空容器: ${id}`);
    container.innerHTML = "";
  }

  // 3. 重置管理器实例（但保留类引用）
  moduleLoggers.App.info("3. 检查 VideoPlayerManager 实例和类");
  moduleLoggers.App.info(
    `window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ 存在: ${!!window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__}`,
  );
  moduleLoggers.App.info(
    `window.__BS_VIDEO_PLAYER_MANAGER_CLASS__ 存在: ${!!window.__BS_VIDEO_PLAYER_MANAGER_CLASS__}`,
  );

  if (
    window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ &&
    window.__BS_VIDEO_PLAYER_MANAGER_CLASS__
  ) {
    moduleLoggers.App.info("3. 重置 VideoPlayerManager 实例状态");
    try {
      // 只重置实例状态，不删除类引用
      window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__.reset();
      moduleLoggers.App.info("3. VideoPlayerManager 实例重置成功");

      // 重新初始化 StatisticsManager 的进度追踪器
      moduleLoggers.App.info("3.1 重新初始化 StatisticsManager 进度追踪器");
      const statisticsManager =
        window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__.statisticsManager;
      if (statisticsManager) {
        statisticsManager.initializeProgressTracker(
          window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__.eventManager,
          window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__.stateManager,
        );
        moduleLoggers.App.info(
          "3.1 StatisticsManager 进度追踪器重新初始化成功",
        );
      } else {
        moduleLoggers.App.warn(
          "3.1 StatisticsManager 实例不存在，无法重新初始化进度追踪器",
        );
      }
    } catch (error) {
      moduleLoggers.App.error("3. VideoPlayerManager 实例重置失败", error);
    }
  } else {
    moduleLoggers.App.warn("3. VideoPlayerManager 实例或类不存在，跳过重置");
  }

  // 4. 清理兼容层
  moduleLoggers.App.info("4. 清理兼容层");
  if (window.__BS_CLEANUP_COMPATIBILITY__) {
    moduleLoggers.App.info("清理兼容层");
    window.__BS_CLEANUP_COMPATIBILITY__();
  } else {
    moduleLoggers.App.warn("兼容层清理函数不存在");
  }

  // 5. 清理部分全局变量（保留管理器类引用）
  moduleLoggers.App.info("5. 清理部分全局变量");
  const varsToClean = [
    "__BS_IS_GESTURE_DISABLED__",
    "__BS_IS_ADJUSTING_VOLUME__",
    "currentVideoPlayer",
  ];

  varsToClean.forEach((varName) => {
    if (window[varName as keyof Window] !== undefined) {
      delete (window as any)[varName];
      moduleLoggers.App.info(`清理全局变量: ${varName}`);
    }
  });

  // 初始化兼容层
  moduleLoggers.App.info("6. 初始化兼容层");
  initCurrentVideoPlayerCompatibility();

  // 渲染新的 React 应用
  moduleLoggers.App.info("7. 渲染新的 React 应用");
  render(productId, id);

  // 设置挂载完成状态
  window.__BS_PLAYER_MOUNTED__ = {
    productId,
    containerId: id,
    mountedAt: Date.now(),
    status: "mounted",
  };

  moduleLoggers.App.info("播放器挂载完成", window.__BS_PLAYER_MOUNTED__);
};

/**
 * 卸载播放器，清理所有相关资源
 * @param id 容器ID，如果提供则只清理指定容器，否则清理所有
 */
window.unmountBSPlayer = (id?: string) => {
  moduleLoggers.App.info("unmountBSPlayer", id || "all");

  // 1. 先进行类级别的清理
  moduleLoggers.App.info("开始类级别清理");

  // 1.1 重置全局 VideoPlayerManager 实例
  moduleLoggers.App.info("1.1 检查 VideoPlayerManager 实例和类");
  moduleLoggers.App.info(
    `window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ 存在: ${!!window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__}`,
  );
  moduleLoggers.App.info(
    `window.__BS_VIDEO_PLAYER_MANAGER_CLASS__ 存在: ${!!window.__BS_VIDEO_PLAYER_MANAGER_CLASS__}`,
  );

  if (
    window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ &&
    window.__BS_VIDEO_PLAYER_MANAGER_CLASS__
  ) {
    moduleLoggers.App.info("1.1 重置全局 VideoPlayerManager 实例");
    try {
      window.__BS_VIDEO_PLAYER_MANAGER_CLASS__.resetGlobalInstance();
      moduleLoggers.App.info("1.1 VideoPlayerManager 重置成功");
    } catch (error) {
      moduleLoggers.App.error("1.1 VideoPlayerManager 重置失败", error);
    }
  } else {
    moduleLoggers.App.warn("1.1 VideoPlayerManager 实例或类不存在，跳过重置");
  }

  // 1.2 清理兼容层
  moduleLoggers.App.info("1.2 清理兼容层");
  if (window.__BS_CLEANUP_COMPATIBILITY__) {
    moduleLoggers.App.info("清理兼容层");
    window.__BS_CLEANUP_COMPATIBILITY__();
  } else {
    moduleLoggers.App.warn("兼容层清理函数不存在");
  }

  // 1.3 清理全局变量
  moduleLoggers.App.info("1.3 清理全局变量");
  if (window.__BS_IS_GESTURE_DISABLED__ !== undefined) {
    delete window.__BS_IS_GESTURE_DISABLED__;
    moduleLoggers.App.info("已清理 __BS_IS_GESTURE_DISABLED__");
  }

  // 1.4 清理挂载状态
  moduleLoggers.App.info("1.4 清理挂载状态");
  if (window.__BS_PLAYER_MOUNTED__ !== undefined) {
    delete window.__BS_PLAYER_MOUNTED__;
    moduleLoggers.App.info("清理播放器挂载状态");
  }

  // 2. 然后进行 DOM 清理
  moduleLoggers.App.info("开始 DOM 清理");

  // 2.1 卸载 React 根节点
  if (currentRoot) {
    moduleLoggers.App.info("卸载 React 根节点");
    currentRoot.unmount();
    currentRoot = null;
  }

  // 2.2 清理指定容器或所有容器
  if (id) {
    const container = document.getElementById(id);
    if (container) {
      moduleLoggers.App.info(`清空容器: ${id}`);
      container.innerHTML = "";
    }
  } else {
    // 执行深度清理
    deepCleanup();
  }

  moduleLoggers.App.info("unmountBSPlayer 完成");
};

/**
 * 全局深度清理方法，清理所有播放器相关资源
 */
window.deepCleanupBSPlayer = () => {
  moduleLoggers.App.info("执行全局深度清理");

  // 直接调用深度清理函数，它已经包含了所有必要的清理步骤
  deepCleanup();

  moduleLoggers.App.info("全局深度清理完成");
};

window.dispatchEvent(new CustomEvent("BSPlayerReady"));
