/**
 * @file CustomHlsJsPlugin.test.ts
 * @description 自定义HLS.js插件测试
 */

import { describe, expect, test } from "vitest";

import { CustomHlsJsPlugin } from "./CustomHlsJsPlugin";

// 简单的功能测试
describe("CustomHlsJsPlugin", () => {
  test("插件名称正确", () => {
    expect(CustomHlsJsPlugin.pluginName).toBe("CustomHlsJsPlugin");
  });

  test("默认配置正确", () => {
    const defaultConfig = CustomHlsJsPlugin.defaultConfig;
    expect(defaultConfig.startLevel).toBe(2);
    expect(defaultConfig.enableAudioTrackLoading).toBe(true);
    expect(defaultConfig.enableAudioTrackSwitching).toBe(true);
  });

  test("支持检查", () => {
    // 这里需要模拟 Hls.isSupported
    // 在实际环境中，这取决于浏览器是否支持 HLS.js
    expect(typeof CustomHlsJsPlugin.isSupported).toBe("function");
  });
});

// 配置适配测试
describe("配置适配", () => {
  test("应该正确设置 startLevel", () => {
    const plugin = new CustomHlsJsPlugin({
      pluginName: "CustomHlsJsPlugin",
      config: {},
      player: {
        config: {
          startLevel: 1,
          CustomHlsJsPlugin: {
            id: "test-video",
          },
        },
      } as any,
    });

    // 这里需要访问私有方法来测试
    // 在实际使用中，这些配置会在插件初始化时应用
    expect(plugin).toBeDefined();
  });
});

// 导出测试
describe("插件导出", () => {
  test("插件可以正确导入", () => {
    expect(CustomHlsJsPlugin).toBeDefined();
    expect(typeof CustomHlsJsPlugin).toBe("function");
  });
});

describe("CustomHlsJsPlugin", () => {
  let plugin: CustomHlsJsPlugin;
  let mockPlayer: any;

  beforeEach(() => {
    // 创建模拟播放器
    mockPlayer = {
      config: {
        CustomHlsJsPlugin: {
          id: "test-instance",
        },
      },
      handleSource: false,
      emit: jest.fn(),
      pause: jest.fn(),
      on: jest.fn(),
    };

    // 创建插件实例
    plugin = new CustomHlsJsPlugin({
      player: mockPlayer,
    } as any);
  });

  afterEach(() => {
    if (plugin) {
      plugin.destroy();
    }
  });

  describe("切片重试限制", () => {
    it("应该正确初始化切片重试计数器", () => {
      expect(plugin.getFragmentRetryStats()).toEqual({});
      expect(plugin.getFragmentRetryConfig()).toEqual({ maxRetries: 3 });
      expect(plugin.getCurrentLoadingFragments()).toEqual([]);
    });

    it("应该在切片加载成功时清除重试计数", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片加载失败
      const mockFragLoadErrorEvent = {
        frag: { url: fragmentUrl },
      };
      
      // 手动触发切片加载错误事件
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        hlsInstance.emit("fragLoadError", mockFragLoadErrorEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 模拟切片加载成功
        const mockFragLoadedEvent = {
          frag: { url: fragmentUrl },
        };
        hlsInstance.emit("fragLoaded", mockFragLoadedEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBeUndefined();
      }
    });

    it("应该在切片连续失败3次后停止加载", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片连续失败3次
      const mockFragLoadErrorEvent = {
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 第一次失败
        hlsInstance.emit("fragLoadError", mockFragLoadErrorEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 第二次失败
        hlsInstance.emit("fragLoadError", mockFragLoadErrorEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(2);
        
        // 第三次失败 - 应该停止加载
        hlsInstance.emit("fragLoadError", mockFragLoadErrorEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(3);
        
        // 验证播放器被暂停
        expect(mockPlayer.pause).toHaveBeenCalled();
      }
    });

    it("应该在手动恢复时清理切片重试计数器", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片加载失败
      const mockFragLoadErrorEvent = {
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        hlsInstance.emit("fragLoadError", mockFragLoadErrorEvent);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 手动恢复
        plugin.manualRecovery();
        expect(plugin.getFragmentRetryStats()).toEqual({});
        expect(plugin.getCurrentLoadingFragments()).toEqual([]);
      }
    });

    it("应该处理切片加载超时错误", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片开始加载
      const mockFragLoadingEvent = {
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 模拟切片开始加载
        hlsInstance.emit("fragLoading", mockFragLoadingEvent);
        expect(plugin.getCurrentLoadingFragments()).toContain(fragmentUrl);
        
        // 模拟切片加载超时错误
        const mockTimeoutError = {
          type: "networkError",
          details: "fragLoadTimeOut",
          fatal: false,
          frag: { url: fragmentUrl },
        };
        
        hlsInstance.emit("error", mockTimeoutError);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 再次超时
        hlsInstance.emit("error", mockTimeoutError);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(2);
        
        // 第三次超时 - 应该停止加载
        hlsInstance.emit("error", mockTimeoutError);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(3);
        
        // 验证播放器被暂停
        expect(mockPlayer.pause).toHaveBeenCalled();
      }
    });

    it("应该在用户点击继续按钮时恢复加载", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片连续失败3次，触发停止状态
      const mockTimeoutError = {
        type: "networkError",
        details: "fragLoadTimeOut",
        fatal: false,
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 模拟三次超时
        for (let i = 0; i < 3; i++) {
          hlsInstance.emit("error", mockTimeoutError);
        }
        
        // 验证需要用户手动恢复
        expect(plugin.needsUserRetry()).toBe(true);
        
        // 模拟用户点击继续按钮
        plugin.handleUserRetry();
        
        // 验证状态已重置
        expect(plugin.getFragmentRetryStats()).toEqual({});
        expect(plugin.getCurrentLoadingFragments()).toEqual([]);
      }
    });

    it("应该提供播放器状态信息", () => {
      const status = plugin.getPlayerStatus();
      expect(status).toHaveProperty("isBlocked");
      expect(status).toHaveProperty("isStopHlsLoading");
      expect(status).toHaveProperty("fragmentRetryStats");
      expect(status).toHaveProperty("currentLoadingFragments");
      expect(status).toHaveProperty("networkState");
    });

    it("应该在用户点击播放按钮时重置计数器", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片加载失败
      const mockTimeoutError = {
        type: "networkError",
        details: "fragLoadTimeOut",
        fatal: false,
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 模拟切片超时
        hlsInstance.emit("error", mockTimeoutError);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 模拟用户点击播放按钮
        plugin.handleUserPlay();
        
        // 验证计数器已重置
        expect(plugin.getFragmentRetryStats()).toEqual({});
        expect(plugin.getCurrentLoadingFragments()).toEqual([]);
      }
    });

    it("应该在播放开始时重置计数器", () => {
      const fragmentUrl = "https://example.com/fragment1.ts";
      
      // 模拟切片加载失败
      const mockTimeoutError = {
        type: "networkError",
        details: "fragLoadTimeOut",
        fatal: false,
        frag: { url: fragmentUrl },
      };
      
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 模拟切片超时
        hlsInstance.emit("error", mockTimeoutError);
        expect(plugin.getFragmentRetryStats()[fragmentUrl]).toBe(1);
        
        // 模拟播放开始事件
        mockPlayer.emit("playstart");
        
        // 验证计数器已重置
        expect(plugin.getFragmentRetryStats()).toEqual({});
        expect(plugin.getCurrentLoadingFragments()).toEqual([]);
      }
    });

    it("应该在缓冲时继续加载", () => {
      const hlsInstance = plugin.getHlsInstance();
      if (hlsInstance) {
        // 模拟缓冲事件
        mockPlayer.emit("buffer");
        
        // 验证HLS加载继续（通过日志或其他方式验证）
        // 这里主要是确保不会因为缓冲而停止加载
      }
    });
  });

  describe("HLS.js配置", () => {
    it("应该禁用HLS.js内置重试", () => {
      const config = plugin["_adaptHlsJsConfig"]({});
      expect(config.fragLoadingMaxRetry).toBe(0);
      expect(config.manifestLoadingMaxRetry).toBe(0);
      expect(config.audioTrackLoadingMaxRetry).toBe(0);
    });

    it("应该设置合理的超时时间", () => {
      const config = plugin["_adaptHlsJsConfig"]({});
      expect(config.fragLoadingTimeOut).toBe(5000);
      expect(config.manifestLoadingTimeOut).toBe(5000);
    });
  });
});
