/**
 * @file CustomHlsJsPlugin.ts
 * @description 自定义HLS.js插件，用于锁定分辨率档位
 * - 基于XGPlayer的BasePlugin扩展
 * - 锁定指定的分辨率档位，禁用自适应比特率
 * - 支持音频轨道管理和错误恢复
 * - 集成网络状态管理，支持重试限制和断网检测
 * - 支持用户手动恢复功能
 * - 支持播放开始和缓冲时的自动重置
 *
 * 使用示例：
 * ```typescript
 * // 获取插件实例
 * const plugin = player.plugins.CustomHlsJsPlugin;
 *
 * // 检查是否需要用户手动恢复
 * if (plugin.needsUserRetry()) {
 *   // 显示"继续"按钮
 *   showRetryButton();
 * }
 *
 * // 用户点击继续按钮时
 * function handleRetryClick() {
 *   plugin.handleUserRetry();
 * }
 *
 * // 用户点击播放按钮时
 * function handlePlayClick() {
 *   plugin.handleUserPlay();
 * }
 *
 * // 获取播放器状态信息
 * const status = plugin.getPlayerStatus();
 * console.log('Player status:', status);
 *
 * // 监听播放器事件（自动处理）
 * player.on('playstart', () => {
 *   // 插件会自动重置计数器并恢复加载
 * });
 *
 * player.on('buffer', () => {
 *   // 插件会自动确保加载继续
 * });
 * ```
 */
import Hls from "hls.js";
import { BasePlugin, Events, IBasePluginOptions } from "xgplayer";

import { moduleLoggers } from "@/utils/LogManager";

import { NetworkStateManager } from "../managers/NetworkStateManager";
import { StateManager } from "../managers/StateManager";

export interface CustomHlsJsPluginConfig {
  hlsOpts?: any;
  startLevel?: number; // 锁定的分辨率档位
  enableAudioTrackLoading?: boolean;
  enableAudioTrackSwitching?: boolean;
  maxRetries?: number; // 最大重试次数
  retryDelay?: number; // 重试延迟
}

// 补充类型声明
interface MediaInfo {
  videoDataRate: number;
  audioDataRate: number;
  fps?: number;
  hasAudio?: boolean;
  hasVideo?: boolean;
  audioChannelCount?: number;
  audioCodec?: string;
  videoCodec?: string;
  width?: number;
  height?: number;
  duration?: number;
  level?: number;
  mimeType?: string;
}

export class CustomHlsJsPlugin extends BasePlugin {
  static get pluginName() {
    return "CustomHlsJsPlugin";
  }

  private isStopHlsLoading = false;
  private fragmentRetryCounters: Map<string, number> = new Map(); // 按切片URL记录重试次数
  private maxFragmentRetries = Number.MAX_SAFE_INTEGER; // 单个切片最大重试次数: before: 3
  private currentLoadingFragments: Set<string> = new Set(); // 跟踪当前正在加载的切片

  static get defaultConfig(): CustomHlsJsPluginConfig {
    return {
      hlsOpts: {},
      startLevel: 2, // 默认锁定第2档分辨率
      enableAudioTrackLoading: true,
      enableAudioTrackSwitching: true,
      maxRetries: Number.MAX_SAFE_INTEGER, // 默认最大重试3次: before: 3
      retryDelay: 2000, // 默认重试延迟2秒
    };
  }

  static get isSupported() {
    return Hls.isSupported;
  }

  private hls: Hls | null = null;
  private hlsOpts: any = {};
  private _statisticsTimer: NodeJS.Timeout | null = null;
  private networkStateManager: NetworkStateManager;
  private retryTimeout: NodeJS.Timeout | null = null;
  private isRetrying = false;
  private id: string;
  private bufferCheckDebounceTimer: NodeJS.Timeout | null = null;

  constructor(args: IBasePluginOptions) {
    super(args);
    this.player.handleSource = false; // 关闭player源处理
    this.networkStateManager = NetworkStateManager.getInstance();

    this.id = args.player.config.CustomHlsJsPlugin.id;

    // 监听停止HLS加载事件
    this.setupHlsStopListener();

    // 监听网络状态变化事件，处理用户手动恢复
    this.setupNetworkStateListener();

    // 监听播放器事件，处理播放开始和缓冲
    this.setupPlayerEventListeners();
  }

  /**
   * 设置HLS停止监听器
   */
  private setupHlsStopListener(): void {
    const handleStopHlsLoading = () => {
      if (this.hls) {
        this.stopHlsLoading();
        moduleLoggers.CustomHlsJsPlugin.info("Received stop HLS loading event");
      }
    };

    const handleResumeHlsLoading = () => {
      if (this.hls) {
        // 检查网络状态
        const networkState = this.networkStateManager.getNetworkState();
        if (networkState.isOnline && !networkState.isBlocked) {
          // 检查当前播放器实例是否为活跃实例
          const stateManager = StateManager.getInstance();
          const currentInstance = stateManager.getInstance(this.id);
          const isActive = currentInstance?.isActive || false;

          if (isActive) {
            // 只有活跃的播放器实例才恢复HLS加载
            this.resumeHlsLoading();
            moduleLoggers.CustomHlsJsPlugin.info(
              `Received resume HLS loading event, started loading for active instance: ${this.id}`,
            );
          } else {
            // 非活跃实例不恢复HLS加载，避免同时播放多个播放器
            moduleLoggers.CustomHlsJsPlugin.info(
              `Received resume HLS loading event, but instance is not active, skipping: ${this.id}`,
            );
          }
        } else {
          moduleLoggers.CustomHlsJsPlugin.warn(
            "Network state does not allow resuming HLS loading",
          );
        }
      }
    };

    window.addEventListener("stopHlsLoading", handleStopHlsLoading);
    window.addEventListener("resumeHlsLoading", handleResumeHlsLoading);

    // 在destroy时移除监听器
    const originalDestroy = this.destroy.bind(this);
    this.destroy = () => {
      window.removeEventListener("stopHlsLoading", handleStopHlsLoading);
      window.removeEventListener("resumeHlsLoading", handleResumeHlsLoading);
      originalDestroy();
    };
  }

  /**
   * 设置网络状态变化事件，处理用户手动恢复
   */
  private setupNetworkStateListener(): void {
    const handleNetworkStateChange = () => {
      if (this.hls) {
        const networkState = this.networkStateManager.getNetworkState();
        if (!networkState.isOnline && !this.isStopHlsLoading) {
          moduleLoggers.CustomHlsJsPlugin.warn(
            "Network is offline, stopping HLS loading",
          );
          this.stopHlsLoading();
        }
      }
    };

    const handleCheckBufferStatus = (event: CustomEvent) => {
      if (this.hls && this.player) {
        this.handleBufferStatusCheck(event.detail);
      }
    };

    const handleCheckActivePlayerBufferStatus = (event: CustomEvent) => {
      if (this.hls && this.player) {
        // 检查当前实例是否为活跃实例
        const stateManager = StateManager.getInstance();
        const currentInstance = stateManager.getInstance(this.id);
        const isActive = currentInstance?.isActive || false;

        if (isActive) {
          moduleLoggers.CustomHlsJsPlugin.info(
            `当前实例为活跃实例，响应缓冲检查: ${this.id}`,
          );
          this.handleBufferStatusCheck(event.detail);
        } else {
          moduleLoggers.CustomHlsJsPlugin.info(
            `当前实例非活跃实例，跳过缓冲检查: ${this.id}`,
          );
        }
      }
    };

    window.addEventListener("networkStateChange", handleNetworkStateChange);
    window.addEventListener(
      "checkBufferStatus",
      handleCheckBufferStatus as EventListener,
    );
    window.addEventListener(
      "checkActivePlayerBufferStatus",
      handleCheckActivePlayerBufferStatus as EventListener,
    );

    // 在destroy时移除监听器
    const originalDestroy = this.destroy.bind(this);
    this.destroy = () => {
      window.removeEventListener(
        "networkStateChange",
        handleNetworkStateChange,
      );
      window.removeEventListener(
        "checkBufferStatus",
        handleCheckBufferStatus as EventListener,
      );
      window.removeEventListener(
        "checkActivePlayerBufferStatus",
        handleCheckActivePlayerBufferStatus as EventListener,
      );
      originalDestroy();
    };
  }

  /**
   * 设置播放器事件监听器
   */
  private setupPlayerEventListeners(): void {
    const handlePlayStart = () => {
      if (this.hls) {
        this.handlePlayStart();
        moduleLoggers.CustomHlsJsPlugin.info(
          "Player started playing, resuming HLS loading",
        );
      }
    };

    const handleBuffer = () => {
      if (this.hls) {
        this.handleBuffer();
        moduleLoggers.CustomHlsJsPlugin.info(
          "Player buffer, pausing HLS loading",
        );
      }
    };

    // 监听播放位置变化，当跳转时触发缓冲检查
    const handleTimeUpdate = () => {
      if (this.hls && !this.networkStateManager.getNetworkState().isOnline) {
        // 网络断开时，播放位置变化需要重新检查缓冲
        this.triggerBufferCheckOnTimeChange();
      }
    };

    this.player.on("playstart", handlePlayStart);
    this.player.on("buffer", handleBuffer);
    this.player.on("timeupdate", handleTimeUpdate);

    // 在destroy时移除监听器
    const originalDestroy = this.destroy.bind(this);
    this.destroy = () => {
      this.player.off("playstart", handlePlayStart);
      this.player.off("buffer", handleBuffer);
      this.player.off("timeupdate", handleTimeUpdate);
      originalDestroy();
    };
  }

  /**
   * 播放位置变化时触发缓冲检查
   */
  private triggerBufferCheckOnTimeChange(): void {
    // 使用防抖，避免频繁触发
    if (this.bufferCheckDebounceTimer) {
      clearTimeout(this.bufferCheckDebounceTimer);
    }

    this.bufferCheckDebounceTimer = setTimeout(() => {
      const networkState = this.networkStateManager.getNetworkState();
      if (!networkState.isOnline && networkState.currentError) {
        // 检查当前实例是否为活跃实例
        const stateManager = StateManager.getInstance();
        const currentInstance = stateManager.getInstance(this.id);
        const isActive = currentInstance?.isActive || false;

        if (isActive) {
          moduleLoggers.CustomHlsJsPlugin.info(
            `播放位置变化，触发活跃实例缓冲检查: ${this.id}`,
          );
          // 手动触发活跃实例缓冲检查
          this.networkStateManager.debugTriggerActivePlayerBufferCheck();
        } else {
          moduleLoggers.CustomHlsJsPlugin.info(
            `播放位置变化，但当前实例非活跃实例，跳过缓冲检查: ${this.id}`,
          );
        }
      }
    }, 500); // 0.5秒防抖
  }

  /**
   * 处理播放开始事件
   */
  private handlePlayStart(): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `Play start detected for instance: ${this.id}`,
    );

    // 重置切片重试计数器
    this.fragmentRetryCounters.clear();
    this.currentLoadingFragments.clear();

    // 重置网络状态管理器的重试计数
    this.networkStateManager.resetRetryCount(this.id);

    // 注意：不再在这里清除错误状态，让错误提示正常显示
    // 只有在剧集切换时才清除错误状态

    // 如果HLS加载被停止，则恢复加载
    if (this.isStopHlsLoading) {
      this.resumeHlsLoading();
    } else if (this.hls) {
      // 如果HLS加载已经活跃，确保开始加载
      this.hls.startLoad();
      moduleLoggers.CustomHlsJsPlugin.info(
        `Play start: HLS loading already active, ensured startLoad for ${this.id}`,
      );
    }
  }

  /**
   * 处理缓冲事件
   */
  private handleBuffer(): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `Buffer detected for instance: ${this.id}`,
    );

    // 缓冲时不需要停止HLS加载，只需要确保加载继续
    if (this.hls && !this.isStopHlsLoading) {
      this.hls.startLoad();
      moduleLoggers.CustomHlsJsPlugin.info(
        `Buffer: ensuring HLS loading continues for ${this.id}`,
      );
    }
  }

  /**
   * 适配HLS.js配置
   */
  private _adaptHlsJsConfig(hlsOpts: Record<string, any> = {}): any {
    const { playerConfig } = this;
    // 设置起始时间
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "startPosition") &&
      typeof playerConfig.startTime === "number"
    ) {
      hlsOpts.startPosition = playerConfig.startTime;
    }
    // 锁定分辨率档位
    const startLevel = this.config.startLevel ?? 2;
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "startLevel")) {
      hlsOpts.startLevel = startLevel;
    }
    // 禁用基于播放器大小的level调整
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "capLevelToPlayerSize")
    ) {
      hlsOpts.capLevelToPlayerSize = false;
    }
    // 禁用基于FPS下降的level调整
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "capLevelOnFPSDrop")) {
      hlsOpts.capLevelOnFPSDrop = false;
    }
    // 禁用自适应比特率算法
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "abrEwmaFastLive")) {
      hlsOpts.abrEwmaFastLive = 1;
    }
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "abrEwmaSlowLive")) {
      hlsOpts.abrEwmaSlowLive = 1;
    }
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "abrEwmaFastVoD")) {
      hlsOpts.abrEwmaFastVoD = 1;
    }
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "abrEwmaSlowVoD")) {
      hlsOpts.abrEwmaSlowVoD = 1;
    }
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "maxLoadingDelay")) {
      hlsOpts.maxLoadingDelay = 4;
    }
    // 优化预加载相关配置
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "enableWorker")) {
      hlsOpts.enableWorker = true;
    }
    // 完全禁用HLS.js内置重试，由我们完全控制重试逻辑
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "fragLoadingMaxRetry")) {
      hlsOpts.fragLoadingMaxRetry = 0; // 设置为0，完全禁用HLS.js内置重试
    }
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "manifestLoadingMaxRetry")
    ) {
      hlsOpts.manifestLoadingMaxRetry = 0; // 设置为0，完全禁用HLS.js内置重试
    }
    // 音频轨道相关配置
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "enableAudioTrackLoading")
    ) {
      hlsOpts.enableAudioTrackLoading = this.config.enableAudioTrackLoading;
    }
    if (
      !Object.prototype.hasOwnProperty.call(
        hlsOpts,
        "enableAudioTrackSwitching",
      )
    ) {
      hlsOpts.enableAudioTrackSwitching = this.config.enableAudioTrackSwitching;
    }
    if (
      !Object.prototype.hasOwnProperty.call(
        hlsOpts,
        "audioTrackLoadingMaxRetry",
      )
    ) {
      hlsOpts.audioTrackLoadingMaxRetry = 0; // 设置为0，完全禁用HLS.js内置重试
    }
    if (
      !Object.prototype.hasOwnProperty.call(
        hlsOpts,
        "audioTrackLoadingRetryDelay",
      )
    ) {
      hlsOpts.audioTrackLoadingRetryDelay = 1000;
    }
    // 添加更严格的错误处理
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "fragLoadingRetryDelay")
    ) {
      hlsOpts.fragLoadingRetryDelay = 1000; // 减少重试延迟
    }
    if (
      !Object.prototype.hasOwnProperty.call(
        hlsOpts,
        "manifestLoadingRetryDelay",
      )
    ) {
      hlsOpts.manifestLoadingRetryDelay = 1000; // 减少重试延迟
    }
    // 添加网络超时配置
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "fragLoadingTimeOut")) {
      hlsOpts.fragLoadingTimeOut = 10 * 1000; // 10 秒超时
    }
    if (
      !Object.prototype.hasOwnProperty.call(hlsOpts, "manifestLoadingTimeOut")
    ) {
      hlsOpts.manifestLoadingTimeOut = 10 * 1000; // 10 秒超时
    }
    // 禁用HLS.js的自动错误恢复，由我们完全控制
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "enableSoftwareAES")) {
      hlsOpts.enableSoftwareAES = false;
    }
    if (!Object.prototype.hasOwnProperty.call(hlsOpts, "debug")) {
      hlsOpts.debug = false;
    }
    return hlsOpts;
  }

  afterCreate() {
    const { hlsOpts } = this.config;
    this.hlsOpts = this._adaptHlsJsConfig(hlsOpts);
    this.on(Events.URL_CHANGE, (url: string) => {
      if (/^blob/.test(url)) {
        return;
      }
      this.playerConfig.url = url;
      this.register(url);
    });
    try {
      BasePlugin.defineGetterOrSetter(this.player, {
        url: {
          get: () => {
            try {
              // 只在video为HTMLVideoElement时返回src
              if (this.player.video && "src" in this.player.video) {
                return (this.player.video as HTMLVideoElement).src;
              }
              return null;
            } catch (error) {
              return null;
            }
          },
          configurable: true,
        },
      });
    } catch (e) {
      // NOOP
    }
  }

  beforePlayerInit() {
    if (typeof this.player.config.url === "string") {
      this.register(this.player.config.url);
    }
  }

  destroy() {
    if (this.hls) {
      this.hls.destroy();
      this.hls = null;
    }
    if (this._statisticsTimer) {
      clearInterval(this._statisticsTimer);
      this._statisticsTimer = null;
    }
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
      this.retryTimeout = null;
    }
    if (this.bufferCheckDebounceTimer) {
      clearTimeout(this.bufferCheckDebounceTimer);
      this.bufferCheckDebounceTimer = null;
    }
    // 清理切片重试计数器和当前加载切片跟踪
    this.fragmentRetryCounters.clear();
    this.currentLoadingFragments.clear();

    const { player } = this;
    BasePlugin.defineGetterOrSetter(player, {
      url: {
        get: () => {
          try {
            // 只在__url存在时返回
            return (player as any).__url ?? null;
          } catch (error) {
            return null;
          }
        },
        configurable: true,
      },
    });
  }

  register(url: string) {
    const { player } = this;
    if (this.hls) {
      this.hls.destroy();
    }

    // 检查网络状态，如果网络断开则不创建HLS实例
    const networkState = this.networkStateManager.getNetworkState();
    if (!networkState.isOnline) {
      moduleLoggers.CustomHlsJsPlugin.warn(
        "Network is offline, skipping HLS initialization",
      );
      // this.pausePlayer();
      return;
    }

    this.hls = new Hls(this.hlsOpts);

    // 重置重试计数
    this.networkStateManager.resetRetryCount(this.id || "unknown");
    // 重置切片重试计数器和当前加载切片跟踪
    this.fragmentRetryCounters.clear();
    this.currentLoadingFragments.clear();
    // 重置停止状态
    this.isStopHlsLoading = false;

    // 设置网络状态监控
    this.setupNetworkMonitoring();

    // 如果是活跃实例且网络已恢复，立即开始加载
    const stateManager = StateManager.getInstance();
    const currentInstance = stateManager.getInstance(this.id);
    const isActive = currentInstance?.isActive || false;
    if (isActive && networkState.isOnline && !networkState.isBlocked) {
      this.hls.startLoad();
      this.isStopHlsLoading = false;
      moduleLoggers.CustomHlsJsPlugin.info(
        `register: active instance with network online, startLoad for ${this.id}`,
      );
    }

    // --- 新增结束 ---
    // 监听 manifest 加载完成事件，确保 startLevel 生效
    this.hls.on(Hls.Events.MANIFEST_LOADED, (event, data) => {
      // 确保 startLevel 在有效范围内
      const maxLevel = data.levels.length - 1;
      let targetLevel = this.hlsOpts.startLevel;
      if (this.hlsOpts.startLevel > maxLevel) {
        targetLevel = maxLevel;
      } else if (this.hlsOpts.startLevel < 0) {
        targetLevel = 0;
      }
      moduleLoggers.CustomHlsJsPlugin.info(
        `Locked resolution level: ${targetLevel}/${maxLevel}`,
      );
      // 安全地设置并锁定level
      try {
        this.hls!.startLevel = targetLevel;
        this.hls!.currentLevel = targetLevel;
        this.hls!.nextLevel = targetLevel;
      } catch (error) {
        moduleLoggers.CustomHlsJsPlugin.warn(
          `Error setting level attribute:`,
          error,
        );
      }
    });
    // 监听 level 切换事件，防止意外切换
    this.hls.on(Hls.Events.LEVEL_SWITCHING, (event, data) => {
      const targetLevel = this.hlsOpts.startLevel;
      if (data.level !== targetLevel && targetLevel >= 0) {
        moduleLoggers.CustomHlsJsPlugin.info(
          `Unexpected level switch detected, forcing switch back to level: ${targetLevel}`,
        );
        this.hls!.nextLevel = targetLevel;
      }
    });
    // 监听 level 切换完成事件
    this.hls.on(Hls.Events.LEVEL_SWITCHED, (event, data) => {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Resolution level switch completed: ${data.level}`,
      );
      // 更新 mediainfo 中的 level 信息
      if ((player as any).mediainfo) {
        (player as any).mediainfo.level = data.level;
        player.emit("media_info", (player as any).mediainfo);
      }
    });
    // 监听音频轨道相关事件
    this.hls.on(Hls.Events.AUDIO_TRACK_LOADING, (event, data) => {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Audio track loading in progress:`,
        data,
      );
    });
    this.hls.on(Hls.Events.AUDIO_TRACK_LOADED, (event, data) => {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Audio track loading completed:`,
        data,
      );
    });
    this.hls.on(Hls.Events.AUDIO_TRACK_SWITCHING, (event, data) => {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Audio track switching in progress:`,
        data,
      );
    });
    this.hls.on(Hls.Events.AUDIO_TRACK_SWITCHED, (event, data) => {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Audio track switching completed:`,
        data,
      );
    });
    this.hls.once(Hls.Events.MEDIA_ATTACHED, () => {
      // 再次检查网络状态
      const currentNetworkState = this.networkStateManager.getNetworkState();
      if (!currentNetworkState.isOnline) {
        moduleLoggers.CustomHlsJsPlugin.warn(
          "Network is offline, stopping HLS loading",
        );
        this.stopHlsLoading();
        return;
      }
      this.hls!.loadSource(url);
    });
    // 错误处理
    this.hls.on(Hls.Events.ERROR, (event: any, data: any) => {
      player.emit("HLS_ERROR", {
        errorType: data.type,
        errorDetails: data.details,
        errorFatal: data.fatal,
      });
      moduleLoggers.CustomHlsJsPlugin.info(
        `HLS Error: ${data.type} - ${data.details}, fatal: ${data.fatal}`,
      );

      // 只处理切片加载错误
      if (data.frag) {
        const fragmentUrl = data.frag.url || "unknown";
        this.currentLoadingFragments.delete(fragmentUrl);
        const currentRetries = this.fragmentRetryCounters.get(fragmentUrl) || 0;
        const newRetryCount = currentRetries + 1;

        moduleLoggers.CustomHlsJsPlugin.warn(
          `Fragment load error: ${fragmentUrl}, retry count: ${newRetryCount}/${this.maxFragmentRetries}`,
        );

        if (newRetryCount >= this.maxFragmentRetries) {
          // 切片连续失败超过最大重试次数，停止HLS加载
          moduleLoggers.CustomHlsJsPlugin.error(
            `Fragment ${fragmentUrl} exceeded max retry attempts (${this.maxFragmentRetries}), stopping HLS loading`,
          );
          this.stopHlsLoading();
          this.pausePlayer();

          // 记录到网络状态管理器
          this.networkStateManager.recordNetworkError(
            this.id,
            "timeout",
            `Fragment load failed after ${this.maxFragmentRetries} attempts: ${fragmentUrl}`,
          );
        } else {
          // 更新重试计数
          this.fragmentRetryCounters.set(fragmentUrl, newRetryCount);
        }
      }

      // 处理网络错误
      this.handleHlsError(data);
    });

    // 监听切片加载事件，用于跟踪切片级别的重试
    this.hls.on(Hls.Events.FRAG_LOADING, (event, data) => {
      const fragmentUrl = data.frag?.url || "unknown";
      this.currentLoadingFragments.add(fragmentUrl);
    });

    this.hls.on(Hls.Events.FRAG_LOADED, (event: any, data: any) => {
      const fragmentUrl = data.frag?.url || "unknown";
      // 切片加载成功，清除重试计数和加载状态
      this.fragmentRetryCounters.delete(fragmentUrl);
      this.currentLoadingFragments.delete(fragmentUrl);
    });

    this.hls.on(Hls.Events.ERROR, (event: any, data: any) => {
      // 只处理切片加载错误
      if (data.frag) {
        const fragmentUrl = data.frag.url || "unknown";
        this.currentLoadingFragments.delete(fragmentUrl);
        const currentRetries = this.fragmentRetryCounters.get(fragmentUrl) || 0;
        const newRetryCount = currentRetries + 1;

        moduleLoggers.CustomHlsJsPlugin.warn(
          `Fragment load error: ${fragmentUrl}, retry count: ${newRetryCount}/${this.maxFragmentRetries}`,
        );

        if (newRetryCount >= this.maxFragmentRetries) {
          // 切片连续失败超过最大重试次数，停止HLS加载
          moduleLoggers.CustomHlsJsPlugin.error(
            `Fragment ${fragmentUrl} exceeded max retry attempts (${this.maxFragmentRetries}), stopping HLS loading`,
          );
          this.stopHlsLoading();
          this.pausePlayer();

          // 记录到网络状态管理器
          this.networkStateManager.recordNetworkError(
            this.id,
            "timeout",
            `Fragment load failed after ${this.maxFragmentRetries} attempts: ${fragmentUrl}`,
          );
        } else {
          // 更新重试计数
          this.fragmentRetryCounters.set(fragmentUrl, newRetryCount);
        }
      }
    });

    // attachMedia 只允许 HTMLMediaElement
    const media = this.player.media || this.player.video;
    if (media instanceof window.HTMLMediaElement) {
      this.hls.attachMedia(media);
    } else {
      moduleLoggers.CustomHlsJsPlugin.warn(
        "attachMedia failed: media is not HTMLMediaElement",
      );
    }
    this._statistics();
  }

  /**
   * 设置网络状态监控
   */
  private setupNetworkMonitoring(): void {
    // 定期检查网络状态
    const networkCheckInterval = setInterval(() => {
      if (!this.hls) {
        clearInterval(networkCheckInterval);
        return;
      }

      const networkState = this.networkStateManager.getNetworkState();
      if (!networkState.isOnline) {
        moduleLoggers.CustomHlsJsPlugin.warn(
          `Network monitoring detected offline state, stopping HLS loading: ${this.id}`,
        );
        this.stopHlsLoading();
        // this.pausePlayer();
        clearInterval(networkCheckInterval);
      }
    }, 2000); // 每2秒检查一次

    // 在destroy时清除定时器
    const originalDestroy = this.destroy.bind(this);
    this.destroy = () => {
      clearInterval(networkCheckInterval);
      originalDestroy();
    };
  }

  /**
   * 处理HLS错误
   */
  private handleHlsError(data: any): void {
    const instanceId = this.id || "unknown";

    // 处理切片加载超时错误（非致命错误）
    if (
      !data.fatal &&
      data.type === Hls.ErrorTypes.NETWORK_ERROR &&
      data.details === "fragLoadTimeOut"
    ) {
      this.handleFragmentLoadTimeout(data, instanceId);
      return;
    }

    if (data.fatal) {
      switch (data.type) {
        case Hls.ErrorTypes.NETWORK_ERROR:
          this.handleNetworkError(data, instanceId);
          break;
        case Hls.ErrorTypes.MEDIA_ERROR:
          this.handleMediaError(data, instanceId);
          break;
        default:
          this.handleOtherError(data, instanceId);
      }
    }
  }

  /**
   * 处理切片加载超时
   */
  private handleFragmentLoadTimeout(data: any, instanceId: string): void {
    // 尝试从错误数据中获取切片URL
    let fragmentUrl = data.frag?.url || data.url || "unknown";

    // 如果错误数据中没有明确的切片URL，尝试从当前加载的切片中获取
    if (fragmentUrl === "unknown" && this.currentLoadingFragments.size > 0) {
      // 获取第一个正在加载的切片（通常是最新的）
      fragmentUrl = Array.from(this.currentLoadingFragments)[0];
      moduleLoggers.CustomHlsJsPlugin.info(
        `Using current loading fragment for timeout: ${fragmentUrl}`,
      );
    }

    const currentRetries = this.fragmentRetryCounters.get(fragmentUrl) || 0;
    const newRetryCount = currentRetries + 1;

    moduleLoggers.CustomHlsJsPlugin.warn(
      `Fragment load timeout: ${fragmentUrl}, retry count: ${newRetryCount}/${this.maxFragmentRetries}`,
    );

    if (newRetryCount >= this.maxFragmentRetries) {
      // 切片连续失败超过最大重试次数，停止HLS加载
      moduleLoggers.CustomHlsJsPlugin.error(
        `Fragment ${fragmentUrl} exceeded max retry attempts (${this.maxFragmentRetries}), stopping HLS loading`,
      );
      this.stopHlsLoading();
      this.pausePlayer();

      // 记录到网络状态管理器，这会触发阻止状态和UI提示
      this.networkStateManager.recordNetworkError(
        instanceId,
        "timeout",
        `Please retry`,
      );
    } else {
      // 更新重试计数
      this.fragmentRetryCounters.set(fragmentUrl, newRetryCount);
      moduleLoggers.CustomHlsJsPlugin.info(
        `Fragment ${fragmentUrl} will retry (${newRetryCount}/${this.maxFragmentRetries})`,
      );
    }
  }

  /**
   * 处理网络错误
   */
  private handleNetworkError(data: any, instanceId: string): void {
    const errorMessage = `Network Error: ${data.details}`;
    moduleLoggers.CustomHlsJsPlugin.warn(errorMessage);

    // 检查网络状态
    const networkState = this.networkStateManager.getNetworkState();

    if (!networkState.isOnline) {
      // 网络断开，暂停播放并停止加载
      this.pausePlayer();
      this.stopHlsLoading();
      moduleLoggers.CustomHlsJsPlugin.warn(
        "Network disconnected, playback paused and loading stopped",
      );
      return;
    }

    // 记录网络错误
    const canContinue = this.networkStateManager.recordNetworkError(
      instanceId,
      "timeout",
      errorMessage,
    );

    if (canContinue && !this.isRetrying) {
      // 可以继续重试
      this.scheduleRetry(instanceId);
    } else {
      // 超过重试次数或被阻止，停止加载
      // this.pausePlayer();
      this.stopHlsLoading();
      moduleLoggers.CustomHlsJsPlugin.warn(
        "Maximum retry attempts exceeded, playback paused and loading stopped",
      );
    }
  }

  /**
   * 处理媒体错误
   */
  private handleMediaError(data: any, instanceId: string): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `Media error detected, attempting recovery`,
    );

    if (this.hls) {
      try {
        this.hls.recoverMediaError();
      } catch (error) {
        moduleLoggers.CustomHlsJsPlugin.error(
          "Media error recovery failed:",
          error,
        );
        this.pausePlayer();
        this.stopHlsLoading();
      }
    }
  }

  /**
   * 处理其他错误
   */
  private handleOtherError(data: any, instanceId: string): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `Fatal error detected, triggering player error event`,
    );
    this.player.emit("error", data);
    this.pausePlayer();
    this.stopHlsLoading();
  }

  /**
   * 安排重试
   */
  private scheduleRetry(instanceId: string): void {
    if (this.isRetrying) {
      return;
    }

    this.isRetrying = true;
    const retryDelay = this.config.retryDelay || 2000;

    moduleLoggers.CustomHlsJsPlugin.info(
      `Scheduling retry, delay: ${retryDelay}ms, instance: ${instanceId}`,
    );

    this.retryTimeout = setTimeout(() => {
      this.isRetrying = false;

      // 检查是否可以继续请求
      if (this.networkStateManager.canContinueRequest(instanceId)) {
        moduleLoggers.CustomHlsJsPlugin.info(
          `Starting retry loading: ${instanceId}`,
        );
        if (this.hls) {
          this.hls.startLoad();
        }
      } else {
        moduleLoggers.CustomHlsJsPlugin.warn(
          `Network state does not allow retry: ${instanceId}`,
        );
        this.pausePlayer();
        this.stopHlsLoading();
      }
    }, retryDelay);
  }

  /**
   * 暂停播放器
   */
  private pausePlayer(): void {
    if (this.player && this.player.pause) {
      this.player.pause();
      moduleLoggers.CustomHlsJsPlugin.info("Player paused");
    }
  }

  /**
   * 停止HLS加载
   */
  private stopHlsLoading(): void {
    if (this.hls) {
      this.hls.stopLoad();
      this.isStopHlsLoading = true;
      moduleLoggers.CustomHlsJsPlugin.info("HLS loading stopped");
    }
  }

  public resumeHlsLoading(): void {
    if (this.hls && this.isStopHlsLoading) {
      // 重置停止状态
      this.isStopHlsLoading = false;

      // 重置切片重试计数器，允许重新尝试加载
      this.fragmentRetryCounters.clear();
      this.currentLoadingFragments.clear();

      // 检查网络状态
      const networkState = this.networkStateManager.getNetworkState();
      if (networkState.isOnline && !networkState.isBlocked) {
        this.hls.startLoad();
        moduleLoggers.CustomHlsJsPlugin.info(
          "HLS loading resumed successfully",
        );
      } else {
        moduleLoggers.CustomHlsJsPlugin.warn(
          "Cannot resume HLS loading: network state does not allow",
        );
        // 如果网络状态不允许，重新设置停止状态
        this.isStopHlsLoading = true;
      }
    } else if (this.hls) {
      // 即使HLS加载已经活跃，也要重置计数器（用于播放开始时的重置）
      this.fragmentRetryCounters.clear();
      this.currentLoadingFragments.clear();
      moduleLoggers.CustomHlsJsPlugin.info(
        "HLS loading is already active, counters reset",
      );
    } else {
      moduleLoggers.CustomHlsJsPlugin.warn(
        "Cannot resume HLS loading: HLS instance not available",
      );
    }
  }

  /**
   * 统计信息收集
   */
  private _statistics() {
    const statsInfo = {
      speed: 0,
      playerType: "CustomHlsPlayer",
    };
    const mediainfo: MediaInfo = {
      videoDataRate: 0,
      audioDataRate: 0,
    };
    const { player, hls } = this;
    if (!hls) return;

    // 使用正确的事件类型
    // hls.on(Hls.Events.FRAG_LOAD_PROGRESS, (event: any, payload: any) => {
    //   statsInfo.speed = payload.stats.loaded / 1000;
    // });

    // hls.on(Hls.Events.FRAG_PARSING_DATA, (event: any, payload: any) => {
    //   if (payload.type === "video") {
    //     mediainfo.fps = parseInt(
    //       String(payload.nb / (payload.endPTS - payload.startPTS)),
    //     );
    //   }
    // });

    hls.on(Hls.Events.FRAG_PARSING_INIT_SEGMENT, (event: any, payload: any) => {
      mediainfo.hasAudio = !!(payload.tracks && payload.tracks.audio);
      mediainfo.hasVideo = !!(payload.tracks && payload.tracks.video);
      if (mediainfo.hasAudio) {
        const track = payload.tracks.audio;
        mediainfo.audioChannelCount =
          track.metadata && track.metadata.channelCount
            ? track.metadata.channelCount
            : 0;
        mediainfo.audioCodec = track.codec;
      }
      if (mediainfo.hasVideo) {
        const track = payload.tracks.video;
        if (track) {
          mediainfo.videoCodec = track.codec;
          mediainfo.width =
            track.metadata && track.metadata.width ? track.metadata.width : 0;
          mediainfo.height =
            track.metadata && track.metadata.height ? track.metadata.height : 0;
        }
      }
      mediainfo.duration =
        payload.frag && payload.frag.duration ? payload.frag.duration : 0;
      // 获取当前实际播放的 level，而不是配置的 startLevel
      mediainfo.level =
        this.hls!.currentLevel >= 0
          ? this.hls!.currentLevel
          : this.hlsOpts.startLevel >= 0
            ? this.hlsOpts.startLevel
            : 0;
      if (mediainfo.videoCodec || mediainfo.audioCodec) {
        mediainfo.mimeType = `video/hls; codecs="${mediainfo.videoCodec};${mediainfo.audioCodec}"`;
      }
      (player as any).mediainfo = mediainfo;
      player.emit("media_info", mediainfo);
    });

    this._statisticsTimer = setInterval(() => {
      player.emit("statistics_info", statsInfo);
      statsInfo.speed = 0;
    }, 1000);
  }

  /**
   * 获取HLS实例
   */
  getHlsInstance(): Hls | null {
    return this.hls;
  }

  /**
   * 手动切换分辨率档位
   */
  switchLevel(level: number): void {
    if (this.hls && level >= 0) {
      moduleLoggers.CustomHlsJsPlugin.info(
        `Manually switching resolution level: ${level}`,
      );
      this.hls.nextLevel = level;
    }
  }

  /**
   * 获取当前分辨率档位
   */
  getCurrentLevel(): number {
    return this.hls ? this.hls.currentLevel : -1;
  }

  /**
   * 获取所有可用的分辨率档位
   */
  getLevels(): any[] {
    return this.hls ? this.hls.levels : [];
  }

  /**
   * 手动恢复网络状态
   */
  manualRecovery(): void {
    const instanceId = this.id;
    moduleLoggers.CustomHlsJsPlugin.info(
      `Manually recovering network state: ${instanceId}`,
    );

    this.networkStateManager.manualRecovery();

    // 清理切片重试计数器和当前加载切片跟踪，允许重新尝试加载
    this.fragmentRetryCounters.clear();
    this.currentLoadingFragments.clear();

    // 检查网络状态，如果网络正常则恢复HLS加载
    const networkState = this.networkStateManager.getNetworkState();
    if (networkState.isOnline && !networkState.isBlocked) {
      // 检查当前播放器实例是否为活跃实例
      const stateManager = StateManager.getInstance();
      const currentInstance = stateManager.getInstance(this.id);
      const isActive = currentInstance?.isActive || false;

      if (isActive) {
        // 只有活跃的播放器实例才恢复HLS加载
        this.resumeHlsLoading();
        moduleLoggers.CustomHlsJsPlugin.info(
          `Manual recovery: active instance, resumed HLS loading for ${this.id}`,
        );
      } else {
        moduleLoggers.CustomHlsJsPlugin.info(
          `Manual recovery: instance is not active, skipping HLS loading for ${this.id}`,
        );
      }
    } else {
      moduleLoggers.CustomHlsJsPlugin.warn(
        "Manual recovery: network state does not allow resuming HLS loading",
      );
    }
  }

  /**
   * 公共方法：用户点击继续按钮时的恢复操作
   * 这个方法可以被UI层调用
   */
  public handleUserRetry(): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `User clicked retry button for instance: ${this.id}`,
    );
    this.manualRecovery();
  }

  /**
   * 公共方法：用户点击播放按钮时的操作
   * 这个方法可以被UI层调用
   */
  public handleUserPlay(): void {
    moduleLoggers.CustomHlsJsPlugin.info(
      `User clicked play button for instance: ${this.id}`,
    );
    this.handlePlayStart();
  }

  /**
   * 检查是否需要用户手动恢复
   */
  public needsUserRetry(): boolean {
    const networkState = this.networkStateManager.getNetworkState();
    return networkState.isBlocked || this.isStopHlsLoading;
  }

  /**
   * 获取当前播放器状态信息
   */
  public getPlayerStatus(): {
    isBlocked: boolean;
    isStopHlsLoading: boolean;
    fragmentRetryStats: { [fragmentUrl: string]: number };
    currentLoadingFragments: string[];
    networkState: any;
  } {
    return {
      isBlocked: this.networkStateManager.getNetworkState().isBlocked,
      isStopHlsLoading: this.isStopHlsLoading,
      fragmentRetryStats: this.getFragmentRetryStats(),
      currentLoadingFragments: this.getCurrentLoadingFragments(),
      networkState: this.networkStateManager.getNetworkState(),
    };
  }

  /**
   * 获取切片重试统计信息
   */
  getFragmentRetryStats(): { [fragmentUrl: string]: number } {
    const stats: { [fragmentUrl: string]: number } = {};
    this.fragmentRetryCounters.forEach((count, url) => {
      stats[url] = count;
    });
    return stats;
  }

  /**
   * 获取当前加载切片信息
   */
  getCurrentLoadingFragments(): string[] {
    return Array.from(this.currentLoadingFragments);
  }

  /**
   * 获取切片重试配置
   */
  getFragmentRetryConfig(): { maxRetries: number } {
    return {
      maxRetries: this.maxFragmentRetries,
    };
  }

  /**
   * 处理缓冲状态检查
   */
  private handleBufferStatusCheck(detail: { bufferThreshold: number }): void {
    if (!this.player || !this.hls) {
      return;
    }

    const currentTime = this.player.currentTime || 0;

    // 使用 xgplayer 的 checkBuffer API 检查当前时间点是否有缓冲
    const hasBuffer = this.player.checkBuffer
      ? this.player.checkBuffer(currentTime)
      : false;

    // 计算缓冲剩余时间
    const buffered = this.player.buffered;
    let bufferEnd = currentTime;

    if (buffered && buffered.length > 0) {
      for (let i = 0; i < buffered.length; i++) {
        const start = buffered.start(i);
        const end = buffered.end(i);

        if (currentTime >= start && currentTime <= end) {
          bufferEnd = end;
          break;
        }
      }
    }

    const bufferAhead = bufferEnd - currentTime;

    // 判断是否有足够缓冲：当前时间点有缓冲 且 缓冲剩余时间超过 0.5 秒
    const hasEnoughBuffer = hasBuffer && bufferAhead > 0.5;

    const effectiveThreshold = 0.5;
    moduleLoggers.CustomHlsJsPlugin.info(
      `缓冲状态检查: 实例=${this.id}, 当前时间=${currentTime}s, 缓冲区=[${buffered && buffered.length ? Array.from({length: buffered.length}).map((_,i)=>`[${buffered.start(i)}-${buffered.end(i)}]`).join(',') : '无'}], 缓冲结束=${bufferEnd}s, 缓冲剩余=${bufferAhead}s, 阈值=0.5s, 当前时间点有缓冲=${hasBuffer}, 是否充足=${hasEnoughBuffer}`,
    );

    // 发送缓冲状态响应事件
    const responseEvent = new CustomEvent("bufferStatusResponse", {
      detail: {
        instanceId: this.id,
        hasEnoughBuffer,
        bufferAhead,
        currentTime,
        bufferEnd,
        hasBuffer,
        timestamp: Date.now(),
      },
    });
    window.dispatchEvent(responseEvent);
  }
}
