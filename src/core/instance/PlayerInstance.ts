/**
 * @file PlayerInstance.ts
 * @description 播放器实例 (PI)
 * - 封装了底层播放器（如 XGPlayer）的单个实例。
 * - 管理单个视频的播放、暂停、加载等生命周期。
 * - 每个实例对应一个视频源。
 * - 集成网络状态管理，支持网络错误处理和手动恢复。
 */

import Player, { IPlayerOptions, MobilePreset } from "xgplayer";
import Subtitle from "xgplayer-subtitles";

import {
  AuthManager,
  IPlayerEpisode,
  IPlayerVideoData,
  ISubtitle,
} from "@/services/AuthManager";
import { TrackingManager } from "@/services/TrackingManager";
import { moduleLoggers } from "@/utils/LogManager";

import { updateUrlVariantId } from "../../utils/urlParams";
import { EventManager } from "../managers/EventManager";
import { NetworkStateManager } from "../managers/NetworkStateManager";
import { StateManager } from "../managers/StateManager";
import { CustomHlsJsPlugin } from "../plugins";
import { PlayerEventHandler } from "./handlers/PlayerEventHandler";

export interface PlayerInstanceConfig {
  id: string;
  containerId: string;
  eventManager: EventManager;
  autoPlay?: boolean;
  preload?: boolean;
  startTime?: number;
  onEnded?: (playerId: string, currentEpisodeNo: number) => void;
}

export class PlayerInstance {
  private static instanceCounter = 0;
  public instanceId: string;
  public containerId: string;
  public stateManager: StateManager;
  public eventManager: EventManager;
  public config: PlayerInstanceConfig;
  private authManager: AuthManager;
  public trackingManager: TrackingManager;
  public networkStateManager: NetworkStateManager;
  public isAuthenticating = false;
  public hasAuthSucceeded = false;
  public originalVideoUrl = "";
  private posterUrl = "";
  public startTime = 0;
  public isFirstInitialization = true;

  public xgplayer: Player | null = null;
  public subtitle: Subtitle | null = null;

  private eventHandler: PlayerEventHandler | null = null;
  private globalPlaybackSettingsHandler: ((event: any) => void) | null = null;
  private networkStateHandler: ((state: any) => void) | null = null;

  constructor(config: PlayerInstanceConfig) {
    this.instanceId = config.id;
    this.containerId = config.containerId;
    this.config = config;
    this.stateManager = StateManager.getInstance();
    this.authManager = AuthManager.getInstance();
    this.trackingManager = TrackingManager.getInstance();
    this.networkStateManager = NetworkStateManager.getInstance();
    this.eventManager = config.eventManager;
    this.startTime = config.startTime || 0;

    moduleLoggers.PlayerInstance.info(
      `Creating instance ${this.instanceId}, startTime: ${this.startTime}, onEnded: ${!!config.onEnded}`,
    );

    PlayerInstance.instanceCounter++;

    this.globalPlaybackSettingsHandler = (event: any) => {
      this.handleGlobalPlaybackSettingsChange(event);
    };
    this.eventManager.on(
      "globalPlaybackSettingsChange",
      this.globalPlaybackSettingsHandler,
    );

    // 监听网络状态变化
    this.setupNetworkStateListener();
  }

  /**
   * 设置网络状态监听器
   */
  private setupNetworkStateListener(): void {
    this.networkStateManager = NetworkStateManager.getInstance();
    this.networkStateHandler = this.handleNetworkStateChange.bind(this);
    this.networkStateManager.subscribe(this.networkStateHandler);

    // 监听停止HLS加载事件
    this.setupHlsStopListener();
  }

  /**
   * 设置HLS停止监听器
   */
  private setupHlsStopListener(): void {
    const handleStopHlsLoading = () => {
      this.stopHlsLoading();
      moduleLoggers.PlayerInstance.info(
        `Received stop HLS loading event: ${this.instanceId}`,
      );
    };

    const handleResumeHlsLoading = () => {
      // 检查网络状态
      const networkState = this.networkStateManager.getNetworkState();
      if (networkState.isOnline && !networkState.isBlocked) {
        // 检查当前播放器实例是否为活跃实例
        const currentInstance = this.stateManager.getInstance(this.instanceId);
        const isActive = currentInstance?.isActive || false;

        if (isActive) {
          // 只有活跃的播放器实例才恢复HLS加载
          const hls = this.getCustomHlsInstance();
          if (hls && typeof hls.resumeHlsLoading === "function") {
            hls.resumeHlsLoading();
            moduleLoggers.PlayerInstance.info(
              `Received resume HLS loading event, resumed HLS loading for active instance: ${this.instanceId}`,
            );
          } else {
            // 如果没有HLS实例或resumeHlsLoading方法，则直接播放
            this.play();
            moduleLoggers.PlayerInstance.info(
              `Received resume HLS loading event, playing for active instance: ${this.instanceId}`,
            );
          }
        } else {
          // 非活跃实例跳过
          moduleLoggers.PlayerInstance.info(
            `Received resume HLS loading event, skipping for inactive instance: ${this.instanceId}`,
          );
        }
      } else {
        moduleLoggers.PlayerInstance.warn(
          `Network state does not allow resuming HLS loading: ${this.instanceId}`,
        );
      }
    };

    window.addEventListener("stopHlsLoading", handleStopHlsLoading);
    window.addEventListener("resumeHlsLoading", handleResumeHlsLoading);

    // 在destroy时移除监听器
    const originalDestroy = this.destroy.bind(this);
    this.destroy = () => {
      window.removeEventListener("stopHlsLoading", handleStopHlsLoading);
      window.removeEventListener("resumeHlsLoading", handleResumeHlsLoading);
      originalDestroy();
    };
  }

  /**
   * 处理网络状态变化
   */
  private handleNetworkStateChange(networkState: any): void {
    moduleLoggers.PlayerInstance.info(
      `Network state changed: ${this.instanceId}, online: ${networkState.isOnline}, blocked: ${networkState.isBlocked}`,
    );

    if (!networkState.isOnline) {
      // 网络断开并停止HLS加载
      if (this.xgplayer && !this.xgplayer.paused) {
        // this.xgplayer.pause();
        moduleLoggers.PlayerInstance.warn(
          "Network disconnected, playback paused",
        );
      }

      // 停止HLS加载
      this.stopHlsLoading();

      this.stateManager.updateInstance(this.instanceId, {
        status: "paused",
      });
    } else if (networkState.isBlocked) {
      // 网络被阻止（超过重试次数），暂停播放并停止HLS加载
      if (this.xgplayer && !this.xgplayer.paused) {
        // this.xgplayer.pause();
        moduleLoggers.PlayerInstance.warn("Network blocked, playback paused");
      }

      // 停止HLS加载
      this.stopHlsLoading();

      this.stateManager.updateInstance(this.instanceId, {
        status: "paused",
      });
    } else {
      // 网络恢复，可以继续播放
      moduleLoggers.PlayerInstance.info(
        "Network restored, playback can be resumed",
      );

      // 如果之前是因为网络问题暂停的，现在可以恢复播放
      const state = this.stateManager.getInstance(this.instanceId);
      if (state?.status === "paused" && this.hasAuthSucceeded) {
        // 只有活跃的播放器实例才自动恢复播放
        if (state.isActive) {
          if (this.xgplayer && this.xgplayer.paused) {
            this.xgplayer.play();
            moduleLoggers.PlayerInstance.info(
              `Auto-resuming playback for active instance: ${this.instanceId}`,
            );
          }
        } else {
          moduleLoggers.PlayerInstance.info(
            `Network restored but instance is not active, skipping auto-resume: ${this.instanceId}`,
          );
        }
      }
    }
  }

  /**
   * 停止HLS加载
   */
  private stopHlsLoading(): void {
    const hls = this.getCustomHlsInstance();
    if (hls) {
      hls.stopLoad();
      moduleLoggers.PlayerInstance.info(
        `HLS loading stopped: ${this.instanceId}`,
      );
    }
  }

  /**
   * 初始化播放器
   */
  private initializePlayer(
    url: string,
    subTitles?: ISubtitle[],
    autoPlay = false,
  ): void {
    if (this.xgplayer) {
      moduleLoggers.PlayerInstance.warn(
        `Player already initialized for instance ${this.instanceId}`,
      );
      return;
    }

    const containerElement = document.getElementById(this.containerId);
    if (!containerElement) {
      moduleLoggers.PlayerInstance.error(
        `DOM container does not exist: ${this.containerId}`,
      );
      this.stateManager.updateInstance(this.instanceId, { status: "error" });
      return;
    }

    containerElement.innerHTML = "";

    try {
      // 检查当前播放器实例是否为活跃实例
      const currentInstance = this.stateManager.getInstance(this.instanceId);
      const isActive = currentInstance?.isActive || false;

      // 只有活跃的播放器实例才启用自动播放
      const shouldAutoPlay = autoPlay && isActive;

      moduleLoggers.PlayerInstance.info(
        `Starting to initialize player instance ${this.instanceId} to container ${this.containerId}`,
        {
          url,
          subTitles,
          autoPlay,
          isActive,
          shouldAutoPlay,
        },
      );
      const playerConfig: IPlayerOptions = {
        id: this.containerId,
        url,
        controls: {
          mode: "flex",
          autoHide: true,
        },
        leavePlayerTime: 5 * 1000,
        inactive: 5 * 1000,
        width: "100%",
        height: "100%",
        autoplay: shouldAutoPlay,
        autoplayMuted:
          StateManager.getInstance().getGlobalPlaybackSettings().muted || false,
        playsinline: true,
        marginControls: false,
        defaultPlaybackRate:
          StateManager.getInstance().getGlobalPlaybackSettings().playbackRate ||
          1.25,
        volume:
          StateManager.getInstance().getGlobalPlaybackSettings().volume || 1,
        fullscreen: false,
        startTime: this.startTime,
        playbackRate: [0.75, 1, 1.25, 1.5, 2],
        plugins: [CustomHlsJsPlugin],
        ignores: [
          "time",
          "volume",
          "progress",
          "controls",
          "playbackrate",
          "replay",
          "definition",
          "fullscreen",
          "play",
          "cssfullscreen",
        ],
        commonStyle: {
          playedColor: "rgba(238, 82, 155, 1)",
        },
        autoplayInlineOnMobile: true,
        fitVideoSize: "fixed",
        videoFillMode: "contain",
        fluid: true,
        objectFit: "contain",
        backgroundColor: "#000000",
        presets: [MobilePreset],
        CustomHlsJsPlugin: {
          id: this.instanceId,
          startLevel: 2,
          enableAudioTrackLoading: true,
          enableAudioTrackSwitching: true,
          maxRetries: 3,
          retryDelay: 2000,
          hlsOpts: {
            enableWorker: true,
            autoStartLoad: true,
            fragLoadingMaxRetry: 5,
            manifestLoadingMaxRetry: 5,
            levelLoadingMaxRetry: 5,
            fragLoadingRetryDelay: 1000,
            manifestLoadingRetryDelay: 1000,
            levelLoadingRetryDelay: 1000,
            maxBufferLength: 30,
            forceKeyFrameOnDiscontinuity: true,
            abrEwmaDefaultEstimate: 500000,
            ignoreLevelError: true,
            audioTrackLoadingMaxRetry: 5,
            audioTrackLoadingRetryDelay: 1000,
            maxAudioBufferLength: 30,
            maxAudioBufferSize: 60 * 1000 * 1000,
          },
        },
      };

      moduleLoggers.PlayerInstance.info(
        `Created player configuration, startTime: ${this.startTime}, isFirstInitialization: ${this.isFirstInitialization}, isActive: ${isActive}, shouldAutoPlay: ${shouldAutoPlay}`,
      );

      if (this.posterUrl) {
        playerConfig.poster = this.posterUrl;
        moduleLoggers.PlayerInstance.info(`Set cover image: ${this.posterUrl}`);
      }

      this.xgplayer = new Player(playerConfig);
      this.applyGlobalPlaybackSettings();

      // 设置起始时间（只在第一次初始化时生效）
      this.xgplayer.once(Player.Events.LOADED_DATA, () => {
        if (this.isFirstInitialization && this.xgplayer) {
          // 明确设置播放时间，包括 startTime = 0 的情况
          // this.xgplayer.currentTime = this.startTime;
          this.xgplayer.seek(this.startTime);
          moduleLoggers.PlayerInstance.info(
            `Video data loaded, manually set playback time to: ${this.startTime}s`,
          );
          this.isFirstInitialization = false; // 标记已不是第一次初始化
        }
      });

      this.subtitle = new Subtitle({
        player: this.xgplayer,
        subTitles: subTitles || [],
        domRender: true,
        defaultOpen: true,
        mode: "stoke",
        line: "double",
        updateMode: "vod",
        renderMode: "normal",
      });

      this.eventHandler = new PlayerEventHandler(this);
      this.eventHandler.setupEventListeners();

      // 在播放器初始化完成后初始化播放追踪ID
      this.eventHandler.initPlayTrackId();

      if (this.xgplayer && this.xgplayer.video) {
        moduleLoggers.PlayerInstance.info(
          `Player instance ${this.instanceId} initialization succeeded`,
        );
      } else {
        throw new Error("Player creation failed, video element not found");
      }
    } catch (error) {
      moduleLoggers.PlayerInstance.error(
        `Player instance ${this.instanceId} initialization failed:`,
        error,
      );
      this.stateManager.updateInstance(this.instanceId, { status: "error" });

      if (this.xgplayer) {
        this.xgplayer.destroy();
        this.xgplayer = null;
      }
    }
  }

  public getPlayTrackId(): string {
    return this.eventHandler?.getPlayTrackId() || "";
  }

  private showAuthDialog(
    videoData: IPlayerVideoData,
    episode: IPlayerEpisode,
  ): void {
    window.SPZ.whenApiDefined(
      document.getElementById("subscribe_check_right_logic"),
    ).then((api: any) => {
      api.checkright(videoData.id, episode.id);
    });
  }

  /**
   * 鉴权检查
   */
  private checkAuthBeforeInitialize(
    videoData: IPlayerVideoData,
    currentEpisode: IPlayerEpisode,
    seekTime?: number,
  ): void {
    moduleLoggers.PlayerInstance.info(
      `Initial authentication check: videoID=${videoData.id}, episodeID=${currentEpisode.id}, status=${currentEpisode.status}`,
    );

    // 如果剧集需要权限，直接弹出购买弹窗
    if (currentEpisode.status === "locked") {
      moduleLoggers.PlayerInstance.info(
        `Episode requires permission, directly show purchase dialog: ${this.instanceId}`,
      );
      this.hasAuthSucceeded = false;
      this.stateManager.updateInstance(this.instanceId, {
        status: "locked",
        currentEpisode,
      });

      // 上报播放进度（取0）
      this.trackingManager.trackDuration(videoData, currentEpisode, {
        playId: `${this.instanceId}-${Date.now()}`,
        playStartAt: Date.now(),
        status: "start",
        vDuration: 0,
        wDuration: 0,
      });

      // 直接弹出购买弹窗
      this.showAuthDialog(videoData, currentEpisode);
      return;
    }

    this.isAuthenticating = true;

    try {
      const hasPermission = !!this.originalVideoUrl;

      if (hasPermission) {
        moduleLoggers.PlayerInstance.info(
          `Initial authentication succeeded: ${this.instanceId}`,
        );
        this.hasAuthSucceeded = true;
        this.initializePlayerWithResult(
          this.originalVideoUrl,
          currentEpisode,
          true,
          this.config.autoPlay,
          seekTime,
        );
      } else {
        moduleLoggers.PlayerInstance.warn(
          `Initial authentication failed: ${this.instanceId}`,
        );
        this.hasAuthSucceeded = false;
        this.initializePlayerWithResult("", currentEpisode, false);

        moduleLoggers.PlayerInstance.info(
          `Initial authentication failure log - player: ${this.instanceId}, video: ${videoData.id}, episode: ${currentEpisode.id}`,
        );
        if (currentEpisode.status !== "free") {
          this.showAuthDialog(videoData, currentEpisode);
        }
      }
    } catch (error) {
      moduleLoggers.PlayerInstance.error(
        `Error occurred during initial authentication: ${this.instanceId}`,
        error,
      );

      this.hasAuthSucceeded = false;

      this.initializePlayerWithResult(
        currentEpisode.url,
        currentEpisode,
        false,
      );
      this.stateManager.updateInstance(this.instanceId, { status: "error" });
    } finally {
      this.isAuthenticating = false;
    }
  }

  /**
   * 初始化播放器结果处理
   */
  private initializePlayerWithResult(
    url: string,
    currentEpisode: IPlayerEpisode,
    authSuccess: boolean,
    autoPlay = false,
    seekTime?: number,
  ): void {
    moduleLoggers.PlayerInstance.info(
      `Starting to initialize player, URL: ${url || "(empty URL)"}, authentication status: ${authSuccess ? "succeeded" : "failed"}`,
    );

    if (authSuccess) {
      moduleLoggers.PlayerInstance.info(
        `Authentication succeeded, use URL to initialize player: ${this.instanceId}`,
      );
      this.hasAuthSucceeded = true;

      if (!this.xgplayer) {
        // 第一次初始化播放器
        this.initializePlayer(url, currentEpisode.subTitles, autoPlay);
        this.isFirstInitialization = false; // 标记已不是第一次初始化
      } else {
        // 播放器已存在，更新播放源（切换集数的情况）
        moduleLoggers.PlayerInstance.info(
          `Player already exists, update playback source: ${this.instanceId}`,
        );

        // 强制重置播放器状态，确保从指定时间开始播放
        this.xgplayer.pause();
        this.xgplayer.currentTime = 0;
        this.xgplayer.src = url;

        // 等待视频数据加载完成后，设置播放时间
        this.xgplayer.once(Player.Events.LOADED_DATA, () => {
          if (this.xgplayer && seekTime !== undefined) {
            // 明确设置播放时间，包括 seekTime = 0 的情况
            this.xgplayer.currentTime = seekTime;
            moduleLoggers.PlayerInstance.info(
              `Set playback time: ${seekTime}s`,
            );
          } else {
            moduleLoggers.PlayerInstance.info(
              `Do not set start time, play directly`,
            );
          }

          if (autoPlay) {
            this.play();
          }
        });
      }

      this.stateManager.updateInstance(this.instanceId, {
        status: "ready",
        currentEpisode,
      });
    } else {
      moduleLoggers.PlayerInstance.info(
        `Authentication failed, show subscription prompt: ${this.instanceId}`,
      );
    }
  }

  /**
   * 加载视频数据
   */
  public loadVideoData(
    videoData: IPlayerVideoData,
    currentEpisode?: IPlayerEpisode,
  ) {
    moduleLoggers.PlayerInstance.info(
      `Starting to load video data ${this.instanceId}`,
      videoData,
    );

    if (!videoData) {
      moduleLoggers.PlayerInstance.error(
        `Video data is empty: ${this.instanceId}`,
      );
      return;
    }

    if (!videoData.series || videoData.series.length === 0) {
      moduleLoggers.PlayerInstance.warn(
        `No episode data found: ${this.instanceId}`,
        videoData,
      );
      return;
    }

    this.isAuthenticating = false;
    this.hasAuthSucceeded = false;

    // 重置追踪器状态，确保首次加载时状态正确
    this.eventHandler?.resetTrackers();

    let episodeToPlay = currentEpisode;
    if (!episodeToPlay) {
      episodeToPlay =
        videoData.series.find((ep) => ep.is_last_play) || videoData.series[0];
    }

    moduleLoggers.PlayerInstance.info(`Found current episode:`, episodeToPlay);

    this.stateManager.updateInstance(this.instanceId, {
      videoData,
      currentEpisode: episodeToPlay,
      status: "loading",
    });

    this.trackingManager.trackViewContent(videoData);

    if (episodeToPlay) {
      this.originalVideoUrl = episodeToPlay.url || "";
      this.posterUrl = videoData.cover?.src || "";
      this.checkAuthBeforeInitialize(videoData, episodeToPlay, this.startTime);
    } else {
      moduleLoggers.PlayerInstance.error(
        `Current episode data is invalid:`,
        episodeToPlay,
      );
      this.stateManager.updateInstance(this.instanceId, { status: "error" });
    }
  }

  /**
   * 强制初始化播放器
   */
  public forceInitialize(): void {
    const state = this.stateManager.getInstance(this.instanceId);
    if (!state?.videoData) {
      moduleLoggers.PlayerInstance.warn(
        `Force initialization failed, no video data: ${this.instanceId}`,
      );
      return;
    }

    const currentEpisode =
      state.videoData.series.find((ep) => ep.is_last_play) ||
      state.videoData.series[0];

    if (currentEpisode && currentEpisode.url) {
      moduleLoggers.PlayerInstance.info(
        `Force initializing player: ${this.instanceId}`,
      );
      this.initializePlayer(currentEpisode.url, currentEpisode.subTitles);
    }
  }

  /**
   * 附加到新容器
   */
  public attachToContainer(
    newContainerId: string,
    newStartTime?: number,
    autoPlay = false,
  ): void {
    moduleLoggers.PlayerInstance.info(
      `Attaching instance ${this.instanceId} to new container ${newContainerId}`,
    );

    if (this.containerId === newContainerId && this.xgplayer) {
      moduleLoggers.PlayerInstance.info(
        `Instance already exists in target container ${newContainerId}, skip attachment operation.`,
      );
      return;
    }

    if (this.xgplayer) {
      moduleLoggers.PlayerInstance.info(
        `Destroy old xgplayer instance to attach to new container: ${this.instanceId}`,
      );
      this.xgplayer.destroy();
      this.xgplayer = null;
    }

    this.containerId = newContainerId;
    if (typeof newStartTime === "number") {
      this.startTime = newStartTime;
      this.isFirstInitialization = true; // 重置为第一次初始化状态
    }

    const state = this.stateManager.getInstance(this.instanceId);
    if (!state?.videoData || !state?.currentEpisode) {
      moduleLoggers.PlayerInstance.error(
        `Attachment container failed, missing video or episode data: ${this.instanceId}`,
      );
      return;
    }

    moduleLoggers.PlayerInstance.info(
      `Reinitialize player in new container ${newContainerId}`,
    );

    if (this.hasAuthSucceeded) {
      this.initializePlayerWithResult(
        this.originalVideoUrl,
        state.currentEpisode,
        true,
        autoPlay,
      );
    } else {
      this.checkAuthBeforeInitialize(
        state.videoData,
        state.currentEpisode,
        this.startTime,
      );
    }
  }

  /**
   * 播放指定集数
   */
  public async playEpisode(episodeNo: number): Promise<void> {
    moduleLoggers.PlayerInstance.info("playEpisode", episodeNo);
    const state = this.stateManager.getInstance(this.instanceId);
    if (!state?.videoData) {
      moduleLoggers.PlayerInstance.warn(
        `No video data loaded for instance ${this.instanceId}`,
      );
      return;
    }

    const videoData = state.videoData;
    const targetEpisode = videoData.series.find((e) => e.no === episodeNo);
    if (!targetEpisode) {
      moduleLoggers.PlayerInstance.error(
        `Cannot play episode ${episodeNo}, no corresponding episode information found`,
      );
      return;
    }

    moduleLoggers.PlayerInstance.info(
      `Switch to episode ${episodeNo}: ${this.instanceId}`,
    );

    // 更新 URL 参数
    updateUrlVariantId(targetEpisode.id);
    moduleLoggers.PlayerInstance.info(
      `Player updates URL parameters when switching episodes: ${targetEpisode.id}`,
    );

    this.eventHandler?.resetTrackers();
    this.isAuthenticating = false;
    this.hasAuthSucceeded = false;

    this.originalVideoUrl = targetEpisode.url || "";

    // 切换集数时总是从 0 开始播放
    const seekTime = 0;
    // 重置 startTime 属性，确保后续重新初始化时也从0开始
    this.startTime = 0;
    moduleLoggers.PlayerInstance.info(
      `Switch episode from 0 to start playback, reset startTime to 0: ${this.instanceId}`,
    );

    // 检查是否真的在切换剧集（当前剧集和目标剧集不同）
    const currentEpisode = state.currentEpisode;
    const isActuallySwitchingEpisode =
      !currentEpisode || currentEpisode.no !== episodeNo;

    // 切换剧集时重置累计观看时间
    this.stateManager.updateInstance(this.instanceId, {
      currentEpisode: targetEpisode,
      status: "loading",
      cumulatedWatchedTime: 0, // 重置为0，确保新剧集从头开始计算
    });

    // 只有在真正切换剧集时才清除错误状态
    if (isActuallySwitchingEpisode) {
      moduleLoggers.PlayerInstance.info(
        `真正切换剧集，清除错误状态: 从第${currentEpisode?.no || "未知"}集到第${episodeNo}集`,
      );
      this.networkStateManager.clearErrorState();
    } else {
      moduleLoggers.PlayerInstance.info(
        `重新播放当前剧集，保持错误状态: 第${episodeNo}集`,
      );
    }

    if (this.xgplayer) {
      this.checkAuthForEpisodeSwitch(videoData, targetEpisode, seekTime);
    } else {
      this.checkAuthBeforeInitialize(videoData, targetEpisode, seekTime);
    }

    // 剧集切换后，如果网络已恢复且为活跃实例，确保HLS开始加载
    this.ensureHlsLoadingAfterEpisodeSwitch();
  }

  /**
   * 切换集数时的鉴权检查
   */
  private checkAuthForEpisodeSwitch(
    videoData: IPlayerVideoData,
    episode: IPlayerEpisode,
    seekTime: number,
  ): void {
    moduleLoggers.PlayerInstance.info(
      `Switch episode authentication check: videoID=${videoData.id}, episodeID=${episode.id}, status=${episode.status}`,
    );

    // 在剧集切换时初始化播放追踪ID
    this.eventHandler?.initPlayTrackId();

    // 如果剧集需要权限，直接弹出购买弹窗
    if (episode.status === "locked") {
      moduleLoggers.PlayerInstance.info(
        `Switching to episode requires permission, directly show purchase dialog: ${this.instanceId}`,
      );
      this.hasAuthSucceeded = false;
      this.stateManager.updateInstance(this.instanceId, {
        status: "locked",
        currentEpisode: episode,
      });

      // 上报播放进度（取0）
      this.trackingManager.trackDuration(videoData, episode, {
        playId: `${this.instanceId}-${Date.now()}`,
        playStartAt: Date.now(),
        status: "start",
        vDuration: 0,
        wDuration: 0,
      });

      // 直接弹出购买弹窗
      this.showAuthDialog(videoData, episode);
      return;
    }

    this.isAuthenticating = true;

    try {
      const hasPermission = !!this.originalVideoUrl;

      if (hasPermission) {
        moduleLoggers.PlayerInstance.info(
          `Switch episode authentication succeeded: ${this.instanceId}`,
        );
        this.hasAuthSucceeded = true;

        if (this.xgplayer) {
          // 强制重置播放器状态，确保从0开始播放
          // 先暂停播放器，然后重置状态
          this.xgplayer.pause();
          this.xgplayer.currentTime = 0;
          this.xgplayer.src = this.originalVideoUrl;

          this.xgplayer.once(Player.Events.LOADED_DATA, () => {
            moduleLoggers.PlayerInstance.info(
              `Switch episode auto playback: ${this.instanceId}`,
            );
            // 设置起播时间并播放
            if (this.xgplayer) {
              // 明确设置播放时间，包括 seekTime = 0 的情况
              this.xgplayer.currentTime = seekTime;
              moduleLoggers.PlayerInstance.info(
                `Set start playback time: ${seekTime} seconds`,
              );
            }
            this.xgplayer?.play();
          });
        }
        this.stateManager.updateInstance(this.instanceId, {
          status: "loading",
        });
      } else {
        moduleLoggers.PlayerInstance.warn(
          `Switch episode authentication failed: ${this.instanceId}`,
        );
        this.hasAuthSucceeded = false;

        if (this.xgplayer) {
          this.xgplayer.src = "";
        }
        this.stateManager.updateInstance(this.instanceId, { status: "paused" });

        moduleLoggers.PlayerInstance.info(
          `Switch episode authentication failure log - player: ${this.instanceId}, video: ${videoData.id}, episode: ${episode.id}`,
        );

        this.showAuthDialog(videoData, episode);
      }
    } catch (error) {
      moduleLoggers.PlayerInstance.error(
        `Error occurred during switch episode authentication: ${this.instanceId}`,
        error,
      );
      this.hasAuthSucceeded = false;

      if (this.xgplayer) {
        this.xgplayer.src = "";
      }
      this.stateManager.updateInstance(this.instanceId, { status: "error" });
    } finally {
      this.isAuthenticating = false;
    }
  }

  public play(): void {
    if (this.xgplayer) {
      this.isAuthenticating = false;

      if (this.xgplayer.src) {
        this.getCustomHlsPlugin()?.resumeHlsLoading();
        this.xgplayer.play();
        moduleLoggers.PlayerInstance.info(
          `Attempting to play: ${this.instanceId}, src: ${this.xgplayer.src}`,
        );
      }
    }
  }

  public pause(): void {
    this.xgplayer?.pause();
  }

  public destroy(): void {
    moduleLoggers.PlayerInstance.info(
      `Destroying player instance: ${this.instanceId}`,
    );

    if (this.globalPlaybackSettingsHandler) {
      this.eventManager.off(
        "globalPlaybackSettingsChange",
        this.globalPlaybackSettingsHandler,
      );
      this.globalPlaybackSettingsHandler = null;
    }

    if (this.networkStateHandler) {
      // 取消网络状态监听
      this.networkStateHandler = null;
    }

    if (this.xgplayer) {
      this.xgplayer.destroy();
      this.xgplayer = null;
    }

    if (this.subtitle) {
      this.subtitle.destroy();
      this.subtitle = null;
    }

    if (this.eventHandler) {
      this.eventHandler.destroy();
      this.eventHandler = null;
    }

    this.stateManager.removeInstance(this.instanceId);
    moduleLoggers.PlayerInstance.info(
      `Player instance destruction completed: ${this.instanceId}`,
    );
  }

  public getId(): string {
    return this.instanceId;
  }

  /**
   * 应用全局播放设置
   */
  private applyGlobalPlaybackSettings(): void {
    if (!this.xgplayer) return;

    try {
      const globalSettings = this.stateManager.getGlobalPlaybackSettings();

      if (globalSettings.playbackRate !== undefined) {
        this.xgplayer.playbackRate = globalSettings.playbackRate;
        // moduleLoggers.PlayerInstance.info(
        //   `应用全局播放速度: ${globalSettings.playbackRate}x`,
        // );
      }

      if (globalSettings.volume !== undefined) {
        this.xgplayer.volume = globalSettings.volume;
        // moduleLoggers.PlayerInstance.info(
        //   `应用全局音量: ${globalSettings.volume}`,
        // );
      }

      if (globalSettings.muted !== undefined) {
        this.xgplayer.muted = globalSettings.muted;
        // moduleLoggers.PlayerInstance.info(
        //   `应用全局静音状态: ${globalSettings.muted}`,
        // );
      }
    } catch (error) {
      moduleLoggers.PlayerInstance.warn(
        `Failed to apply global playback settings:`,
        error,
      );
    }
  }

  /**
   * 处理全局播放设置变化
   */
  private handleGlobalPlaybackSettingsChange(event: any): void {
    if (!this.xgplayer) return;

    try {
      if (event.playbackRate !== undefined) {
        this.xgplayer.playbackRate = event.playbackRate;
        moduleLoggers.PlayerInstance.info(
          `Synchronized playback speed change: ${event.playbackRate}x`,
        );
      }

      if (event.volume !== undefined) {
        this.xgplayer.volume = event.volume;
        moduleLoggers.PlayerInstance.info(
          `Synchronized volume change: ${event.volume}`,
        );
      }

      if (event.muted !== undefined) {
        this.xgplayer.muted = event.muted;
        moduleLoggers.PlayerInstance.info(
          `Synchronized mute state change: ${event.muted}`,
        );
      }
    } catch (error) {
      moduleLoggers.PlayerInstance.warn(
        `Failed to synchronize global playback settings:`,
        error,
      );
    }
  }

  /**
   * 获取自定义插件实例
   */
  private getCustomHlsPlugin(): CustomHlsJsPlugin | null {
    if (!this.xgplayer) return null;
    return this.xgplayer.getPlugin("CustomHlsJsPlugin") as CustomHlsJsPlugin;
  }

  /**
   * 获取自定义插件的HLS实例
   */
  private getCustomHlsInstance(): any {
    if (!this.xgplayer) return null;

    // 安全检查plugins属性
    if (!this.xgplayer.plugins || !Array.isArray(this.xgplayer.plugins)) {
      moduleLoggers.PlayerInstance.warn(
        `plugins is not an array or does not exist: ${this.instanceId}`,
      );
      return (this.xgplayer as any).hls;
    }

    const customPlugin = this.xgplayer.plugins.find(
      (plugin: any) => plugin.constructor.name === "CustomHlsJsPlugin",
    );
    return customPlugin
      ? customPlugin.getHlsInstance()
      : (this.xgplayer as any).hls;
  }

  public setOnEnded(
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ) {
    moduleLoggers.PlayerInstance.info(
      `Setting onEnded callback: ${this.instanceId}, onEnded: ${!!onEnded}`,
    );
    this.config.onEnded = onEnded;
  }

  /**
   * 手动恢复网络状态
   */
  public manualRecovery(): void {
    moduleLoggers.PlayerInstance.info(
      `Manually recovering network state: ${this.instanceId}`,
    );

    // 重置网络状态
    this.networkStateManager.manualRecovery();

    // 获取自定义插件的HLS实例并恢复
    const customHlsPlugin = this.getCustomHlsPlugin();
    if (customHlsPlugin) {
      customHlsPlugin.resumeHlsLoading();
      moduleLoggers.PlayerInstance.info(
        `HLS instance reloaded: ${this.instanceId}`,
      );
    }

    // 如果播放器暂停了，可以选择自动恢复播放
    if (this.xgplayer && this.xgplayer.paused && this.hasAuthSucceeded) {
      this.play();
    }
  }

  /**
   * 检查网络状态
   */
  public checkNetworkStatus(): boolean {
    const networkState = this.networkStateManager.getNetworkState();
    return networkState.isOnline && !networkState.isBlocked;
  }

  /**
   * 剧集切换后确保HLS开始加载
   */
  private ensureHlsLoadingAfterEpisodeSwitch(): void {
    const networkState = this.networkStateManager.getNetworkState();
    const currentInstance = this.stateManager.getInstance(this.instanceId);
    const isActive = currentInstance?.isActive || false;

    if (isActive && networkState.isOnline && !networkState.isBlocked) {
      const customHlsPlugin = this.getCustomHlsPlugin();
      if (customHlsPlugin) {
        customHlsPlugin.resumeHlsLoading();
        moduleLoggers.PlayerInstance.info(
          `Episode switch: ensured HLS loading for active instance: ${this.instanceId}`,
        );
      }
    }
  }
}
