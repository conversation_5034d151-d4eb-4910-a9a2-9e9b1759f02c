import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

import { PlayerFactory } from "../factories/PlayerFactory";
import { EventManager } from "../managers/EventManager";
import { PreloadManager } from "../managers/PreloadManager";
import { PlayerInstance } from "./PlayerInstance";

/**
 * @file LifecycleController.ts
 * @description 生命周期控制器 (LC)
 * - 集中管理所有播放器实例的生命周期事件。
 * - 实现智能的多实例管理策略：
 *   - 核心3个实例：上一集、当前播放、下一集（永不销毁）
 *   - 跳集时最多保留5个实例（核心3个 + 跳集后的当前播放 + 跳集后的下一集）
 *   - 自动清理超出限制的实例
 */

export interface InstanceInfo {
  id: string;
  episodeNo: number;
  createdAt: number;
  lastAccessed: number;
  isCore: boolean; // 是否为核心实例（上一集、当前播放、下一集）
}

export class LifecycleController {
  private instances: Map<string, PlayerInstance> = new Map();
  private instanceInfo: Map<string, InstanceInfo> = new Map();
  private playerFactory: PlayerFactory;
  private eventManager: EventManager;
  private preloadManager: PreloadManager;

  // 配置参数
  private readonly MAX_CORE_INSTANCES = 3; // 核心实例数量（上一集、当前播放、下一集）
  private readonly MAX_TOTAL_INSTANCES = 5; // 最大总实例数量
  private readonly CORE_INSTANCE_TTL = 24 * 60 * 60 * 1000; // 核心实例TTL（24小时）
  private readonly NON_CORE_INSTANCE_TTL = 30 * 60 * 1000; // 非核心实例TTL（30分钟）

  constructor(playerFactory: PlayerFactory, eventManager: EventManager) {
    this.playerFactory = playerFactory;
    this.eventManager = eventManager;
    this.preloadManager = PreloadManager.getInstance();

    moduleLoggers.LifecycleController.info(
      "LifecycleController initialized with smart instance management",
    );
  }

  /**
   * 创建播放器实例
   */
  public createInstance(
    id: string,
    containerId: string,
    videoData: IPlayerVideoData,
    autoPlay: boolean = false,
    startTime: number = 0,
    currentEpisode?: IPlayerEpisode,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): PlayerInstance {
    const now = Date.now();
    const episodeNo = currentEpisode?.no || 1;

    // 检查是否已存在实例
    if (this.instances.has(id)) {
      const existingInstance = this.instances.get(id)!;
      const existingInfo = this.instanceInfo.get(id);

      if (existingInfo) {
        // 更新访问时间
        existingInfo.lastAccessed = now;
        moduleLoggers.LifecycleController.info(
          `实例已存在，更新访问时间: ${id} (第${episodeNo}集)`,
        );
      }

      return existingInstance;
    }

    // 创建新实例前进行清理检查
    this.enforceInstanceLimits(episodeNo);

    // 创建播放器实例
    const instance = this.playerFactory.createPlayer({
      id,
      containerId,
      videoData,
      autoPlay,
      startTime,
      onEnded,
      currentEpisode,
    });

    // 注册实例信息
    const isCore = this.isCoreInstance(episodeNo, videoData);
    const instanceInfo: InstanceInfo = {
      id,
      episodeNo,
      createdAt: now,
      lastAccessed: now,
      isCore,
    };

    this.instances.set(id, instance);
    this.instanceInfo.set(id, instanceInfo);

    moduleLoggers.LifecycleController.info(
      `创建播放器实例: ${id} (第${episodeNo}集, ${isCore ? "核心" : "非核心"})`,
    );

    return instance;
  }

  /**
   * 获取或创建实例（惰性处理）
   */
  public getOrCreateInstance(
    id: string,
    containerId: string,
    videoData: IPlayerVideoData,
    autoPlay: boolean = false,
    startTime: number = 0,
    currentEpisode?: IPlayerEpisode,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): PlayerInstance {
    // 首先尝试获取现有实例
    let instance = this.instances.get(id);

    if (instance) {
      // 实例存在，更新访问时间
      const info = this.instanceInfo.get(id);
      if (info) {
        info.lastAccessed = Date.now();
      }

      // 如果传入了 onEnded 回调，更新实例的配置
      if (onEnded && instance.config.onEnded !== onEnded) {
        moduleLoggers.LifecycleController.info(
          `更新实例 ${id} 的 onEnded 回调（setOnEnded）`,
        );
        if (typeof instance.setOnEnded === "function") {
          instance.setOnEnded(onEnded);
        } else {
          instance.config.onEnded = onEnded;
        }
      }

      return instance;
    }

    // 实例不存在，创建新实例
    moduleLoggers.LifecycleController.info(
      `实例不存在，惰性创建: ${id} (第${currentEpisode?.no || 1}集)`,
    );

    return this.createInstance(
      id,
      containerId,
      videoData,
      autoPlay,
      startTime,
      currentEpisode,
      onEnded,
    );
  }

  /**
   * 检查实例是否存在
   */
  public hasInstance(id: string): boolean {
    return this.instances.has(id);
  }

  /**
   * 获取实例（如果存在）
   */
  public getInstance(id: string): PlayerInstance | undefined {
    return this.instances.get(id);
  }

  /**
   * 判断是否为核心实例（上一集、当前播放、下一集）
   */
  private isCoreInstance(
    episodeNo: number,
    videoData: IPlayerVideoData,
  ): boolean {
    const totalEpisodes = videoData.series.length;
    const currentEpisode = videoData.series.find((ep) => ep.no === episodeNo);

    if (!currentEpisode) return false;

    // 获取当前活跃播放器的集数
    const activeInstance = this.getActivePlayer();
    if (!activeInstance) {
      // 如果活跃播放器不存在，直接返回 false，避免递归调用
      return false;
    }

    const activeEpisodeNo = this.getEpisodeNoFromInstance(activeInstance);
    if (!activeEpisodeNo) return false;

    // 核心实例：上一集、当前播放、下一集
    const coreEpisodes = [
      activeEpisodeNo - 1,
      activeEpisodeNo,
      activeEpisodeNo + 1,
    ].filter((no) => no >= 1 && no <= totalEpisodes);

    return coreEpisodes.includes(episodeNo);
  }

  /**
   * 从实例中获取集数
   */
  private getEpisodeNoFromInstance(instance: PlayerInstance): number | null {
    const instanceInfo = this.instanceInfo.get(instance.instanceId);
    return instanceInfo?.episodeNo || null;
  }

  /**
   * 设置活跃播放器
   */
  public setActivePlayer(id: string): PlayerInstance | null {
    const instance = this.instances.get(id);
    if (instance) {
      // 更新访问时间
      const info = this.instanceInfo.get(id);
      if (info) {
        info.lastAccessed = Date.now();
      }

      // 确保状态管理器中的活跃实例已经设置
      const stateManager = (this.playerFactory as any).stateManager;
      if (stateManager) {
        // 先设置状态管理器中的活跃实例
        stateManager.setActiveInstance(id);
        this.recalculateCoreInstances();
      } else {
        // 如果没有状态管理器，直接重新评估
        this.recalculateCoreInstances();
      }

      moduleLoggers.LifecycleController.info(`设置活跃播放器: ${id}`);
      return instance;
    }

    // 实例不存在，记录警告但不抛出错误
    moduleLoggers.LifecycleController.warn(
      `设置活跃播放器失败，实例不存在: ${id}`,
    );
    return null;
  }

  /**
   * 重新计算核心实例状态
   */
  private recalculateCoreInstances(): void {
    const activeInstance = this.getActivePlayer();
    if (!activeInstance) {
      // 如果活跃播放器不存在，直接返回，避免递归调用
      moduleLoggers.LifecycleController.warn(
        "无法重新计算核心实例状态，活跃播放器不存在",
      );
      return;
    }

    const activeEpisodeNo = this.getEpisodeNoFromInstance(activeInstance);
    if (!activeEpisodeNo) return;

    // 获取视频数据
    const videoData = this.getVideoDataFromInstance(activeInstance);
    if (!videoData) return;

    // 更新所有实例的核心状态
    this.instanceInfo.forEach((info, id) => {
      const wasCore = info.isCore;
      info.isCore = this.isCoreInstance(info.episodeNo, videoData);

      if (wasCore !== info.isCore) {
        moduleLoggers.LifecycleController.info(
          `实例核心状态变更: ${id} (第${info.episodeNo}集) ${wasCore ? "核心" : "非核心"} -> ${info.isCore ? "核心" : "非核心"}`,
        );
      }
    });

    // 执行清理
    this.enforceInstanceLimits(activeEpisodeNo);
  }

  /**
   * 从实例中获取视频数据
   */
  private getVideoDataFromInstance(
    instance: PlayerInstance,
  ): IPlayerVideoData | null {
    // 通过状态管理器获取实例的视频数据
    const stateManager = (this.playerFactory as any).stateManager;
    if (stateManager) {
      const instanceState = stateManager.getInstance(instance.instanceId);
      return instanceState?.videoData || null;
    }
    return null;
  }

  /**
   * 获取活跃播放器
   */
  public getActivePlayer(): PlayerInstance | null {
    // 通过状态管理器获取活跃实例ID
    const stateManager = (this.playerFactory as any).stateManager;
    if (stateManager) {
      const activeInstanceId = stateManager.getActiveInstance()?.id;
      if (activeInstanceId) {
        return this.instances.get(activeInstanceId) || null;
      }
    }
    return null;
  }

  /**
   * 安全地获取活跃播放器（如果不存在则尝试重新创建）
   */
  public getOrCreateActivePlayer(): PlayerInstance | null {
    const activeInstance = this.getActivePlayer();
    if (activeInstance) {
      return activeInstance;
    }

    // 如果活跃播放器不存在，尝试从状态管理器获取信息并重新创建
    const stateManager = (this.playerFactory as any).stateManager;
    if (stateManager) {
      const activeInstanceState = stateManager.getActiveInstance();
      if (
        activeInstanceState?.videoData &&
        activeInstanceState.currentEpisode
      ) {
        const videoData = activeInstanceState.videoData;
        const episode = activeInstanceState.currentEpisode;
        const id = activeInstanceState.id;
        const containerId = `container-${id}`;

        moduleLoggers.LifecycleController.info(
          `活跃播放器不存在，尝试重新创建: ${id}`,
        );

        return this.getOrCreateInstance(
          id,
          containerId,
          videoData,
          false,
          0,
          episode,
        );
      }
    }

    return null;
  }

  /**
   * 强制实例数量限制
   */
  private enforceInstanceLimits(currentEpisodeNo: number): void {
    const now = Date.now();
    const totalInstances = this.instances.size;

    // 如果总实例数未超过限制，不需要清理
    if (totalInstances <= this.MAX_TOTAL_INSTANCES) {
      return;
    }

    moduleLoggers.LifecycleController.info(
      `实例数量超限 (${totalInstances}/${this.MAX_TOTAL_INSTANCES})，开始清理`,
    );

    // 按优先级排序实例（核心实例 > 最近访问 > 创建时间）
    const sortedInstances = Array.from(this.instanceInfo.entries()).sort(
      ([, a], [, b]) => {
        // 1. 核心实例优先保留
        if (a.isCore && !b.isCore) return -1;
        if (!a.isCore && b.isCore) return 1;

        // 2. 最近访问时间优先
        if (a.lastAccessed !== b.lastAccessed) {
          return b.lastAccessed - a.lastAccessed;
        }

        // 3. 创建时间优先
        return b.createdAt - a.createdAt;
      },
    );

    // 计算需要清理的实例数量
    const instancesToRemove = totalInstances - this.MAX_TOTAL_INSTANCES;
    const instancesToClean = sortedInstances.slice(-instancesToRemove);

    // 清理实例
    instancesToClean.forEach(([id, info]) => {
      if (!info.isCore) {
        // 只清理非核心实例
        moduleLoggers.LifecycleController.info(
          `清理非核心实例: ${id} (第${info.episodeNo}集)`,
        );
        this.destroyInstance(id);
      }
    });

    // 如果清理后仍然超限，强制清理最旧的实例
    if (this.instances.size > this.MAX_TOTAL_INSTANCES) {
      const remainingToClean = this.instances.size - this.MAX_TOTAL_INSTANCES;
      const oldestInstances = sortedInstances
        .slice(0, -this.MAX_TOTAL_INSTANCES)
        .slice(0, remainingToClean);

      oldestInstances.forEach(([id, info]) => {
        moduleLoggers.LifecycleController.warn(
          `强制清理实例: ${id} (第${info.episodeNo}集)`,
        );
        this.destroyInstance(id);
      });
    }
  }

  /**
   * 定期清理过期实例
   */
  public cleanupExpiredInstances(): void {
    const now = Date.now();
    const instancesToRemove: string[] = [];

    this.instanceInfo.forEach((info, id) => {
      const ttl = info.isCore
        ? this.CORE_INSTANCE_TTL
        : this.NON_CORE_INSTANCE_TTL;
      const isExpired = now - info.lastAccessed > ttl;

      if (isExpired) {
        instancesToRemove.push(id);
        moduleLoggers.LifecycleController.info(
          `实例已过期: ${id} (第${info.episodeNo}集, ${info.isCore ? "核心" : "非核心"})`,
        );
      }
    });

    // 清理过期实例
    instancesToRemove.forEach((id) => {
      this.destroyInstance(id);
    });

    if (instancesToRemove.length > 0) {
      moduleLoggers.LifecycleController.info(
        `清理了 ${instancesToRemove.length} 个过期实例`,
      );
    }
  }

  /**
   * 销毁实例
   */
  public destroyInstance(id: string): void {
    const instance = this.instances.get(id);
    const info = this.instanceInfo.get(id);

    if (instance) {
      instance.destroy();
      this.instances.delete(id);
      this.instanceInfo.delete(id);

      // 同时清理 PlayerFactory 中的实例
      if (this.playerFactory) {
        this.playerFactory.destroyPlayer(id);
      }

      moduleLoggers.LifecycleController.info(
        `销毁实例: ${id}${info ? ` (第${info.episodeNo}集)` : ""}`,
      );
    }
  }

  /**
   * 获取实例统计信息
   */
  public getStats(): {
    total: number;
    core: number;
    nonCore: number;
    maxAllowed: number;
  } {
    let coreCount = 0;
    let nonCoreCount = 0;

    this.instanceInfo.forEach((info) => {
      if (info.isCore) {
        coreCount++;
      } else {
        nonCoreCount++;
      }
    });

    return {
      total: this.instances.size,
      core: coreCount,
      nonCore: nonCoreCount,
      maxAllowed: this.MAX_TOTAL_INSTANCES,
    };
  }

  /**
   * 获取所有实例信息
   */
  public getAllInstances(): InstanceInfo[] {
    return Array.from(this.instanceInfo.values());
  }

  /**
   * 获取核心实例
   */
  public getCoreInstances(): InstanceInfo[] {
    return Array.from(this.instanceInfo.values()).filter((info) => info.isCore);
  }

  /**
   * 预加载指定集数的实例
   */
  public preloadEpisode(
    videoData: IPlayerVideoData,
    episodeNo: number,
    containerId: string,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): PlayerInstance | null {
    const id = `episode-${videoData.id}-${episodeNo}`;
    const targetEpisode = videoData.series.find((ep) => ep.no === episodeNo);

    if (!targetEpisode) {
      moduleLoggers.LifecycleController.warn(
        `预加载失败，剧集不存在: 第${episodeNo}集`,
      );
      return null;
    }

    // 检查是否已存在
    if (this.instances.has(id)) {
      return this.instances.get(id)!;
    }

    // 检查是否需要清理实例
    this.enforceInstanceLimits(episodeNo);

    // 使用惰性处理创建预加载实例
    const instance = this.getOrCreateInstance(
      id,
      containerId,
      videoData,
      false, // 不自动播放
      0, // 从头开始
      targetEpisode,
      onEnded,
    );

    moduleLoggers.LifecycleController.info(
      `预加载实例: ${id} (第${episodeNo}集)`,
    );

    return instance;
  }

  /**
   * 处理剧集切换
   */
  public handleEpisodeSwitch(
    newEpisodeNo: number,
    videoData: IPlayerVideoData,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): void {
    moduleLoggers.LifecycleController.info(`处理剧集切换: 第${newEpisodeNo}集`);

    // 重新计算核心实例
    this.recalculateCoreInstances();

    // 使用智能预加载下一集（会自动终止之前的预加载任务）
    this.smartPreloadNextEpisode(videoData, newEpisodeNo, onEnded);

    // 执行清理
    this.enforceInstanceLimits(newEpisodeNo);
  }

  /**
   * 智能预加载下一集（集成 PreloadManager）
   */
  public smartPreloadNextEpisode(
    videoData: IPlayerVideoData,
    currentEpisodeNo: number,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): void {
    const totalEpisodes = videoData.series.length;
    const nextEpisodeNo = currentEpisodeNo + 1;

    if (nextEpisodeNo > totalEpisodes) {
      moduleLoggers.LifecycleController.info(`已是最后一集，无需预加载下一集`);
      return;
    }

    const nextEpisode = videoData.series.find((ep) => ep.no === nextEpisodeNo);
    if (!nextEpisode) {
      moduleLoggers.LifecycleController.warn(
        `下一集不存在: 第${nextEpisodeNo}集`,
      );
      return;
    }

    // 检查下一集是否需要权限
    if (nextEpisode.status === "locked") {
      moduleLoggers.LifecycleController.info(
        `跳过预加载第${nextEpisodeNo}集，需要权限 (状态: ${nextEpisode.status})`,
      );
      return;
    }

    // 终止当前视频的所有预加载任务
    this.cancelAllPreloadsForVideo(videoData.id);

    // 使用 PreloadManager 预加载下一集的 HLS 切片
    if (nextEpisode.url) {
      moduleLoggers.LifecycleController.info(
        `开始预加载第${nextEpisodeNo}集 HLS 切片`,
      );
      this.preloadManager.preloadEpisode(
        nextEpisode.url,
        videoData.id,
        nextEpisodeNo,
        true, // 使用 xgplayer 方式
      );
    }

    // 同时创建播放器实例（用于快速切换）
    const nextEpisodeId = `episode-${videoData.id}-${nextEpisodeNo}`;
    const nextContainerId = `container-${nextEpisodeId}`;

    this.getOrCreateInstance(
      nextEpisodeId,
      nextContainerId,
      videoData,
      false, // 不自动播放
      0, // 从头开始
      nextEpisode,
      onEnded,
    );

    moduleLoggers.LifecycleController.info(
      `智能预加载完成: 第${nextEpisodeNo}集 (HLS切片 + 播放器实例)`,
    );
  }

  /**
   * 取消指定视频的所有预加载任务
   */
  private cancelAllPreloadsForVideo(videoId: string): void {
    const preloadStatus = this.preloadManager.getPreloadStatus();
    const videoTasks = preloadStatus.tasks.filter(
      (task) => task.videoId === videoId,
    );

    if (videoTasks.length > 0) {
      moduleLoggers.LifecycleController.info(
        `取消视频 ${videoId} 的所有预加载任务: ${videoTasks.length}个`,
      );

      videoTasks.forEach((task) => {
        this.preloadManager.cancelPreload(task.videoId, task.episodeNo);
        moduleLoggers.LifecycleController.info(
          `已取消预加载: 第${task.episodeNo}集`,
        );
      });
    }
  }

  /**
   * 获取预加载状态
   */
  public getPreloadStatus() {
    return this.preloadManager.getPreloadStatus();
  }

  /**
   * 清理所有实例
   */
  public destroyAllInstances(): void {
    const instanceIds = Array.from(this.instances.keys());
    moduleLoggers.LifecycleController.info(
      `清理所有实例，共 ${instanceIds.length} 个`,
    );
    moduleLoggers.LifecycleController.info(
      `实例列表: ${instanceIds.join(", ")}`,
    );

    instanceIds.forEach((id) => {
      moduleLoggers.LifecycleController.info(`销毁实例: ${id}`);
      this.destroyInstance(id);
    });

    moduleLoggers.LifecycleController.info(
      `清理完成，剩余实例数量: ${this.instances.size}`,
    );
  }
}
