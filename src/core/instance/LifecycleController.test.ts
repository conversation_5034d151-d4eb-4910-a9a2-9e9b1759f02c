import { beforeEach, describe, expect, it, vi } from "vitest";

import { IPlayerVideoData } from "@/services/AuthManager";

import { PlayerFactory } from "../factories/PlayerFactory";
import { EventManager } from "../managers/EventManager";
import { PreloadManager } from "../managers/PreloadManager";
import { LifecycleController } from "./LifecycleController";

// Mock dependencies
vi.mock("@/utils/LogManager");
vi.mock("../factories/PlayerFactory");
vi.mock("../managers/EventManager");
vi.mock("../managers/StateManager");

// Mock PreloadManager
vi.mock("../managers/PreloadManager", () => ({
  PreloadManager: {
    getInstance: vi.fn(() => ({
      preloadEpisode: vi.fn(),
      cancelPreload: vi.fn(),
      getPreloadStatus: vi.fn(() => ({
        total: 0,
        active: 0,
        tasks: [],
      })),
    })),
  },
}));

describe("LifecycleController", () => {
  let lifecycleController: LifecycleController;
  let mockPlayerFactory: any;
  let mockEventManager: any;
  let mockStateManager: any;
  let mockVideoData: IPlayerVideoData;
  let mockPreloadManager: any;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create mock instances
    mockPlayerFactory = {
      createPlayer: vi.fn(),
      getPlayer: vi.fn(),
      destroyPlayer: vi.fn(),
      getAllPlayers: vi.fn(),
      getStats: vi.fn(),
      stateManager: {
        setActiveInstance: vi.fn(),
        getActiveInstance: vi.fn(() => ({ id: "test-instance" })),
        getInstance: vi.fn(),
      },
    };

    mockEventManager = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    };

    mockStateManager = {
      getInstance: vi.fn(),
      getActiveInstance: vi.fn(),
      createInstance: vi.fn(),
      updateInstance: vi.fn(),
      removeInstance: vi.fn(),
    };

    mockPreloadManager = {
      preloadEpisode: vi.fn(),
      cancelPreload: vi.fn(),
      getPreloadStatus: vi.fn(() => ({
        total: 0,
        active: 0,
        tasks: [],
      })),
    };

    // Mock video data
    mockVideoData = {
      id: "test-video-1",
      title: "Test Video",
      series: [
        {
          id: "ep1",
          no: 1,
          // title: "Episode 1",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep1",
          status: "paid",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep2",
          no: 2,
          // title: "Episode 2",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep2",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep3",
          no: 3,
          // title: "Episode 3",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep3",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep4",
          no: 4,
          // title: "Episode 4",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep4",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep5",
          no: 5,
          // title: "Episode 5",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep5",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
      ],
      description: "Test Video",
      tags: [],
      cover: {
        src: "http://example.com/cover.jpg",
        alt: "Test Video",
        width: 100,
        height: 100,
        is_last_play: false,
      },
      operate: { liked: false, collected: false, subscribed: true },
      metaFields: {},
    };

    // Mock player factory to return stateManager
    mockPlayerFactory.stateManager = mockStateManager;

    // Set PreloadManager mock return value
    (PreloadManager.getInstance as any).mockReturnValue(mockPreloadManager);

    // Create lifecycle controller
    lifecycleController = new LifecycleController(
      mockPlayerFactory,
      mockEventManager,
    );
  });

  describe("实例创建和管理", () => {
    it("应该创建新的播放器实例", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      const instance = lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      expect(instance).toBe(mockInstance);
      expect(mockPlayerFactory.createPlayer).toHaveBeenCalledWith({
        id: "episode-test-video-1-1",
        containerId: "container-episode-test-video-1-1",
        videoData: mockVideoData,
        autoPlay: false,
        startTime: 0,
        currentEpisode: mockVideoData.series[0],
      });
    });

    it("应该返回已存在的实例", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 创建第一次
      lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      // 创建第二次（应该返回已存在的实例）
      const secondInstance = lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      expect(secondInstance).toBe(mockInstance);
      expect(mockPlayerFactory.createPlayer).toHaveBeenCalledTimes(1);
    });
  });

  describe("实例统计", () => {
    it("应该返回正确的统计信息", () => {
      // 创建几个实例
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      lifecycleController.createInstance(
        "episode-test-video-1-2",
        "container-episode-test-video-1-2",
        mockVideoData,
        false,
        0,
        mockVideoData.series[1],
      );

      const stats = lifecycleController.getStats();

      expect(stats.total).toBe(2);
      expect(stats.maxAllowed).toBe(5);
    });
  });

  describe("实例清理", () => {
    it("应该清理过期实例", () => {
      // 启用假定时器
      vi.useFakeTimers();

      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 创建实例
      lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      // 模拟时间过去
      vi.advanceTimersByTime(31 * 60 * 1000); // 31分钟

      // 清理过期实例
      lifecycleController.cleanupExpiredInstances();

      expect(mockInstance.destroy).toHaveBeenCalled();

      // 恢复真实定时器
      vi.useRealTimers();
    });
  });

  describe("剧集切换", () => {
    it("应该处理剧集切换", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
        playEpisode: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);
      mockPlayerFactory.getPlayer.mockReturnValue(mockInstance);

      // 模拟活跃实例
      mockStateManager.getActiveInstance.mockReturnValue({
        id: "episode-test-video-1-1",
        videoData: mockVideoData,
        currentEpisode: mockVideoData.series[0],
      });

      lifecycleController.handleEpisodeSwitch(2, mockVideoData);

      // 验证是否尝试预加载相邻集数
      expect(mockPlayerFactory.createPlayer).toHaveBeenCalled();
    });
  });

  describe("实例限制", () => {
    it("应该限制最大实例数量", () => {
      const mockInstance = {
        instanceId: "test-instance",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 模拟活跃实例状态，确保核心实例计算正确
      mockStateManager.getActiveInstance.mockReturnValue({
        id: "episode-test-video-1-1",
        videoData: mockVideoData,
        currentEpisode: mockVideoData.series[0],
      });

      // 只创建5个实例，保证currentEpisode有效
      for (let i = 1; i <= 5; i++) {
        lifecycleController.createInstance(
          `episode-test-video-1-${i}`,
          `container-episode-test-video-1-${i}`,
          mockVideoData,
          false,
          0,
          mockVideoData.series[i - 1],
        );
      }

      // 主动触发一次核心实例重算和清理
      lifecycleController["recalculateCoreInstances"]();

      // 验证实例数量被限制
      const stats = lifecycleController.getStats();
      expect(stats.total).toBeLessThanOrEqual(5);
    });
  });

  describe("惰性处理", () => {
    it("应该能够惰性创建实例", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 使用惰性处理获取或创建实例
      const instance = lifecycleController.getOrCreateInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      expect(instance).toBe(mockInstance);
      expect(mockPlayerFactory.createPlayer).toHaveBeenCalled();
    });

    it("应该能够检查实例是否存在", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 创建实例
      lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      // 检查实例是否存在
      expect(lifecycleController.hasInstance("episode-test-video-1-1")).toBe(
        true,
      );
      expect(lifecycleController.hasInstance("episode-test-video-1-2")).toBe(
        false,
      );
    });

    it("应该能够获取实例", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 创建实例
      lifecycleController.createInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      // 获取实例
      const retrievedInstance = lifecycleController.getInstance(
        "episode-test-video-1-1",
      );
      expect(retrievedInstance).toBe(mockInstance);

      // 获取不存在的实例
      const nonExistentInstance = lifecycleController.getInstance(
        "episode-test-video-1-2",
      );
      expect(nonExistentInstance).toBeUndefined();
    });
  });

  describe("智能预加载", () => {
    describe("smartPreloadNextEpisode", () => {
      const mockVideoData = {
        id: "test-video",
        series: [
          { no: 1, status: "unlocked", url: "http://example.com/ep1.m3u8" },
          { no: 2, status: "unlocked", url: "http://example.com/ep2.m3u8" },
          { no: 3, status: "locked", url: "http://example.com/ep3.m3u8" },
          { no: 4, status: "unlocked", url: "http://example.com/ep4.m3u8" },
        ],
      };

      it("应该预加载下一集并取消之前的预加载任务", () => {
        // 模拟有正在进行的预加载任务
        mockPreloadManager.getPreloadStatus.mockReturnValue({
          total: 2,
          active: 2,
          tasks: [
            { videoId: "test-video", episodeNo: 1, timestamp: Date.now() },
            { videoId: "test-video", episodeNo: 3, timestamp: Date.now() },
          ],
        });

        // 调用智能预加载
        lifecycleController.smartPreloadNextEpisode(mockVideoData, 1);

        // 验证取消了之前的预加载任务
        expect(mockPreloadManager.cancelPreload).toHaveBeenCalledWith(
          "test-video",
          1,
        );
        expect(mockPreloadManager.cancelPreload).toHaveBeenCalledWith(
          "test-video",
          3,
        );

        // 验证开始预加载第2集
        expect(mockPreloadManager.preloadEpisode).toHaveBeenCalledWith(
          "http://example.com/ep2.m3u8",
          "test-video",
          2,
          true,
        );
      });

      it("应该跳过需要权限的下一集", () => {
        // 调用智能预加载（当前是第2集，下一集第3集需要权限）
        lifecycleController.smartPreloadNextEpisode(mockVideoData, 2);

        // 验证没有开始预加载
        expect(mockPreloadManager.preloadEpisode).not.toHaveBeenCalled();
      });

      it("应该处理最后一集的情况", () => {
        // 调用智能预加载（当前是第4集，没有下一集）
        lifecycleController.smartPreloadNextEpisode(mockVideoData, 4);

        // 验证没有开始预加载
        expect(mockPreloadManager.preloadEpisode).not.toHaveBeenCalled();
      });

      it("应该处理下一集不存在的情况", () => {
        const incompleteVideoData = {
          id: "test-video",
          series: [
            { no: 1, status: "unlocked", url: "http://example.com/ep1.m3u8" },
          ],
        };

        // 调用智能预加载（当前是第1集，但下一集不存在）
        lifecycleController.smartPreloadNextEpisode(incompleteVideoData, 1);

        // 验证没有开始预加载
        expect(mockPreloadManager.preloadEpisode).not.toHaveBeenCalled();
      });
    });

    describe("handleEpisodeSwitch", () => {
      const mockVideoData = {
        id: "test-video",
        series: [
          { no: 1, status: "unlocked", url: "http://example.com/ep1.m3u8" },
          { no: 2, status: "unlocked", url: "http://example.com/ep2.m3u8" },
        ],
      };

      it("应该调用智能预加载下一集", () => {
        // 模拟有正在进行的预加载任务
        mockPreloadManager.getPreloadStatus.mockReturnValue({
          total: 1,
          active: 1,
          tasks: [
            { videoId: "test-video", episodeNo: 1, timestamp: Date.now() },
          ],
        });

        // 调用剧集切换
        lifecycleController.handleEpisodeSwitch(1, mockVideoData);

        // 验证取消了之前的预加载任务
        expect(mockPreloadManager.cancelPreload).toHaveBeenCalledWith(
          "test-video",
          1,
        );

        // 验证开始预加载第2集
        expect(mockPreloadManager.preloadEpisode).toHaveBeenCalledWith(
          "http://example.com/ep2.m3u8",
          "test-video",
          2,
          true,
        );
      });
    });

    describe("cancelAllPreloadsForVideo", () => {
      it("应该取消指定视频的所有预加载任务", () => {
        // 模拟有多个预加载任务
        mockPreloadManager.getPreloadStatus.mockReturnValue({
          total: 3,
          active: 3,
          tasks: [
            { videoId: "test-video", episodeNo: 1, timestamp: Date.now() },
            { videoId: "test-video", episodeNo: 2, timestamp: Date.now() },
            { videoId: "other-video", episodeNo: 1, timestamp: Date.now() },
          ],
        });

        // 调用取消预加载
        (lifecycleController as any).cancelAllPreloadsForVideo("test-video");

        // 验证只取消了 test-video 的预加载任务
        expect(mockPreloadManager.cancelPreload).toHaveBeenCalledWith(
          "test-video",
          1,
        );
        expect(mockPreloadManager.cancelPreload).toHaveBeenCalledWith(
          "test-video",
          2,
        );
        expect(mockPreloadManager.cancelPreload).not.toHaveBeenCalledWith(
          "other-video",
          1,
        );
      });

      it("应该处理没有预加载任务的情况", () => {
        // 模拟没有预加载任务
        mockPreloadManager.getPreloadStatus.mockReturnValue({
          total: 0,
          active: 0,
          tasks: [],
        });

        // 调用取消预加载
        (lifecycleController as any).cancelAllPreloadsForVideo("test-video");

        // 验证没有调用取消方法
        expect(mockPreloadManager.cancelPreload).not.toHaveBeenCalled();
      });
    });
  });
});
