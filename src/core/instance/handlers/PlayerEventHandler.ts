import Player from "xgplayer";

import { moduleLoggers } from "@/utils/LogManager";

import { PlayerEvents } from "../../managers/EventManager";
import { PlayerInstance } from "../PlayerInstance";

export class PlayerEventHandler {
  private pi: PlayerInstance;

  private loadStartTime = 0;
  private playTrackId = "";
  private playTrackIdBindEpisodeId = "";
  private playStartTimeStamp = 0;
  private lastProgressTrackTime = 0;
  private boundHandlePageHide: () => void;
  private boundHandleBeforeUnload: () => void;
  private isSeeking = false;
  private lastSeekEndTime = 0;
  private hasLoadedData = false;
  private pendingPlayDrama = false;

  private isFirstPlay = true; // 标识是否是初次播放

  constructor(playerInstance: PlayerInstance) {
    this.pi = playerInstance;
    this.boundHandlePageHide = this.handlePageHide.bind(this);
    this.boundHandleBeforeUnload = this.handleBeforeUnload.bind(this);
  }

  /**
   * 页面隐藏/关闭时的处理逻辑
   */
  private handlePageHide(): void {
    if (!this.pi.xgplayer || this.pi.xgplayer.ended) {
      return;
    }
    const state = this.pi.stateManager.getInstance(this.pi.instanceId);
    if (!state) return;

    const { videoData, currentEpisode, cumulatedWatchedTime } = state;
    const isPlaying = this.pi.xgplayer && !this.pi.xgplayer.paused;

    // 仅当视频正在播放或已加载但未结束时才上报
    if (
      videoData &&
      currentEpisode &&
      (isPlaying ||
        (this.pi.xgplayer.played && this.pi.xgplayer.played.length > 0))
    ) {
      const watchedTime = cumulatedWatchedTime || 0;
      // cumulatedWatchedTime 是以毫秒为单位，10秒 = 10000毫秒
      if (watchedTime > 0 && watchedTime < 10 * 1000) {
        moduleLoggers.PlayerEventHandler.info(
          `⏱️ [Tracking] 播放时间 < 10s (关闭页面): ${(
            watchedTime / 1000
          ).toFixed(2)}s, 上报 tenSecExitPlay`,
        );
        this.pi.trackingManager.track10ExitPlay(videoData, currentEpisode);
      }
    }
  }

  private handleBeforeUnload(): void {
    if (!this.pi.xgplayer || this.pi.xgplayer.ended) {
      return;
    }
    const state = this.pi.stateManager.getInstance(this.pi.instanceId);
    if (!state) return;

    const { videoData, currentEpisode, cumulatedWatchedTime } = state;
    const isPlaying = this.pi.xgplayer && !this.pi.xgplayer.paused;

    // 仅当视频正在播放或已加载但未结束时才上报
    if (
      videoData &&
      currentEpisode &&
      (isPlaying ||
        (this.pi.xgplayer.played && this.pi.xgplayer.played.length > 0))
    ) {
      const watchedTime = cumulatedWatchedTime || 0;
      // cumulatedWatchedTime 是以毫秒为单位，10秒 = 10000毫秒
      if (watchedTime > 0 && watchedTime < 10 * 1000) {
        moduleLoggers.PlayerEventHandler.info(
          `⏱️ [Tracking] 播放时间 < 10s (beforeunload): ${(
            watchedTime / 1000
          ).toFixed(2)}s, 上报 tenSecExitPlay`,
        );
        this.pi.trackingManager.track10ExitPlay(videoData, currentEpisode);
      }
    }
  }

  /**
   * 初始化播放追踪ID - 在剧集切换时调用
   */
  public initPlayTrackId(): void {
    const state = this.pi.stateManager.getInstance(this.pi.instanceId);
    const currentEpisodeId = state?.currentEpisode?.id;

    // 每次调用都重新生成playTrackId，包括切换回同一个剧集的情况
    this.playTrackIdBindEpisodeId = currentEpisodeId || "";

    const clientId = window.csTracker?.getClientId();
    this.playTrackId = `${clientId || this.pi.instanceId}-${Date.now()}`;
    this.playStartTimeStamp = Date.now();

    if (currentEpisodeId) {
      moduleLoggers.PlayerEventHandler.info(
        `🎯 [PlayerEventHandler] 为剧集 ${currentEpisodeId} 重新生成 playTrackId: ${this.playTrackId}`,
      );
    } else {
      moduleLoggers.PlayerEventHandler.info(
        `🎯 [PlayerEventHandler] 初始化默认 playTrackId: ${this.playTrackId}`,
      );
    }
  }

  public getPlayTrackId(): string {
    return this.playTrackId;
  }

  public resetTrackers(): void {
    this.lastProgressTrackTime = 0;
    this.isSeeking = false;
    this.lastSeekEndTime = 0;
    this.hasLoadedData = false;
    this.pendingPlayDrama = false;
    this.isFirstPlay = true; // 重置为初次播放状态

    // 重置播放器的累计观看时间和播放位置
    if (this.pi.xgplayer) {
      this.pi.xgplayer.resetState();
      moduleLoggers.PlayerEventHandler.info(
        `重置播放器状态: ${this.pi.instanceId}`,
      );
    }

    // 重置30秒播放追踪状态
    const state = this.pi.stateManager.getInstance(this.pi.instanceId);
    if (state?.currentEpisode) {
      this.pi.trackingManager.resetThirtySecondPlayTracking(
        state.currentEpisode.id,
      );
    }
  }

  /**
   * 设置事件监听器
   */
  public setupEventListeners(): void {
    if (!this.pi.xgplayer) {
      return;
    }
    moduleLoggers.PlayerEventHandler.info(
      `🎧 [PlayerInstance] 设置事件监听器: ${this.pi.instanceId}`,
    );

    window.addEventListener("pagehide", this.boundHandlePageHide);
    window.addEventListener("beforeunload", this.boundHandleBeforeUnload);

    const getEventPayload = (
      override: Record<string, unknown> = {},
    ): Record<string, unknown> => {
      const state = this.pi.stateManager.getInstance(this.pi.instanceId);
      const videoId = state?.videoData?.id ?? "";
      const episodeId = state?.currentEpisode?.id ?? "";
      const episodeIndex = state?.currentEpisode?.no ?? -1;

      return {
        videoId,
        episodeId,
        episodeIndex,
        ...override,
      };
    };

    // Fired when the player starts loading the video.
    this.pi.xgplayer.on(Player.Events.LOAD_START, () => {
      this.loadStartTime = Date.now();
      this.pi.eventManager.emit(
        "loadStart",
        getEventPayload() as PlayerEvents["loadStart"],
      );
    });

    // Fired when the player has loaded data.
    this.pi.xgplayer.on(Player.Events.LOADED_DATA, () => {
      const state = this.pi.stateManager.getInstance(this.pi.instanceId);
      if (this.loadStartTime > 0 && state?.videoData && state?.currentEpisode) {
        const loadDuration = Date.now() - this.loadStartTime;
        this.pi.trackingManager.trackLoadComplete(
          state.videoData,
          state.currentEpisode,
          loadDuration,
        );
        this.loadStartTime = 0; // Reset for next load
      }

      // 标记已触发loaded_data事件
      this.hasLoadedData = true;
      moduleLoggers.PlayerEventHandler.info(
        `📊 [PlayerEventHandler] loaded_data事件触发，标记hasLoadedData=true: ${this.pi.instanceId}`,
      );

      // 如果有待上报的playDrama事件，立即上报
      if (this.pendingPlayDrama) {
        this.reportPlayDrama();
      }

      this.pi.eventManager.emit(
        "loadedData",
        getEventPayload() as PlayerEvents["loadedData"],
      );
    });

    this.pi.xgplayer.on(Player.Events.PLAY, () => {
      moduleLoggers.PlayerEventHandler.info(
        `播放开始事件触发: ${this.pi.instanceId}`,
      );

      const customHlsPlugin = this.pi.xgplayer?.getPlugin("CustomHlsJsPlugin");
      if (customHlsPlugin && !customHlsPlugin.isStopHlsLoading) {
        moduleLoggers.PlayerEventHandler.info(
          `播放开始事件触发，恢复HLS加载: ${this.pi.instanceId}`,
        );
        customHlsPlugin.resumeHlsLoading();
      }

      // 上报时间校正, 初次播放时, 如果存在进度, 则会播放后 seek, 本次直接取 seek 后的时间
      let reportTime = this.pi.xgplayer?.currentTime ?? 0;

      if (
        this.isFirstPlay &&
        this.pi.startTime > 0 &&
        this.pi.startTime !== reportTime
      ) {
        reportTime = this.pi.startTime;
      }

      moduleLoggers.PlayerEventHandler.info(
        `上报播放时间: ${reportTime}s, 播放器时间: ${this.pi.xgplayer?.currentTime}s, 是否初次播放: ${this.isFirstPlay}, 开始时间: ${this.pi.startTime}s`,
      );

      this.pi.eventManager.emit(
        "play",
        getEventPayload({
          currentTime: reportTime,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["play"],
      );

      // 标记非初次播放
      if (this.isFirstPlay) {
        this.isFirstPlay = false;
        moduleLoggers.PlayerEventHandler.info(
          `初次播放成功，标记为非初次播放: ${this.pi.instanceId}`,
        );
      }

      // 防止重复鉴权
      if (this.pi.isAuthenticating) {
        moduleLoggers.PlayerEventHandler.info(
          `正在进行鉴权，跳过重复调用: ${this.pi.instanceId}`,
        );
        return;
      }

      const state = this.pi.stateManager.getInstance(this.pi.instanceId);
      const currentEpisode = state?.currentEpisode;
      const videoData = state?.videoData;

      // 如果没有原始URL，说明这是一个空视频，直接阻止播放
      if (!this.pi.originalVideoUrl) {
        moduleLoggers.PlayerEventHandler.warn(
          `⚠️ [PlayerInstance] 原始URL为空，阻止播放: ${this.pi.instanceId}`,
        );
        if (this.pi.xgplayer) {
          this.pi.xgplayer.pause();
        }
        return;
      }

      if (!videoData || !currentEpisode) {
        moduleLoggers.PlayerEventHandler.warn(
          `⚠️ [PlayerInstance] 缺少视频数据或集数信息: ${this.pi.instanceId}`,
        );
        if (this.pi.xgplayer) {
          this.pi.xgplayer.pause();
        }
        return;
      }

      const handlePlayTracking = () => {
        if (videoData && currentEpisode) {
          // 检查是否在拖拽结束后的短时间内（500ms内）
          const now = Date.now();
          const isRecentSeek = now - this.lastSeekEndTime < 500;

          moduleLoggers.PlayerEventHandler.info(
            `播放追踪检查: hasLoadedData=${this.hasLoadedData}, isSeeking=${this.isSeeking}, isRecentSeek=${isRecentSeek}, pendingPlayDrama=${this.pendingPlayDrama}, isFirstPlay=${this.isFirstPlay}: ${this.pi.instanceId}`,
          );

          // 放宽首次播放时的限制：如果是首次播放且不是最近拖拽结束，就允许处理 playDrama 事件
          const shouldHandlePlayDrama = this.isFirstPlay
            ? !isRecentSeek // 首次播放时，只要不是最近拖拽结束就处理
            : !this.isSeeking && !isRecentSeek; // 非首次播放时，使用原有逻辑

          moduleLoggers.PlayerEventHandler.info(
            `是否准备上报 playDrama: ${shouldHandlePlayDrama}`,
          );

          if (shouldHandlePlayDrama) {
            if (this.hasLoadedData) {
              // loaded_data已触发，立即上报playDrama事件
              moduleLoggers.PlayerEventHandler.info(
                `loaded_data已触发，立即上报 playDrama 事件: ${this.pi.instanceId}`,
              );
              this.pi.trackingManager.trackPlay(videoData, currentEpisode);
            } else {
              // loaded_data未触发，标记为待上报
              this.pendingPlayDrama = true;
              moduleLoggers.PlayerEventHandler.info(
                `loaded_data未触发，标记playDrama为待上报: ${this.pi.instanceId}`,
              );
            }
          } else {
            moduleLoggers.PlayerEventHandler.info(
              `跳过 playDrama 事件上报 - 正在拖拽或刚拖拽结束: ${this.pi.instanceId}, isSeeking: ${this.isSeeking}, isRecentSeek: ${isRecentSeek}, isFirstPlay: ${this.isFirstPlay}`,
            );
          }

          // 上报播放时长 - 开始
          this.pi.trackingManager.trackDuration(videoData, currentEpisode, {
            playId: this.playTrackId,
            playStartAt: this.playStartTimeStamp,
            status: "start",
            vDuration: this.pi.xgplayer?.duration ?? 0,
            wDuration: this.pi.xgplayer?.currentTime ?? 0,
          });
          // 设置时间以进行进度跟踪
          this.lastProgressTrackTime = Date.now();
        }
      };

      // 如果之前鉴权已经成功，直接允许播放
      if (this.pi.hasAuthSucceeded) {
        moduleLoggers.PlayerEventHandler.info(
          `之前鉴权已成功，允许播放: ${this.pi.instanceId}`,
        );
        this.pi.stateManager.updateInstance(this.pi.instanceId, {
          status: "playing",
        });
        handlePlayTracking();
        return;
      }

      // 如果之前鉴权失败或未鉴权，重新尝试鉴权
      moduleLoggers.PlayerEventHandler.info(
        `🔐 [PlayerInstance] 重新尝试鉴权检查: 视频ID=${videoData.id}, 集数ID=${currentEpisode.id}`,
      );

      this.pi.isAuthenticating = true;

      try {
        const hasPermission = !!this.pi.originalVideoUrl;

        if (hasPermission) {
          moduleLoggers.PlayerEventHandler.info(
            `✅ [PlayerInstance] 鉴权成功，更新视频URL并开始播放: ${this.pi.instanceId}`,
          );
          this.pi.hasAuthSucceeded = true;

          // 鉴权成功后，更新播放器的URL
          if (this.pi.xgplayer && this.pi.originalVideoUrl) {
            this.pi.xgplayer.src = this.pi.originalVideoUrl;
          }

          this.pi.stateManager.updateInstance(this.pi.instanceId, {
            status: "playing",
          });
          handlePlayTracking();
        } else {
          moduleLoggers.PlayerEventHandler.warn(
            `❌ [PlayerInstance] 鉴权失败，暂停播放: ${this.pi.instanceId}`,
          );

          // 暂停播放器
          if (this.pi.xgplayer) {
            this.pi.xgplayer.pause();
          }

          this.pi.stateManager.updateInstance(this.pi.instanceId, {
            status: "paused",
          });

          // 记录日志（后续会接入第三方付费弹窗）
          moduleLoggers.PlayerEventHandler.info(
            `📝 [PlayerInstance] 播放时鉴权失败日志 - 播放器: ${this.pi.instanceId}, 视频: ${videoData.id}, 集数: ${currentEpisode.id}`,
          );
        }
      } catch (error) {
        moduleLoggers.PlayerEventHandler.error(
          `💥 [PlayerInstance] 播放时鉴权过程中发生错误: ${this.pi.instanceId}`,
          error,
        );

        // 发生错误时暂停播放器
        if (this.pi.xgplayer) {
          this.pi.xgplayer.pause();
        }

        this.pi.stateManager.updateInstance(this.pi.instanceId, {
          status: "error",
        });
      } finally {
        this.pi.isAuthenticating = false;
      }
    });

    this.pi.xgplayer.on(Player.Events.PAUSE, () => {
      this.pi.stateManager.updateInstance(this.pi.instanceId, {
        status: "paused",
      });
      this.pi.eventManager.emit(
        "pause",
        getEventPayload({
          currentTime: this.pi.xgplayer?.currentTime ?? 0,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["pause"],
      );

      // 上报播放时长 - 暂停
      const state = this.pi.stateManager.getInstance(this.pi.instanceId);
      if (state?.videoData && state.currentEpisode && this.playTrackId) {
        this.pi.trackingManager.trackDuration(
          state.videoData,
          state.currentEpisode,
          {
            playId: this.playTrackId,
            playStartAt: this.playStartTimeStamp,
            status: "pause",
            vDuration: this.pi.xgplayer?.duration ?? 0,
            wDuration: this.pi.xgplayer?.currentTime ?? 0,
          },
        );
      }
    });

    this.pi.xgplayer.on(Player.Events.ERROR, (error) => {
      console.log("player event error", error);
      this.pi.stateManager.updateInstance(this.pi.instanceId, {
        status: "error",
      });
      this.pi.eventManager.emit(
        "error",
        getEventPayload({ error }) as PlayerEvents["error"],
      );
    });

    this.pi.xgplayer.on(Player.Events.ENDED, () => {
      moduleLoggers.PlayerEventHandler.info(
        `🏁 [PlayerInstance] 播放结束: ${this.pi.instanceId}`,
      );

      const state = this.pi.stateManager.getInstance(this.pi.instanceId);

      // 上报播放结束事件
      if (state?.videoData && state.currentEpisode) {
        this.pi.trackingManager.trackFinishPlay(
          state.videoData,
          state.currentEpisode,
        );

        // 上报播放时长 - 结束
        if (this.playTrackId) {
          this.pi.trackingManager.trackDuration(
            state.videoData,
            state.currentEpisode,
            {
              playId: this.playTrackId,
              playStartAt: this.playStartTimeStamp,
              status: "end",
              vDuration: this.pi.xgplayer?.duration ?? 0,
              wDuration: this.pi.xgplayer?.duration ?? 0, // 播放结束时，观看时长等于总时长
            },
          );
        }
      }

      this.pi.eventManager.emit(
        "ended",
        getEventPayload({
          duration: this.pi.xgplayer?.duration ?? 0,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["ended"],
      );

      this.pi.stateManager.updateInstance(this.pi.instanceId, {
        status: "paused",
      });

      const currentEpisodeNo = state?.currentEpisode?.no;

      if (currentEpisodeNo !== undefined && this.pi.config.onEnded) {
        moduleLoggers.PlayerEventHandler.info(
          `🔄 [PlayerInstance] 触发自动切换下一集: 当前第${currentEpisodeNo}集`,
        );
        this.pi.config.onEnded(this.pi.instanceId, currentEpisodeNo);
      } else {
        moduleLoggers.PlayerEventHandler.warn(
          `⚠️ [PlayerInstance] onEnded 回调未设置或 currentEpisodeNo 未定义: currentEpisodeNo=${currentEpisodeNo}, onEnded=${!!this.pi.config.onEnded}`,
        );
      }
    });

    // Fired when a seek operation is completed.
    this.pi.xgplayer.on(Player.Events.SEEKED, () => {
      // 延迟清除拖拽标志，确保 PLAY 事件处理完成后再清除
      setTimeout(() => {
        this.isSeeking = false;
        this.lastSeekEndTime = Date.now();
        moduleLoggers.PlayerEventHandler.info(
          `✅ [PlayerEventHandler] 拖拽结束，清除拖拽标志: ${this.pi.instanceId}`,
        );
      }, 100);

      this.pi.eventManager.emit(
        "seeked",
        getEventPayload({
          currentTime: this.pi.xgplayer?.currentTime ?? 0,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["seeked"],
      );
    });

    // Fired when a seek operation starts.
    this.pi.xgplayer.on(Player.Events.SEEKING, () => {
      // 设置拖拽标志
      this.isSeeking = true;
      moduleLoggers.PlayerEventHandler.info(
        `🔄 [PlayerEventHandler] 开始拖拽，设置拖拽标志: ${this.pi.instanceId}`,
      );

      this.pi.eventManager.emit(
        Player.Events.SEEKING,
        getEventPayload({
          currentTime: this.pi.xgplayer?.currentTime ?? 0,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["seeking"],
      );
    });

    // Fired when the current playback position has changed.
    this.pi.xgplayer.on(Player.Events.TIME_UPDATE, () => {
      const currentTime = this.pi.xgplayer?.currentTime ?? 0;
      const cumulatedWatchedTime = this.pi.xgplayer?.cumulateTime ?? 0;
      const playbackRate = this.pi.xgplayer?.playbackRate ?? 1;
      this.pi.stateManager.updateInstance(this.pi.instanceId, {
        cumulatedWatchedTime,
      });
      const state = this.pi.stateManager.getInstance(this.pi.instanceId);

      // 使用智能30秒播放追踪，支持倍速播放
      if (state?.videoData && state.currentEpisode) {
        // 使用新的智能30秒播放追踪，传入倍速信息
        this.pi.trackingManager.checkAndTriggerThirtySecondPlay(
          state.currentEpisode.id,
          cumulatedWatchedTime,
          currentTime,
          playbackRate,
          state.videoData,
          state.currentEpisode,
        );
      }

      // 每5秒上报一次播放进度
      if (this.pi.xgplayer && !this.pi.xgplayer.paused) {
        const now = Date.now();
        if (now - this.lastProgressTrackTime > 5000) {
          if (state?.videoData && state.currentEpisode && this.playTrackId) {
            this.pi.trackingManager.trackDuration(
              state.videoData,
              state.currentEpisode,
              {
                playId: this.playTrackId,
                playStartAt: this.playStartTimeStamp,
                status: "pause", // status 1: playing
                vDuration: this.pi.xgplayer?.duration ?? 0,
                wDuration: this.pi.xgplayer?.currentTime ?? 0,
              },
            );
            this.lastProgressTrackTime = now;
          }
        }
      }

      this.pi.eventManager.emit(
        "timeUpdate",
        getEventPayload({
          currentTime,
          duration: this.pi.xgplayer?.duration ?? 0,
          playTrackId: this.playTrackId,
          playStartTimeStamp: this.playStartTimeStamp,
        }) as PlayerEvents["timeUpdate"],
      );
    });

    // Fired when autoplay is started.
    this.pi.xgplayer.on(Player.Events.AUTOPLAY_STARTED, () => {
      moduleLoggers.PlayerEventHandler.info(
        `🎬 [PlayerInstance] 自动播放开始事件触发: ${this.pi.instanceId}`,
      );
    });

    // Fired when autoplay is prevented.
    this.pi.xgplayer.on(Player.Events.AUTOPLAY_PREVENTED, () => {
      moduleLoggers.PlayerEventHandler.info(
        `🔄 [PlayerInstance] 自动播放被阻止事件触发: ${this.pi.instanceId}`,
      );
      this.handleAutoplayPrevented();
    });
  }

  public destroy(): void {
    window.removeEventListener("pagehide", this.boundHandlePageHide);
    window.removeEventListener("beforeunload", this.boundHandleBeforeUnload);

    // 在销毁前上报最后一次播放进度
    if (this.pi.xgplayer) {
      const state = this.pi.stateManager.getInstance(this.pi.instanceId);
      if (state?.videoData && state.currentEpisode && this.playTrackId) {
        this.pi.trackingManager.trackDuration(
          state.videoData,
          state.currentEpisode,
          {
            playId: this.playTrackId,
            playStartAt: this.playStartTimeStamp,
            status: "end",
            vDuration: this.pi.xgplayer?.duration ?? 0,
            wDuration: this.pi.xgplayer?.currentTime ?? 0,
          },
        );
      }
    }
  }

  /**
   * 上报playDrama事件
   */
  private reportPlayDrama(): void {
    const state = this.pi.stateManager.getInstance(this.pi.instanceId);
    const currentEpisode = state?.currentEpisode;
    const videoData = state?.videoData;

    moduleLoggers.PlayerEventHandler.info(
      `🔍 [PlayerEventHandler] reportPlayDrama被调用: videoData=${!!videoData}, currentEpisode=${!!currentEpisode}: ${this.pi.instanceId}`,
    );

    if (!videoData || !currentEpisode) {
      moduleLoggers.PlayerEventHandler.warn(
        `⚠️ [PlayerEventHandler] 无法上报playDrama事件，缺少视频数据或集数信息: ${this.pi.instanceId}`,
      );
      return;
    }

    // 检查是否在拖拽结束后的短时间内（500ms内）
    const now = Date.now();
    const isRecentSeek = now - this.lastSeekEndTime < 500;

    moduleLoggers.PlayerEventHandler.info(
      `🔍 [PlayerEventHandler] reportPlayDrama检查: isSeeking=${this.isSeeking}, isRecentSeek=${isRecentSeek}, lastSeekEndTime=${this.lastSeekEndTime}, now=${now}, isFirstPlay=${this.isFirstPlay}: ${this.pi.instanceId}`,
    );

    // 在首次播放时，如果isSeeking为true但lastSeekEndTime为0，说明是播放器初始化过程中的自动seeking，应该忽略
    const isInitialSeeking = this.isSeeking && this.lastSeekEndTime === 0;

    // 放宽首次播放时的限制：如果是首次播放且不是最近拖拽结束，就允许上报
    const shouldReport = this.isFirstPlay
      ? !isRecentSeek // 首次播放时，只要不是最近拖拽结束就上报
      : (!this.isSeeking || isInitialSeeking) && !isRecentSeek; // 非首次播放时，使用原有逻辑

    if (shouldReport) {
      moduleLoggers.PlayerEventHandler.info(
        `🎬 [PlayerEventHandler] 上报 playDrama 事件: ${this.pi.instanceId} (首次播放: ${this.isFirstPlay})`,
      );
      this.pi.trackingManager.trackPlay(videoData, currentEpisode);
    } else {
      moduleLoggers.PlayerEventHandler.info(
        `⏸️ [PlayerEventHandler] 跳过 playDrama 事件上报 - 正在拖拽或刚拖拽结束: ${this.pi.instanceId}, isSeeking: ${this.isSeeking}, isRecentSeek: ${isRecentSeek}, isInitialSeeking: ${isInitialSeeking}, isFirstPlay: ${this.isFirstPlay}`,
      );
    }

    // 重置待上报标志
    this.pendingPlayDrama = false;
    moduleLoggers.PlayerEventHandler.info(
      `✅ [PlayerEventHandler] reportPlayDrama完成，重置pendingPlayDrama=false: ${this.pi.instanceId}`,
    );
  }

  /**
   * 处理自动播放被阻止事件
   */
  private handleAutoplayPrevented(): void {
    // 只在初次播放时处理自动播放重试
    if (!this.isFirstPlay) {
      moduleLoggers.PlayerEventHandler.info(
        `⚠️ [PlayerEventHandler] 非初次播放，跳过自动播放重试: ${this.pi.instanceId}`,
      );
      return;
    }
  }
}
