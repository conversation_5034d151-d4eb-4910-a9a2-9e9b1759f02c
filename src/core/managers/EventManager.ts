import { moduleLoggers } from "@/utils/LogManager";

/**
 * @file EventManager.ts
 * @description 事件管理器 (EM)
 * - 使用原生实现事件的发布/订阅模式。
 * - 用于播放器内核事件的解耦。
 * - 支持多实例场景，所有事件载荷都包含`episodeIndex`。
 */

export type PlayerEvents = {
  /** Fired when the player starts loading the video. */
  loadStart: { episodeIndex: number; videoId: string; episodeId: string };
  /** Fired when the player has loaded data. */
  loadedData: { episodeIndex: number; videoId: string; episodeId: string };
  /** Fired when playback begins. */
  play: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    currentTime: number;
  };
  /** Fired when playback is paused. */
  pause: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    currentTime: number;
  };
  /** Fired when playback has finished. */
  ended: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    duration: number;
  };
  /** Fired when an error occurs. */
  error: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    error: any;
  };
  /** Fired when a seek operation is completed. */
  seeked: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    currentTime: number;
  };
  /** Fired when the current playback position has changed. */
  timeUpdate: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    currentTime: number;
    duration: number;
  };
  /** Fired when a seek operation starts. */
  seeking: {
    episodeIndex: number;
    videoId: string;
    episodeId: string;
    currentTime: number;
  };

  /** Fired when the active episode changes. */
  episodeChange: { fromEpisodeIndex: number; toEpisodeIndex: number };

  /** Fired when global playback settings change. */
  globalPlaybackSettingsChange: {
    playbackRate?: number;
    volume?: number;
    muted?: boolean;
  };
};

type EventKey = keyof PlayerEvents;
type Handler<T> = (event: T) => void;
type Handlers = {
  [K in EventKey]?: Array<Handler<PlayerEvents[K]>>;
};

export class EventManager {
  private handlers: Handlers = {};

  private static instance: EventManager;

  constructor() {
    moduleLoggers.EventManager.info("EventManager initialized");
  }

  public static getInstance(): EventManager {
    if (!EventManager.instance) {
      EventManager.instance = new EventManager();
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_EVENT_MANAGER_CLASS__ = EventManager;
      }
    }
    return EventManager.instance;
  }

  public on<K extends EventKey>(type: K, handler: Handler<PlayerEvents[K]>) {
    if (!this.handlers[type]) {
      this.handlers[type] = [];
    }
    this.handlers[type]?.push(handler);
  }

  public off<K extends EventKey>(type: K, handler: Handler<PlayerEvents[K]>) {
    const handlersForType = this.handlers[type];
    if (handlersForType) {
      this.handlers[type] = handlersForType.filter(
        (h) => h !== handler,
      ) as Handlers[K];
    }
  }

  public emit<K extends EventKey>(type: K, event: PlayerEvents[K]) {
    const handlersForType = this.handlers[type];
    if (handlersForType) {
      handlersForType.forEach((handler) => handler(event));
    }
  }

  /**
   * 清理所有事件处理器
   */
  public clearAll(): void {
    moduleLoggers.EventManager.info("清理所有事件处理器");
    this.handlers = {};
  }

  /**
   * 重置事件管理器状态
   */
  public reset(): void {
    moduleLoggers.EventManager.info("重置 EventManager 状态");
    this.clearAll();
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.EventManager.info("开始重置全局 EventManager 实例");

    if (EventManager.instance) {
      EventManager.instance.clearAll();
      EventManager.instance = null as any;
    }

    moduleLoggers.EventManager.info("全局 EventManager 实例重置完成");
  }
}
