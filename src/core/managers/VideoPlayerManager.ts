/**
 * @file VideoPlayerManager.ts
 * @description 视频播放器管理器 (VPM)
 * - 核心业务逻辑，负责协调应用内其他管理器。
 * - 管理播放器实例池。
 * - 对接UI层和API层。
 */
import { moduleLoggers } from "@/utils/LogManager";

import { AuthManager } from "../../services/AuthManager";
import { PlayerFactory } from "../factories/PlayerFactory";
import { EventManager } from "./EventManager";
import { InstanceLifecycleManager } from "./InstanceLifecycleManager";
import { StateManager } from "./StateManager";
import { StatisticsManager } from "./StatisticsManager";

export class VideoPlayerManager {
  private static instance: VideoPlayerManager;

  public eventManager: EventManager;
  public stateManager: StateManager;
  public instanceLifecycleManager: InstanceLifecycleManager;
  public statisticsManager: StatisticsManager;
  public playerFactory: PlayerFactory;

  private constructor() {
    moduleLoggers.VideoPlayerManager.info("VideoPlayerManager constructor");

    // 1. 核心管理器初始化
    this.eventManager = EventManager.getInstance();
    this.stateManager = StateManager.getInstance();
    this.statisticsManager = StatisticsManager.getInstance();

    // 2. 工厂初始化 (使用单例模式)
    this.playerFactory = PlayerFactory.getInstance(
      this.eventManager,
      this.stateManager,
    );

    // 3. 实例生命周期管理器初始化
    this.instanceLifecycleManager = InstanceLifecycleManager.getInstance(
      this.playerFactory,
      this.stateManager,
      this.eventManager,
    );

    // 4. 初始化进度追踪
    this.statisticsManager.initializeProgressTracker(
      this.eventManager,
      this.stateManager,
    );
  }

  public static getInstance(): VideoPlayerManager {
    if (!VideoPlayerManager.instance) {
      if (!window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__) {
        moduleLoggers.VideoPlayerManager.info(
          "Creating new VideoPlayerManager instance for the first time in DEV mode",
        );
        window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ = new VideoPlayerManager();
        // 将类本身也挂载到 window 上，方便重置时使用
        window.__BS_VIDEO_PLAYER_MANAGER_CLASS__ = VideoPlayerManager;
      }
      VideoPlayerManager.instance =
        window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ as VideoPlayerManager;
    }
    return VideoPlayerManager.instance;
  }

  async initData(productId: string) {
    const data = await AuthManager.getInstance().getVideoDetail(productId);

    moduleLoggers.VideoPlayerManager.info("init video detail", data);

    this.stateManager.setVideoDetail(data);
    this.stateManager.setCurrentEpisodeIndex(0);
    return data;
  }

  public initPlayer(episodeIndex: number, id: string) {
    moduleLoggers.VideoPlayerManager.info("initPlayer", episodeIndex, id);

    const { videoDetail } = this.stateManager.getGlobalState();
    const videoUrl = videoDetail?.series[episodeIndex].url;

    if (videoUrl) {
      // 使用新的实例生命周期管理器
      const episode = videoDetail?.series[episodeIndex];
      this.instanceLifecycleManager.createPlayerInstance(
        id,
        id, // containerId 使用相同的 id
        videoDetail!,
        false, // 不自动播放
        0, // 从头开始
        episode,
      );
    }
  }

  public switchEpisode(episodeIndex: number) {
    const { videoDetail, currentEpisodeIndex } =
      this.stateManager.getGlobalState();
    const currentEpisode = videoDetail?.series[currentEpisodeIndex];
    const nextEpisode = videoDetail?.series[episodeIndex];

    if (currentEpisode && nextEpisode && videoDetail) {
      this.statisticsManager.reportPlaybackProgress(currentEpisode.id, true);

      // 使用新的实例生命周期管理器切换剧集
      this.instanceLifecycleManager.switchEpisode(episodeIndex + 1); // 转换为1-based索引
      this.stateManager.setCurrentEpisodeIndex(episodeIndex);
    }
  }

  /**
   * 获取实例统计信息
   */
  public getInstanceStats() {
    return this.instanceLifecycleManager.getInstanceStats();
  }

  /**
   * 手动清理过期实例
   */
  public cleanupExpiredInstances() {
    this.instanceLifecycleManager.cleanupExpiredInstances();
  }

  /**
   * 销毁所有实例
   */
  public destroyAllInstances() {
    this.instanceLifecycleManager.destroyAllInstances();
  }

  /**
   * 全局重置 - 清理所有资源并重新初始化
   * 用于重新挂载播放器时完全清理之前的状态
   */
  public reset(): void {
    moduleLoggers.VideoPlayerManager.info("开始重置 VideoPlayerManager 状态");

    // 1. 销毁所有播放器实例
    moduleLoggers.VideoPlayerManager.info("1. 开始销毁所有播放器实例");
    this.destroyAllInstances();

    // 2. 重置各个管理器的状态（不删除实例）
    moduleLoggers.VideoPlayerManager.info("2. 开始重置各个管理器的状态");
    try {
      if (window.__BS_PRELOAD_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.1 重置 PreloadManager");
        const preloadManager =
          window.__BS_PRELOAD_MANAGER_CLASS__.getInstance();
        if (preloadManager && typeof preloadManager.reset === "function") {
          preloadManager.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.1 PreloadManager 类不存在");
      }

      if (window.__BS_STATE_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.2 重置 StateManager");
        const stateManager = window.__BS_STATE_MANAGER_CLASS__.getInstance();
        if (stateManager && typeof stateManager.reset === "function") {
          // 在重新挂载时保留活跃实例状态，避免播放器黑屏
          stateManager.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.2 StateManager 类不存在");
      }

      if (window.__BS_EVENT_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.3 重置 EventManager");
        const eventManager = window.__BS_EVENT_MANAGER_CLASS__.getInstance();
        if (eventManager && typeof eventManager.reset === "function") {
          eventManager.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.3 EventManager 类不存在");
      }

      if (window.__BS_STATISTICS_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.4 重置 StatisticsManager");
        const statisticsManager =
          window.__BS_STATISTICS_MANAGER_CLASS__.getInstance();
        if (
          statisticsManager &&
          typeof statisticsManager.reset === "function"
        ) {
          statisticsManager.reset();
          // 重新初始化进度追踪器
          moduleLoggers.VideoPlayerManager.info(
            "2.4.1 重新初始化 StatisticsManager 进度追踪器",
          );
          statisticsManager.initializeProgressTracker(
            this.eventManager,
            this.stateManager,
          );
          moduleLoggers.VideoPlayerManager.info(
            "2.4.1 StatisticsManager 进度追踪器重新初始化成功",
          );
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.4 StatisticsManager 类不存在");
      }

      if (window.__BS_TRACKING_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.5 重置 TrackingManager");
        const trackingManager =
          window.__BS_TRACKING_MANAGER_CLASS__.getInstance();
        if (trackingManager && typeof trackingManager.reset === "function") {
          trackingManager.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.5 TrackingManager 类不存在");
      }

      if (window.__BS_PLAYER_FACTORY_CLASS__) {
        moduleLoggers.VideoPlayerManager.info("2.6 重置 PlayerFactory");
        const playerFactory = window.__BS_PLAYER_FACTORY_CLASS__.getInstance();
        if (playerFactory && typeof playerFactory.reset === "function") {
          playerFactory.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn("2.6 PlayerFactory 类不存在");
      }

      if (window.__BS_INSTANCE_LIFECYCLE_MANAGER_CLASS__) {
        moduleLoggers.VideoPlayerManager.info(
          "2.7 重置 InstanceLifecycleManager",
        );
        const instanceLifecycleManager =
          window.__BS_INSTANCE_LIFECYCLE_MANAGER_CLASS__.getInstance();
        if (
          instanceLifecycleManager &&
          typeof instanceLifecycleManager.reset === "function"
        ) {
          instanceLifecycleManager.reset();
        }
      } else {
        moduleLoggers.VideoPlayerManager.warn(
          "2.7 InstanceLifecycleManager 类不存在",
        );
      }
    } catch (error) {
      moduleLoggers.VideoPlayerManager.warn("重置管理器时出错", error);
    }

    // 3. 清理兼容层
    moduleLoggers.VideoPlayerManager.info("3. 清理兼容层");
    if (window.__BS_CLEANUP_COMPATIBILITY__) {
      window.__BS_CLEANUP_COMPATIBILITY__();
    }

    // 4. 清理全局变量
    moduleLoggers.VideoPlayerManager.info("4. 清理全局变量");
    if (window.__BS_IS_GESTURE_DISABLED__ !== undefined) {
      delete window.__BS_IS_GESTURE_DISABLED__;
    }

    moduleLoggers.VideoPlayerManager.info("VideoPlayerManager 状态重置完成");
  }

  /**
   * 静态方法：重置全局实例
   * 清理 window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__ 并重新创建
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.VideoPlayerManager.info(
      "开始重置全局 VideoPlayerManager 实例",
    );

    // 1. 清理当前实例
    if (VideoPlayerManager.instance) {
      moduleLoggers.VideoPlayerManager.info("1. 清理当前实例");
      VideoPlayerManager.instance.reset();
    }

    // 2. 清理 window 上的实例
    if (window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__) {
      moduleLoggers.VideoPlayerManager.info("2. 清理 window 上的实例");
      delete window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__;
    }

    // 3. 重置静态实例
    moduleLoggers.VideoPlayerManager.info("3. 重置静态实例");
    VideoPlayerManager.instance = null as any;

    // 4. 重新创建实例（确保下次 getInstance() 调用时能创建新实例）
    moduleLoggers.VideoPlayerManager.info("4. 重新创建实例");
    const newInstance = VideoPlayerManager.getInstance();

    moduleLoggers.VideoPlayerManager.info(
      "全局 VideoPlayerManager 实例重置完成",
      {
        newInstanceExists: !!newInstance,
        windowInstanceExists: !!window.__BS_VIDEO_PLAYER_MANAGER_INSTANCE__,
        windowClassExists: !!window.__BS_VIDEO_PLAYER_MANAGER_CLASS__,
      },
    );
  }
}
