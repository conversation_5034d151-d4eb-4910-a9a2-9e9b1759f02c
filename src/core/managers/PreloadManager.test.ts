import { vi } from "vitest";

// Mock document.getElementById
const mockContainer = {
  querySelectorAll: vi.fn(() => []),
};

// 设置全局 DOM 环境
global.document = {
  getElementById: vi.fn(() => mockContainer),
  body: {
    innerHTML: "",
  },
} as any;

global.window = {
  __BS_PRELOAD_MANAGER_CLASS__: null,
} as any;

vi.mock("xgplayer", () => ({
  default: vi.fn().mockImplementation(() => ({
    on: vi.fn(),
    load: vi.fn(),
    destroy: vi.fn(),
    currentTime: 0,
    duration: 0,
    readyState: 0,
    networkState: 0,
  })),
  Events: {
    LOAD_START: "loadstart",
    LOADED_DATA: "loadeddata",
    ERROR: "error",
    DESTROY: "destroy",
    LOADED_METADATA: "loadedmetadata",
    CANPLAY: "canplay",
    CANPLAY_THROUGH: "canplaythrough",
  },
}));

vi.mock("hls.js", () => {
  function HlsMock(this: any) {
    this.on = vi.fn();
    this.loadSource = vi.fn();
    this.startLoad = vi.fn();
    this.destroy = vi.fn();
    this.startLevel = 2;
    this.currentLevel = 2;
    this.nextLevel = 2;
    this.autoLevelEnabled = true;
  }
  (HlsMock as any).isSupported = vi.fn().mockReturnValue(true);
  (HlsMock as any).Events = {
    MANIFEST_LOADED: "manifestloaded",
    LEVEL_SWITCHING: "levelswitching",
    LEVEL_SWITCHED: "levelswitched",
    FRAG_LOADED: "fragloaded",
    ERROR: "error",
    MEDIA_ATTACHED: "mediaattached",
    MANIFEST_LOADING: "manifestloading",
  };
  return {
    default: HlsMock,
    Events: (HlsMock as any).Events,
    isSupported: (HlsMock as any).isSupported,
  };
});

import { afterEach, beforeEach, describe, expect, it } from "vitest";

import { PreloadManager } from "./PreloadManager";

describe("PreloadManager", () => {
  let preloadManager: PreloadManager;

  beforeEach(() => {
    // 重置全局实例
    PreloadManager.resetGlobalInstance();
    preloadManager = PreloadManager.getInstance();

    // 重置 mock
    vi.clearAllMocks();

    // 重置容器 mock
    mockContainer.querySelectorAll.mockReturnValue([]);
    (global.document.getElementById as any).mockReturnValue(mockContainer);
  });

  afterEach(() => {
    if (preloadManager && typeof preloadManager.destroy === "function") {
      preloadManager.destroy();
    }
  });

  describe("preloadEpisode", () => {
    it("应该使用 xgplayer 方式预加载（默认）", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 调用预加载方法
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证任务已创建
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(1);
      expect(status.tasks[0]).toEqual({
        videoId,
        episodeNo,
        timestamp: expect.any(Number),
      });
    });

    it("应该使用 HLS.js 方式预加载", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 调用预加载方法，指定使用 HLS.js
      preloadManager.preloadEpisode(url, videoId, episodeNo, false);

      // 验证任务已创建
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(1);
      expect(status.tasks[0]).toEqual({
        videoId,
        episodeNo,
        timestamp: expect.any(Number),
      });
    });

    it("应该跳过重复的预加载请求", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 第一次预加载
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 第二次预加载（应该被跳过）
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证只有一个任务
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(1);
    });

    it("应该终止其他集数的预加载请求，以新的为准", () => {
      const url1 = "https://example.com/video1.m3u8";
      const url2 = "https://example.com/video2.m3u8";
      const videoId = "test-video";
      const episodeNo1 = 1;
      const episodeNo2 = 2;

      // 第一次预加载第1集
      preloadManager.preloadEpisode(url1, videoId, episodeNo1);

      // 验证第一次预加载任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 第二次预加载第2集（应该终止第1集的预加载，创建第2集的预加载）
      preloadManager.preloadEpisode(url2, videoId, episodeNo2);

      // 验证仍然只有一个任务（第1集被终止，第2集被创建）
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(1);
      expect(status.tasks[0]).toEqual({
        videoId,
        episodeNo: episodeNo2,
        timestamp: expect.any(Number),
      });
    });

    it("同一集数的重复请求应该被跳过", () => {
      const url1 = "https://example.com/video1.m3u8";
      const url2 = "https://example.com/video2.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 第一次预加载第1集
      preloadManager.preloadEpisode(url1, videoId, episodeNo);

      // 验证第一次预加载任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 第二次预加载同一集数（应该被跳过）
      preloadManager.preloadEpisode(url2, videoId, episodeNo);

      // 验证仍然只有一个任务，且没有被终止
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(1);
      expect(status.tasks[0]).toEqual({
        videoId,
        episodeNo,
        timestamp: expect.any(Number),
      });
    });

    it("应该处理容器不存在的情况", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 模拟容器不存在
      (global.document.getElementById as any).mockReturnValue(null);

      // 调用预加载方法
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证没有创建任务
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(0);
    });

    it("应该处理容器已被使用的情况", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 模拟容器已被使用
      mockContainer.querySelectorAll.mockReturnValue([{ tagName: "video" }]);

      // 调用预加载方法
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证没有创建任务
      const status = preloadManager.getPreloadStatus();
      expect(status.total).toBe(0);
    });
  });

  describe("cancelPreload", () => {
    it("应该能够取消预加载任务", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 创建预加载任务
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 取消预加载
      preloadManager.cancelPreload(videoId, episodeNo);

      // 验证任务已被取消
      expect(preloadManager.getPreloadStatus().total).toBe(0);
    });

    it("应该能够取消不存在的预加载任务", () => {
      const videoId = "test-video";
      const episodeNo = 1;

      // 取消不存在的预加载任务
      preloadManager.cancelPreload(videoId, episodeNo);

      // 验证没有错误发生
      expect(preloadManager.getPreloadStatus().total).toBe(0);
    });
  });

  describe("getPreloadStatus", () => {
    it("应该返回正确的预加载状态", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 创建预加载任务
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      const status = preloadManager.getPreloadStatus();

      expect(status).toEqual({
        total: 1,
        active: 1,
        tasks: [
          {
            videoId,
            episodeNo,
            timestamp: expect.any(Number),
          },
        ],
      });
    });

    it("应该返回空状态当没有预加载任务时", () => {
      const status = preloadManager.getPreloadStatus();

      expect(status).toEqual({
        total: 0,
        active: 0,
        tasks: [],
      });
    });
  });

  describe("destroy", () => {
    it("应该清理所有预加载任务", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 创建预加载任务
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 销毁管理器
      preloadManager.destroy();

      // 验证所有任务已被清理
      expect(preloadManager.getPreloadStatus().total).toBe(0);
    });
  });

  describe("reset", () => {
    it("应该重置预加载管理器状态", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 创建预加载任务
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 重置管理器
      preloadManager.reset();

      // 验证所有任务已被清理
      expect(preloadManager.getPreloadStatus().total).toBe(0);
    });
  });

  describe("resetGlobalInstance", () => {
    it("应该重置全局实例", () => {
      const url = "https://example.com/video.m3u8";
      const videoId = "test-video";
      const episodeNo = 1;

      // 创建预加载任务
      preloadManager.preloadEpisode(url, videoId, episodeNo);

      // 验证任务已创建
      expect(preloadManager.getPreloadStatus().total).toBe(1);

      // 重置全局实例
      PreloadManager.resetGlobalInstance();

      // 获取新的实例
      const newPreloadManager = PreloadManager.getInstance();

      // 验证新实例没有任务
      expect(newPreloadManager.getPreloadStatus().total).toBe(0);
    });
  });

  // describe("HLS.js 预加载", () => {
  //   it("应该处理 HLS.js 不支持的情况", () => {
  //     const Hls = require("hls.js");
  //     Hls.default.isSupported.mockReturnValue(false);

  //     const url = "https://example.com/video.m3u8";
  //     const videoId = "test-video";
  //     const episodeNo = 1;

  //     // 调用 HLS.js 预加载
  //     preloadManager.preloadEpisode(url, videoId, episodeNo, false);

  //     // 验证没有创建任务
  //     const status = preloadManager.getPreloadStatus();
  //     expect(status.total).toBe(0);
  //   });

  //   it("应该处理 HLS.js 预加载错误", () => {
  //     const Hls = require("hls.js");

  //     // 模拟 HLS 构造函数抛出错误
  //     Hls.default.mockImplementation(() => {
  //       throw new Error("HLS initialization failed");
  //     });

  //     const url = "https://example.com/video.m3u8";
  //     const videoId = "test-video";
  //     const episodeNo = 1;

  //     // 调用 HLS.js 预加载
  //     preloadManager.preloadEpisode(url, videoId, episodeNo, false);

  //     // 验证没有创建任务
  //     const status = preloadManager.getPreloadStatus();
  //     expect(status.total).toBe(0);
  //   });
  // });

  // describe("xgplayer 预加载", () => {
  //   it("应该处理 xgplayer 预加载错误", () => {
  //     const Player = require("xgplayer").default;

  //     // 模拟 Player 构造函数抛出错误
  //     Player.default.mockImplementation(() => {
  //       throw new Error("Player initialization failed");
  //     });

  //     const url = "https://example.com/video.m3u8";
  //     const videoId = "test-video";
  //     const episodeNo = 1;

  //     // 调用 xgplayer 预加载
  //     preloadManager.preloadEpisode(url, videoId, episodeNo, true);

  //     // 验证没有创建任务
  //     const status = preloadManager.getPreloadStatus();
  //     expect(status.total).toBe(0);
  //   });
  // });
});
