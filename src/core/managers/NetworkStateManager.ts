/**
 * @file NetworkStateManager.ts
 * @description 网络状态管理器
 * - 管理网络连接状态
 * - 处理网络超时和重试限制
 * - 监听网络环境变化
 * - 提供网络状态提示和恢复机制
 * - 支持缓冲进度检查，延迟显示网络断开提示
 */

import { moduleLoggers } from "@/utils/LogManager";

export interface NetworkErrorInfo {
  type: "timeout" | "offline" | "server_error";
  retryCount: number;
  maxRetries: number;
  lastErrorTime: number;
  errorMessage: string;
}

export interface NetworkState {
  isOnline: boolean;
  isBlocked: boolean; // 是否被阻止（超过重试次数）
  currentError?: NetworkErrorInfo;
  lastNetworkChange: number;
}

export interface NetworkStateManagerConfig {
  maxRetries: number;
  retryDelay: number;
  timeoutThreshold: number;
  offlineCheckDelay: number; // 网络断开后延迟检查时间（毫秒）
  bufferThreshold: number; // 缓冲阈值（秒），低于此值时显示网络断开提示
}

export class NetworkStateManager {
  private static instance: NetworkStateManager;
  private state: NetworkState;
  private config: NetworkStateManagerConfig;
  private listeners: Set<(state: NetworkState) => void>;
  private retryCounters: Map<string, number> = new Map(); // 按实例ID记录重试次数
  private offlineCheckTimer: NodeJS.Timeout | null = null; // 网络断开检查定时器
  private bufferCheckTimer: NodeJS.Timeout | null = null; // 缓冲检查定时器
  private activePlayers: Set<string> = new Set(); // 活跃播放器实例ID
  private bufferCheckResponses: Map<string, any> = new Map(); // 缓冲检查响应
  private bufferCheckTimeout: NodeJS.Timeout | null = null; // 缓冲检查超时定时器

  private constructor() {
    this.config = {
      maxRetries: 3,
      retryDelay: 2000,
      timeoutThreshold: 10000,
      offlineCheckDelay: 1000, // 1 秒后开始检查缓冲
      bufferThreshold: 2, // 2 秒缓冲阈值
    };

    this.state = {
      isOnline: navigator.onLine,
      isBlocked: false,
      lastNetworkChange: Date.now(),
    };

    this.listeners = new Set();
    this.setupNetworkListeners();
    this.setupBufferStatusListener();
  }

  public static getInstance(): NetworkStateManager {
    if (!NetworkStateManager.instance) {
      NetworkStateManager.instance = new NetworkStateManager();
    }
    return NetworkStateManager.instance;
  }

  /**
   * 设置网络监听器
   */
  private setupNetworkListeners(): void {
    // 监听在线状态变化，仅用于 UI 提示
    window.addEventListener("online", this.handleOnline.bind(this));
    window.addEventListener("offline", this.handleOffline.bind(this));
    moduleLoggers.NetworkStateManager.info("网络状态监听器已设置");
  }

  /**
   * 设置缓冲状态监听器
   */
  private setupBufferStatusListener(): void {
    // 监听缓冲状态响应事件
    window.addEventListener(
      "bufferStatusResponse" as any,
      this.handleBufferStatusResponse.bind(this),
    );
    moduleLoggers.NetworkStateManager.info("缓冲状态监听器已设置");
  }

  /**
   * 处理网络恢复
   */
  private handleOnline(): void {
    moduleLoggers.NetworkStateManager.info("网络连接已恢复");
    this.state.isOnline = true;
    this.state.lastNetworkChange = Date.now();
    // 清除错误状态
    this.state.currentError = undefined;
    this.state.isBlocked = false;

    // 清除所有定时器
    this.clearOfflineCheckTimer();
    this.clearBufferCheckTimer();

    // 通知所有播放器实例重新开始HLS加载
    this.resumeAllHlsLoading();
    this.notifyListeners();
    this.showNetworkRecoveryMessage();
  }

  /**
   * 恢复所有HLS实例的加载
   */
  private resumeAllHlsLoading(): void {
    // 通过自定义事件通知所有播放器实例恢复HLS加载
    // 注意：CustomHlsJsPlugin 会检查实例是否为活跃实例，只有活跃实例才会恢复加载
    const event = new CustomEvent("resumeHlsLoading", {
      detail: {
        timestamp: Date.now(),
        source: "network_recovery",
      },
    });
    window.dispatchEvent(event);
    moduleLoggers.NetworkStateManager.info(
      "已发送恢复HLS加载的事件（只有活跃实例会响应）",
    );
  }

  /**
   * 触发活跃实例的HLS加载（用于剧集切换等场景）
   */
  public triggerActiveInstanceHlsLoading(): void {
    if (this.state.isOnline && !this.state.isBlocked) {
      const event = new CustomEvent("resumeHlsLoading", {
        detail: {
          timestamp: Date.now(),
          source: "episode_switch",
        },
      });
      window.dispatchEvent(event);
      moduleLoggers.NetworkStateManager.info(
        "已发送剧集切换后的HLS加载事件（只有活跃实例会响应）",
      );
    }
  }

  /**
   * 处理网络断开
   */
  private handleOffline(): void {
    moduleLoggers.NetworkStateManager.warn("网络连接已断开");
    this.state.isOnline = false;
    this.state.lastNetworkChange = Date.now();

    // 立即停止所有HLS加载
    this.stopAllHlsLoading();

    // 清除之前的定时器
    this.clearOfflineCheckTimer();
    this.clearBufferCheckTimer();

    // 设置延迟检查，而不是立即显示弹窗
    this.scheduleOfflineCheck();

    // 设置错误状态但不立即显示UI
    this.state.currentError = {
      type: "offline",
      retryCount: 0,
      maxRetries: this.config.maxRetries,
      lastErrorTime: Date.now(),
      errorMessage: "Network Connection Lost",
    };

    this.notifyListeners();
  }

  /**
   * 安排网络断开检查
   */
  private scheduleOfflineCheck(): void {
    moduleLoggers.NetworkStateManager.info(
      `安排网络断开检查，延迟: ${this.config.offlineCheckDelay}ms`,
    );

    this.offlineCheckTimer = setTimeout(() => {
      this.checkBufferAndShowOfflineMessage();
    }, this.config.offlineCheckDelay);
  }

  /**
   * 检查缓冲并显示离线消息
   */
  private checkBufferAndShowOfflineMessage(): void {
    if (this.state.isOnline) {
      // 网络已恢复，不需要显示离线消息
      return;
    }

    moduleLoggers.NetworkStateManager.info("开始检查当前活跃播放器缓冲状态");

    // 清除之前的响应
    this.bufferCheckResponses.clear();

    // 只检查当前活跃播放器的缓冲状态
    this.checkActivePlayerBuffer();

    // 设置超时等待响应
    this.scheduleBufferCheckTimeout();
  }

  /**
   * 检查所有播放器的缓冲状态
   */
  private checkAllPlayersBuffer(): boolean {
    moduleLoggers.NetworkStateManager.info("发送缓冲状态检查事件");

    // 通过自定义事件获取所有活跃播放器的缓冲状态
    const event = new CustomEvent("checkBufferStatus", {
      detail: {
        timestamp: Date.now(),
        bufferThreshold: this.config.bufferThreshold,
      },
    });
    window.dispatchEvent(event);

    // 返回false，让processBufferCheckResults处理实际结果
    return false;
  }

  /**
   * 检查当前活跃播放器的缓冲状态
   */
  private checkActivePlayerBuffer(): boolean {
    moduleLoggers.NetworkStateManager.info("发送活跃播放器缓冲状态检查事件");

    // 通过自定义事件获取当前活跃播放器的缓冲状态
    const event = new CustomEvent("checkActivePlayerBufferStatus", {
      detail: {
        timestamp: Date.now(),
        bufferThreshold: this.config.bufferThreshold,
      },
    });
    window.dispatchEvent(event);

    // 返回false，让handleBufferStatusResponse处理实际结果
    return false;
  }

  /**
   * 安排缓冲检查
   */
  private scheduleBufferCheck(): void {
    this.clearBufferCheckTimer();

    this.bufferCheckTimer = setTimeout(() => {
      this.checkBufferAndShowOfflineMessage();
    }, 500); // 每 0.5 秒检查一次缓冲
  }

  /**
   * 清除网络断开检查定时器
   */
  private clearOfflineCheckTimer(): void {
    if (this.offlineCheckTimer) {
      clearTimeout(this.offlineCheckTimer);
      this.offlineCheckTimer = null;
    }
  }

  /**
   * 清除缓冲检查定时器
   */
  private clearBufferCheckTimer(): void {
    if (this.bufferCheckTimer) {
      clearTimeout(this.bufferCheckTimer);
      this.bufferCheckTimer = null;
    }
  }

  /**
   * 停止所有HLS实例的加载
   */
  private stopAllHlsLoading(): void {
    // 通过自定义事件通知所有播放器实例停止HLS加载
    const event = new CustomEvent("stopHlsLoading", {
      detail: {
        timestamp: Date.now(),
      },
    });
    window.dispatchEvent(event);
    moduleLoggers.NetworkStateManager.info("已发送停止所有HLS加载的事件");
  }

  /**
   * 处理网络问题（仅供 HLS 错误调用）
   */
  private handleNetworkIssue(
    type: "timeout" | "server_error",
    message: string,
  ): void {
    moduleLoggers.NetworkStateManager.warn(`检测到网络问题: ${message}`);
    this.state.currentError = {
      type,
      retryCount: 0,
      maxRetries: this.config.maxRetries,
      lastErrorTime: Date.now(),
      errorMessage: message,
    };
    this.notifyListeners();
  }

  /**
   * 记录网络错误（仅供 HLS 错误调用）
   */
  public recordNetworkError(
    instanceId: string,
    errorType: "timeout" | "offline" | "server_error",
    errorMessage: string,
  ): boolean {
    const currentRetries = this.retryCounters.get(instanceId) || 0;
    const newRetryCount = currentRetries + 1;
    moduleLoggers.NetworkStateManager.warn(
      `记录网络错误: ${instanceId}, 类型: ${errorType}, 重试次数: ${newRetryCount}/${this.config.maxRetries}`,
    );
    this.retryCounters.set(instanceId, newRetryCount);

    // 设置错误状态
    this.state.currentError = {
      type: errorType,
      retryCount: newRetryCount,
      maxRetries: this.config.maxRetries,
      lastErrorTime: Date.now(),
      errorMessage,
    };

    // 检查是否超过最大重试次数
    // if (newRetryCount >= this.config.maxRetries) {
    //   this.state.isBlocked = true;
    //   moduleLoggers.NetworkStateManager.warn(
    //     `网络错误超过最大重试次数，阻止继续请求: ${instanceId}`,
    //   );
    //   this.showMaxRetriesMessage();
    // }

    // 通知监听器，这会触发UI显示错误提示
    this.notifyListeners();

    // 如果是 manifest 加载错误，立即显示错误提示
    if (errorMessage.includes("manifestLoadError")) {
      this.showNetworkMessage(
        "Connection Timeout",
        errorMessage,
        "error",
        false,
        instanceId,
      );
    }

    return !this.state.isBlocked;
  }

  /**
   * 重置实例的重试计数
   */
  public resetRetryCount(instanceId: string): void {
    this.retryCounters.delete(instanceId);
    moduleLoggers.NetworkStateManager.info(`重置重试计数: ${instanceId}`);
  }

  /**
   * 清除错误状态（用于剧集切换等场景）
   */
  public clearErrorState(): void {
    this.state.currentError = undefined;
    this.state.isBlocked = false;
    this.notifyListeners();
    moduleLoggers.NetworkStateManager.info("已清除错误状态");
  }

  /**
   * 清除错误状态但不立即通知监听器（用于内部状态重置）
   */
  private clearErrorStateSilent(): void {
    this.state.currentError = undefined;
    this.state.isBlocked = false;
    moduleLoggers.NetworkStateManager.info("已静默清除错误状态");
  }

  /**
   * 手动恢复网络状态
   */
  public manualRecovery(): void {
    moduleLoggers.NetworkStateManager.info("手动恢复网络状态");
    this.state.isBlocked = false;
    this.state.currentError = undefined;
    this.retryCounters.clear();

    // 清除所有定时器
    this.clearOfflineCheckTimer();
    this.clearBufferCheckTimer();
    this.clearBufferCheckTimeout();

    // 清除缓冲检查响应
    this.bufferCheckResponses.clear();

    this.notifyListeners();
    this.showManualRecoveryMessage();
  }

  /**
   * 检查是否可以继续请求
   */
  public canContinueRequest(instanceId: string): boolean {
    if (!this.state.isOnline) {
      return false;
    }
    if (this.state.isBlocked) {
      return false;
    }
    const retryCount = this.retryCounters.get(instanceId) || 0;
    return retryCount < this.config.maxRetries;
  }

  /**
   * 获取当前网络状态
   */
  public getNetworkState(): NetworkState {
    return { ...this.state };
  }

  /**
   * 订阅网络状态变化
   */
  public subscribe(listener: (state: NetworkState) => void): () => void {
    this.listeners.add(listener);
    // 立即通知当前状态
    listener(this.getNetworkState());
    return () => {
      this.listeners.delete(listener);
    };
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    const state = this.getNetworkState();
    this.listeners.forEach((listener) => {
      try {
        listener(state);
      } catch (error) {
        moduleLoggers.NetworkStateManager.error("通知监听器时出错:", error);
      }
    });
  }

  /**
   * 显示离线消息
   */
  private showOfflineMessage(): void {
    this.showNetworkMessage(
      "Network Connection Lost",
      "Please check your network settings and try again",
      "warning",
    );
  }

  /**
   * 显示网络恢复消息
   */
  private showNetworkRecoveryMessage(): void {
    // this.showNetworkMessage("Network Connection Restored", "Reconnecting...", "success");
  }

  /**
   * 显示最大重试次数消息
   */
  private showMaxRetriesMessage(): void {
    this.showNetworkMessage(
      "Network Connection Error",
      "Maximum retry attempts exceeded. Please click retry button to manually recover",
      "error",
      true,
    );
  }

  /**
   * 显示手动恢复消息
   */
  private showManualRecoveryMessage(): void {
    // this.showNetworkMessage("Network Connection Restored", "Playback can be resumed", "success");
  }

  /**
   * 显示网络消息
   */
  private showNetworkMessage(
    title: string,
    message: string,
    type: "success" | "warning" | "error",
    showRetryButton = false,
    instanceId?: string,
  ): void {
    // 暂时使用 console 输出
    const logMethod =
      type === "error" ? "error" : type === "warning" ? "warn" : "info";
    moduleLoggers.NetworkStateManager[logMethod](`[${title}] ${message}`);
    // 触发自定义事件，供UI层监听
    const event = new CustomEvent("networkStateChange", {
      detail: {
        title,
        message,
        type,
        showRetryButton,
        timestamp: Date.now(),
        instanceId,
      },
    });
    window.dispatchEvent(event);
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<NetworkStateManagerConfig>): void {
    this.config = { ...this.config, ...config };
    moduleLoggers.NetworkStateManager.info(
      "网络状态管理器配置已更新",
      this.config,
    );
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    window.removeEventListener("online", this.handleOnline.bind(this));
    window.removeEventListener("offline", this.handleOffline.bind(this));
    window.removeEventListener(
      "bufferStatusResponse" as any,
      this.handleBufferStatusResponse.bind(this),
    );
    this.listeners.clear();
    this.retryCounters.clear();
    this.clearOfflineCheckTimer();
    this.clearBufferCheckTimer();
    this.clearBufferCheckTimeout();
    this.bufferCheckResponses.clear();
    moduleLoggers.NetworkStateManager.info("网络状态管理器已销毁");
  }

  /**
   * 处理缓冲状态响应
   */
  private handleBufferStatusResponse(event: CustomEvent): void {
    const { instanceId, hasEnoughBuffer, bufferAhead, hasBuffer } =
      event.detail;

    moduleLoggers.NetworkStateManager.info(
      `收到缓冲状态响应: ${instanceId}, 缓冲充足=${hasEnoughBuffer}, 缓冲剩余=${bufferAhead}s, 当前时间点有缓冲=${hasBuffer}`,
    );

    this.bufferCheckResponses.set(instanceId, {
      hasEnoughBuffer,
      bufferAhead,
      hasBuffer,
      timestamp: Date.now(),
    });

    // 不在这里立即判断，等待所有响应收集完毕后再处理
  }

  /**
   * 安排缓冲检查超时
   */
  private scheduleBufferCheckTimeout(): void {
    this.clearBufferCheckTimeout();

    this.bufferCheckTimeout = setTimeout(() => {
      this.processBufferCheckResults();
    }, 500); // 等待 0.5 秒收集所有响应
  }

  /**
   * 清除缓冲检查超时定时器
   */
  private clearBufferCheckTimeout(): void {
    if (this.bufferCheckTimeout) {
      clearTimeout(this.bufferCheckTimeout);
      this.bufferCheckTimeout = null;
    }
  }

  /**
   * 处理缓冲检查结果
   */
  private processBufferCheckResults(): void {
    if (this.state.isOnline) {
      // 网络已恢复，不需要显示离线消息
      moduleLoggers.NetworkStateManager.info("网络已恢复，取消缓冲检查");
      return;
    }

    // 检查是否有任何播放器缓冲充足
    let hasAnyEnoughBuffer = false;
    let totalBufferAhead = 0;
    let responseCount = 0;
    let activePlayerCount = 0;
    let activeInstanceBufferAhead = 0;

    this.bufferCheckResponses.forEach((response, instanceId) => {
      responseCount++;
      if (response.hasEnoughBuffer) {
        hasAnyEnoughBuffer = true;
        activePlayerCount++;
      }
      totalBufferAhead += response.bufferAhead;

      // 记录活跃实例的缓冲情况
      activeInstanceBufferAhead = response.bufferAhead;
    });

    moduleLoggers.NetworkStateManager.info(
      `缓冲检查结果: 响应数量=${responseCount}, 活跃播放器数量=${activePlayerCount}, 有充足缓冲=${hasAnyEnoughBuffer}, 总缓冲剩余=${totalBufferAhead}s, 活跃实例缓冲剩余=${activeInstanceBufferAhead}s`,
    );

    if (hasAnyEnoughBuffer && activePlayerCount > 0) {
      // 至少有一个播放器缓冲充足，继续检查
      moduleLoggers.NetworkStateManager.info("缓冲充足，继续监控");
      this.scheduleBufferCheck();
    } else {
      // 所有播放器缓冲都不足，显示离线消息
      moduleLoggers.NetworkStateManager.warn(
        `所有播放器缓冲不足，显示网络断开提示。响应数量=${responseCount}, 活跃播放器=${activePlayerCount}, 活跃实例缓冲剩余=${activeInstanceBufferAhead}s`,
      );
      this.showOfflineMessage();
    }
  }

  /**
   * 处理切换剧集时的播放失败
   * 这个方法用于处理切换剧集时如果播放失败的情况
   */
  public handleEpisodeSwitchFailure(
    instanceId: string,
    errorMessage: string,
  ): void {
    moduleLoggers.NetworkStateManager.warn(
      `剧集切换失败: ${instanceId}, 错误: ${errorMessage}`,
    );

    // 检查网络状态
    if (!this.state.isOnline) {
      // 网络断开，使用延迟提示机制
      this.scheduleOfflineCheck();
    } else {
      // 网络正常但播放失败，可能是其他原因，延迟显示错误
      this.scheduleEpisodeSwitchFailureCheck(instanceId, errorMessage);
    }
  }

  /**
   * 安排剧集切换失败检查
   */
  private scheduleEpisodeSwitchFailureCheck(
    instanceId: string,
    errorMessage: string,
  ): void {
    moduleLoggers.NetworkStateManager.info(
      `安排剧集切换失败检查，延迟: ${this.config.offlineCheckDelay}ms`,
    );

    // 清除之前的定时器
    this.clearOfflineCheckTimer();

    this.offlineCheckTimer = setTimeout(() => {
      this.checkBufferAndShowEpisodeSwitchFailure(instanceId, errorMessage);
    }, this.config.offlineCheckDelay);
  }

  /**
   * 检查缓冲并显示剧集切换失败消息
   */
  private checkBufferAndShowEpisodeSwitchFailure(
    instanceId: string,
    errorMessage: string,
  ): void {
    if (this.state.isOnline) {
      // 网络已恢复，不需要显示错误消息
      return;
    }

    moduleLoggers.NetworkStateManager.info("开始检查剧集切换失败后的缓冲状态");

    // 清除之前的响应
    this.bufferCheckResponses.clear();

    // 只检查当前活跃播放器的缓冲状态
    this.checkActivePlayerBuffer();

    // 设置超时等待响应
    this.scheduleEpisodeSwitchFailureTimeout(instanceId, errorMessage);
  }

  /**
   * 安排剧集切换失败检查超时
   */
  private scheduleEpisodeSwitchFailureTimeout(
    instanceId: string,
    errorMessage: string,
  ): void {
    this.clearBufferCheckTimeout();

    this.bufferCheckTimeout = setTimeout(() => {
      this.processEpisodeSwitchFailureResults(instanceId, errorMessage);
    }, 500); // 等待 0.5 秒收集所有响应
  }

  /**
   * 处理剧集切换失败检查结果
   */
  private processEpisodeSwitchFailureResults(
    instanceId: string,
    errorMessage: string,
  ): void {
    if (this.state.isOnline) {
      // 网络已恢复，不需要显示错误消息
      moduleLoggers.NetworkStateManager.info(
        "网络已恢复，取消剧集切换失败检查",
      );
      return;
    }

    // 检查是否有任何播放器缓冲充足
    let hasAnyEnoughBuffer = false;
    let totalBufferAhead = 0;
    let responseCount = 0;
    let activePlayerCount = 0;

    this.bufferCheckResponses.forEach((response) => {
      responseCount++;
      if (response.hasEnoughBuffer) {
        hasAnyEnoughBuffer = true;
        activePlayerCount++;
      }
      totalBufferAhead += response.bufferAhead;
    });

    moduleLoggers.NetworkStateManager.info(
      `剧集切换失败检查结果: 响应数量=${responseCount}, 活跃播放器数量=${activePlayerCount}, 有充足缓冲=${hasAnyEnoughBuffer}, 总缓冲剩余=${totalBufferAhead}s`,
    );

    if (hasAnyEnoughBuffer && activePlayerCount > 0) {
      // 至少有一个播放器缓冲充足，继续检查
      moduleLoggers.NetworkStateManager.info("缓冲充足，继续监控");
      this.scheduleBufferCheck();
    } else {
      // 所有播放器缓冲都不足，显示剧集切换失败消息
      moduleLoggers.NetworkStateManager.warn(
        `所有播放器缓冲不足，显示剧集切换失败提示。响应数量=${responseCount}, 活跃播放器=${activePlayerCount}`,
      );
      this.showEpisodeSwitchFailureMessage(errorMessage);
    }
  }

  /**
   * 显示剧集切换失败消息
   */
  private showEpisodeSwitchFailureMessage(errorMessage: string): void {
    this.showNetworkMessage(
      "Episode Switch Failed",
      `Failed to load episode: ${errorMessage}. Please check your network connection and try again.`,
      "error",
      true,
    );
  }

  /**
   * 调试方法：强制显示网络断开提示（用于测试）
   */
  public debugForceShowOfflineMessage(): void {
    moduleLoggers.NetworkStateManager.info("强制显示网络断开提示（测试用）");
    this.showOfflineMessage();
  }

  /**
   * 调试方法：强制显示剧集切换失败提示（用于测试）
   */
  public debugForceShowEpisodeSwitchFailureMessage(errorMessage: string): void {
    moduleLoggers.NetworkStateManager.info(
      "强制显示剧集切换失败提示（测试用）",
    );
    this.showEpisodeSwitchFailureMessage(errorMessage);
  }

  /**
   * 调试方法：手动触发缓冲检查
   */
  public debugTriggerBufferCheck(): void {
    moduleLoggers.NetworkStateManager.info("手动触发缓冲检查");
    this.checkBufferAndShowOfflineMessage();
  }

  /**
   * 调试方法：手动触发活跃播放器缓冲检查
   */
  public debugTriggerActivePlayerBufferCheck(): void {
    moduleLoggers.NetworkStateManager.info("手动触发活跃播放器缓冲检查");
    this.checkBufferAndShowOfflineMessage();
  }

  /**
   * 调试方法：获取当前缓冲检查状态
   */
  public debugGetBufferCheckStatus(): any {
    return {
      isOnline: this.state.isOnline,
      isBlocked: this.state.isBlocked,
      currentError: this.state.currentError,
      bufferCheckResponses: Array.from(this.bufferCheckResponses.entries()),
      offlineCheckTimer: !!this.offlineCheckTimer,
      bufferCheckTimer: !!this.bufferCheckTimer,
      bufferCheckTimeout: !!this.bufferCheckTimeout,
    };
  }
}
