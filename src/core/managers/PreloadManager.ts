/**
 * @file PreloadManager.ts
 * @description HLS切片预加载管理器
 * - 统一管理多播放器实例的HLS切片预加载
 * - 支持两种预加载方式：HLS.js 和 xgplayer.load()
 * - 轻量级预加载，只预加载第一个切片
 * - 避免创建完整播放器实例的开销
 */

import Hls from "hls.js";
import Player from "xgplayer";

import { moduleLoggers } from "@/utils/LogManager";

interface PreloadTask {
  url: string;
  videoId: string;
  episodeNo: number;
  hlsInstance?: any;
  xgplayer?: Player;
  timestamp: number;
  type: "hls" | "xgplayer";
}

export class PreloadManager {
  private static instance: PreloadManager;
  private preloadTasks: Map<string, PreloadTask> = new Map();
  private maxConcurrentPreloads = Number.MAX_SAFE_INTEGER; // 最大并发预加载数量
  private preloadTimeout = 30000; // 预加载超时时间（毫秒）- 增加到30秒
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor() {
    this.startCleanupTimer();
  }

  public static getInstance(): PreloadManager {
    if (!PreloadManager.instance) {
      PreloadManager.instance = new PreloadManager();
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_PRELOAD_MANAGER_CLASS__ = PreloadManager;
      }
    }
    return PreloadManager.instance;
  }

  /**
   * 预加载指定集数的视频资源
   * @param url 视频URL
   * @param videoId 视频ID
   * @param episodeNo 集数
   * @param useXgPlayer 是否使用 xgplayer 方式预加载，默认为 true
   */
  public preloadEpisode(
    url: string,
    videoId: string,
    episodeNo: number,
    useXgPlayer: boolean = true,
  ): void {
    const taskKey = `${videoId}-${episodeNo}`;

    moduleLoggers.PreloadManager.info(`预加载请求: ${taskKey}, URL: ${url}`);

    // 检查是否已经在预加载同一集数
    const existingTask = this.preloadTasks.get(taskKey);
    if (existingTask) {
      // 如果正在预加载同一集数，跳过重复请求
      moduleLoggers.PreloadManager.info(
        `跳过重复预加载请求: 第${episodeNo}集 (已在预加载中)`,
      );
      return;
    }

    // 检查是否有其他集数正在预加载，如果有则终止
    const otherTasks = Array.from(this.preloadTasks.entries()).filter(
      ([key, task]) => task.videoId === videoId && task.episodeNo !== episodeNo,
    );

    if (otherTasks.length > 0) {
      moduleLoggers.PreloadManager.info(
        `检测到其他集数正在预加载，终止旧的预加载任务: ${otherTasks.length}个`,
      );
      otherTasks.forEach(([taskKey, task]) => {
        this.cancelPreload(task.videoId, task.episodeNo);
      });
    }

    // 检查并发数量限制
    if (this.preloadTasks.size >= this.maxConcurrentPreloads) {
      moduleLoggers.PreloadManager.warn(
        `达到最大并发预加载数量限制，跳过: 第${episodeNo}集`,
      );
      return;
    }

    // 根据参数选择预加载方式
    if (useXgPlayer) {
      this.preloadEpisodeWithXgPlayer(url, videoId, episodeNo);
    } else {
      this.preloadEpisodeWithHls(url, videoId, episodeNo);
    }
  }

  /**
   * 使用 HLS.js 预加载指定集数的HLS切片
   */
  private preloadEpisodeWithHls(
    url: string,
    videoId: string,
    episodeNo: number,
  ): void {
    const taskKey = `${videoId}-${episodeNo}`;

    try {
      // 检查HLS.js支持
      if (!Hls.isSupported()) {
        moduleLoggers.PreloadManager.warn(
          `HLS.js不支持，跳过切片预加载: 第${episodeNo}集`,
        );
        return;
      }

      moduleLoggers.PreloadManager.info(
        `创建HLS实例开始预加载: 第${episodeNo}集`,
      );

      // 创建HLS实例
      const hlsInstance = new Hls({
        enableWorker: true,
        autoStartLoad: false, // 不自动开始加载，手动控制
        startLevel: 2, // 使用与主播放器相同的分辨率档位
        maxBufferLength: 5, // 限制缓冲区大小，只预加载少量数据
        maxMaxBufferLength: 10,
        fragLoadingMaxRetry: 3,
        manifestLoadingMaxRetry: 3,
        levelLoadingMaxRetry: 3,
        // 参考CustomHlsJsPlugin的配置，锁定分辨率档位
        capLevelToPlayerSize: false, // 禁用基于播放器大小的level调整
        capLevelOnFPSDrop: false, // 禁用基于FPS下降的level调整
        // 禁用自适应比特率算法
        abrEwmaFastLive: 1,
        abrEwmaSlowLive: 1,
        abrEwmaFastVoD: 1,
        abrEwmaSlowVoD: 1,
        maxLoadingDelay: 4,
      });

      // 创建预加载任务
      const task: PreloadTask = {
        url,
        videoId,
        episodeNo,
        hlsInstance,
        timestamp: Date.now(),
        type: "hls",
      };

      this.preloadTasks.set(taskKey, task);

      moduleLoggers.PreloadManager.info(
        `预加载任务已创建: ${taskKey}, 当前任务数: ${this.preloadTasks.size}`,
      );

      // 设置事件监听
      this.setupHlsEventListeners(hlsInstance, taskKey, episodeNo);

      // 开始加载manifest
      moduleLoggers.PreloadManager.info(
        `开始加载manifest: 第${episodeNo}集, URL: ${url}`,
      );
      hlsInstance.loadSource(url);

      moduleLoggers.PreloadManager.info(`开始预加载第${episodeNo}集HLS切片`);
    } catch (error) {
      moduleLoggers.PreloadManager.error(
        `预加载HLS切片失败: 第${episodeNo}集`,
        error,
      );
    }
  }

  /**
   * 设置HLS事件监听器
   */
  private setupHlsEventListeners(
    hlsInstance: any,
    taskKey: string,
    episodeNo: number,
  ): void {
    // 监听manifest加载完成
    hlsInstance.on(Hls.Events.MANIFEST_LOADED, (event: any, data: any) => {
      moduleLoggers.PreloadManager.info(
        `预加载manifest加载完成，开始加载第一个切片: 第${episodeNo}集`,
      );

      // 确保startLevel在有效范围内，参考CustomHlsJsPlugin的逻辑
      const maxLevel = data.levels.length - 1;
      let targetLevel = 2; // 与主播放器保持一致
      if (targetLevel > maxLevel) {
        targetLevel = maxLevel;
      } else if (targetLevel < 0) {
        targetLevel = 0;
      }

      moduleLoggers.PreloadManager.info(
        `预加载锁定分辨率档位: ${targetLevel}/${maxLevel}`,
      );

      // 安全地设置并锁定level
      try {
        hlsInstance.startLevel = targetLevel;
        hlsInstance.currentLevel = targetLevel;
        hlsInstance.nextLevel = targetLevel;

        // 尝试禁用自适应比特率
        const descriptor =
          Object.getOwnPropertyDescriptor(hlsInstance, "autoLevelEnabled") ||
          Object.getOwnPropertyDescriptor(
            Object.getPrototypeOf(hlsInstance),
            "autoLevelEnabled",
          );
        if (descriptor && descriptor.set) {
          try {
            (hlsInstance as any).autoLevelEnabled = false;
            moduleLoggers.PreloadManager.info(`预加载已禁用自适应比特率`);
          } catch {
            // 只读属性，跳过
          }
        }
      } catch (error) {
        moduleLoggers.PreloadManager.warn(`预加载设置level属性时出错:`, error);
      }

      // 开始加载第一个切片
      moduleLoggers.PreloadManager.info(`开始加载第一个切片: 第${episodeNo}集`);
      hlsInstance.startLoad();
    });

    // 监听level切换事件，防止意外切换（参考CustomHlsJsPlugin）
    hlsInstance.on(Hls.Events.LEVEL_SWITCHING, (event: any, data: any) => {
      const targetLevel = 2; // 与主播放器保持一致
      if (data.level !== targetLevel && targetLevel >= 0) {
        moduleLoggers.PreloadManager.info(
          `预加载检测到意外切换，强制切回档位: ${targetLevel}`,
        );
        hlsInstance.nextLevel = targetLevel;
      }
    });

    // 监听level切换完成事件
    hlsInstance.on(Hls.Events.LEVEL_SWITCHED, (event: any, data: any) => {
      moduleLoggers.PreloadManager.info(
        `预加载分辨率档位切换完成: ${data.level}`,
      );
    });

    // 监听第一个切片加载完成
    hlsInstance.on(Hls.Events.FRAG_LOADED, (event: any, data: any) => {
      moduleLoggers.PreloadManager.info(
        `预加载第一个切片完成: 第${episodeNo}集, 时长: ${data.frag.duration}s`,
      );

      // 预加载完成后清理
      this.completePreload(taskKey);
    });

    // 监听错误
    hlsInstance.on(Hls.Events.ERROR, (event: any, data: any) => {
      moduleLoggers.PreloadManager.warn(
        `预加载HLS切片出错: 第${episodeNo}集, 类型: ${data.type}, 详情: ${data.details}`,
      );
      this.completePreload(taskKey);
    });

    // 监听其他重要事件
    hlsInstance.on(Hls.Events.MEDIA_ATTACHED, (event: any, data: any) => {
      moduleLoggers.PreloadManager.info(`预加载媒体已附加: 第${episodeNo}集`);
    });

    hlsInstance.on(Hls.Events.MANIFEST_LOADING, (event: any, data: any) => {
      moduleLoggers.PreloadManager.info(
        `预加载manifest加载中: 第${episodeNo}集`,
      );
    });
  }

  /**
   * 完成预加载任务
   */
  private completePreload(taskKey: string): void {
    const task = this.preloadTasks.get(taskKey);
    if (!task) return;

    moduleLoggers.PreloadManager.info(
      `预加载任务完成: ${taskKey} (第${task.episodeNo}集)`,
    );

    // 清理资源
    if (task.type === "hls" && task.hlsInstance) {
      task.hlsInstance.destroy();
    } else if (task.type === "xgplayer" && task.xgplayer) {
      task.xgplayer.destroy();
    }

    // 从任务列表中移除
    this.preloadTasks.delete(taskKey);

    moduleLoggers.PreloadManager.info(
      `预加载任务已清理: ${taskKey}, 剩余任务数: ${this.preloadTasks.size}`,
    );
  }

  /**
   * 取消预加载任务
   */
  public cancelPreload(videoId: string, episodeNo: number): void {
    const taskKey = `${videoId}-${episodeNo}`;
    const task = this.preloadTasks.get(taskKey);

    if (task) {
      if (task.type === "hls" && task.hlsInstance) {
        task.hlsInstance.destroy();
      } else if (task.type === "xgplayer" && task.xgplayer) {
        task.xgplayer.destroy();
      }
      this.preloadTasks.delete(taskKey);

      moduleLoggers.PreloadManager.info(`取消预加载任务: 第${episodeNo}集`);
    }
  }

  /**
   * 清理超时的预加载任务
   */
  private cleanupTimeoutTasks(): void {
    const now = Date.now();
    const timeoutTasks: string[] = [];

    this.preloadTasks.forEach((task, taskKey) => {
      if (now - task.timestamp > this.preloadTimeout) {
        timeoutTasks.push(taskKey);
      }
    });

    timeoutTasks.forEach((taskKey) => {
      moduleLoggers.PreloadManager.warn(`预加载任务超时，强制清理: ${taskKey}`);
      this.completePreload(taskKey);
    });
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanupTimeoutTasks();
    }, 5000); // 每5秒检查一次
  }

  /**
   * 停止清理定时器
   */
  private stopCleanupTimer(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
  }

  /**
   * 获取预加载状态
   */
  public getPreloadStatus(): {
    total: number;
    active: number;
    tasks: Array<{ videoId: string; episodeNo: number; timestamp: number }>;
  } {
    const tasks = Array.from(this.preloadTasks.values()).map((task) => ({
      videoId: task.videoId,
      episodeNo: task.episodeNo,
      timestamp: task.timestamp,
    }));

    return {
      total: this.preloadTasks.size,
      active: this.preloadTasks.size,
      tasks,
    };
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.stopCleanupTimer();

    // 清理所有预加载任务
    this.preloadTasks.forEach((task, taskKey) => {
      if (task.type === "hls" && task.hlsInstance) {
        task.hlsInstance.destroy();
      } else if (task.type === "xgplayer" && task.xgplayer) {
        task.xgplayer.destroy();
      }
    });
    this.preloadTasks.clear();

    moduleLoggers.PreloadManager.info("预加载管理器已销毁");
  }

  /**
   * 重置预加载管理器状态
   */
  public reset(): void {
    moduleLoggers.PreloadManager.info("重置 PreloadManager 状态");
    this.destroy();
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.PreloadManager.info("开始重置全局 PreloadManager 实例");

    if (PreloadManager.instance) {
      PreloadManager.instance.destroy();
      PreloadManager.instance = null as any;
    }

    moduleLoggers.PreloadManager.info("全局 PreloadManager 实例重置完成");
  }

  /**
   * 预加载指定集数的xgplayer
   */
  public preloadEpisodeWithXgPlayer(
    url: string,
    videoId: string,
    episodeNo: number,
  ): void {
    const taskKey = `${videoId}-${episodeNo}`;

    try {
      moduleLoggers.PreloadManager.info(`开始预加载第${episodeNo}集xgplayer`);

      // 使用已存在的播放器容器
      const containerId = `container-episode-${videoId}-${episodeNo}`;
      const container = document.getElementById(containerId);

      if (!container) {
        moduleLoggers.PreloadManager.warn(
          `预加载容器不存在: ${containerId}，跳过预加载`,
        );
        return;
      }

      // 检查容器是否已被其他播放器使用
      const existingPlayers = container.querySelectorAll("video, .xgplayer");
      if (existingPlayers.length > 0) {
        moduleLoggers.PreloadManager.warn(
          `预加载容器已被使用: ${containerId}，存在 ${existingPlayers.length} 个播放器元素，跳过预加载`,
        );
        return;
      }

      moduleLoggers.PreloadManager.info(
        `使用容器进行预加载: ${containerId}, URL: ${url}`,
      );

      // 创建xgplayer实例，使用已存在的DOM元素
      const xgplayer = new Player({
        el: container,
        url,
        autoplay: false,
        preload: true,
        volume: 0, // 静音预加载
        muted: true, // 确保静音
      });

      // 创建预加载任务
      const task: PreloadTask = {
        url,
        videoId,
        episodeNo,
        xgplayer,
        timestamp: Date.now(),
        type: "xgplayer",
      };

      this.preloadTasks.set(taskKey, task);

      moduleLoggers.PreloadManager.info(
        `预加载任务已创建: ${taskKey}, 当前任务数: ${this.preloadTasks.size}`,
      );

      // 设置事件监听
      this.setupXgPlayerEventListeners(xgplayer, taskKey, episodeNo);

      // 开始加载
      xgplayer.load();

      moduleLoggers.PreloadManager.info(`开始预加载第${episodeNo}集xgplayer`);
    } catch (error) {
      moduleLoggers.PreloadManager.error(
        `预加载xgplayer失败: 第${episodeNo}集`,
        error,
      );
    }
  }

  /**
   * 设置xgplayer事件监听器
   */
  private setupXgPlayerEventListeners(
    xgplayer: Player,
    taskKey: string,
    episodeNo: number,
  ): void {
    // 监听加载开始
    xgplayer.on(Player.Events.LOAD_START, () => {
      moduleLoggers.PreloadManager.info(`预加载开始加载: 第${episodeNo}集`);
    });

    // 监听数据加载完成
    xgplayer.on(Player.Events.LOADED_DATA, () => {
      moduleLoggers.PreloadManager.info(`预加载数据加载完成: 第${episodeNo}集`);

      // 延迟清理，给一些时间让资源缓存
      setTimeout(() => {
        this.completePreload(taskKey);
      }, 1000);
    });

    // 监听加载错误
    xgplayer.on(Player.Events.ERROR, (error: any) => {
      moduleLoggers.PreloadManager.warn(`预加载出错: 第${episodeNo}集`, {
        error,
        errorType: error?.type,
        errorCode: error?.code,
        errorMessage: error?.message,
        playerState: {
          currentTime: xgplayer?.currentTime,
          duration: xgplayer?.duration,
          readyState: xgplayer?.readyState,
          networkState: xgplayer?.networkState,
        },
      });
      this.completePreload(taskKey);
    });

    // 监听播放器销毁
    xgplayer.on(Player.Events.DESTROY, () => {
      moduleLoggers.PreloadManager.info(`预加载播放器已销毁: 第${episodeNo}集`);
    });

    // 监听其他重要事件
    xgplayer.on(Player.Events.LOADED_METADATA, () => {
      moduleLoggers.PreloadManager.info(
        `预加载元数据加载完成: 第${episodeNo}集`,
      );
    });

    xgplayer.on(Player.Events.CANPLAY, () => {
      moduleLoggers.PreloadManager.info(`预加载可以播放: 第${episodeNo}集`);
    });

    xgplayer.on(Player.Events.CANPLAY_THROUGH, () => {
      moduleLoggers.PreloadManager.info(`预加载可以流畅播放: 第${episodeNo}集`);
    });
  }
}
