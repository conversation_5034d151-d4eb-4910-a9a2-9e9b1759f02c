import { State } from "hls.js";

import { TrackingManager } from "@/services/TrackingManager";
import { moduleLoggers } from "@/utils/LogManager";
import { retryOperation } from "@/utils/retry";

import { EventManager, PlayerEvents } from "./EventManager";
import { StateManager } from "./StateManager";

// 添加统计数据接口
export interface Statistics {
  like: number;
  collect: number;
}

// 添加统计数据缓存类型
type StatisticsCacheKey = string; // 格式: "videoId"

const PROGRESS_STORAGE_KEY = "csp-video-progress";
class ProgressTracker {
  private eventManager: EventManager;
  private stateManager: StateManager;
  private trackingManager: TrackingManager;

  // Configuration
  private minPlayTimeThreshold: number = 3000; // 最低有效播放时间间隔(毫秒)
  private interval: number = 5000; // 定时上报间隔(毫秒)
  STORAGE_KEY = PROGRESS_STORAGE_KEY;

  // State
  private lastReportedProgress: Record<
    string,
    { currentTime: number; timestamp: number }
  > = {};
  private skipped = false;
  private previousVideoId: string | null = null;
  private previousEpisodeId: string | null = null;

  // 添加 API 调用防抖机制
  private lastApiReportTime: Record<string, number> = {};
  private readonly API_REPORT_INTERVAL = 1000; // 1秒内防抖处理

  constructor(eventManager: EventManager, stateManager: StateManager) {
    this.eventManager = eventManager;
    this.stateManager = stateManager;
    this.trackingManager = TrackingManager.getInstance();
    this.listenToEvents();
  }

  private listenToEvents(): void {
    this.eventManager.on("loadedData", (e) => {
      this.handleTimeUpdate(e);
    });
    this.eventManager.on("play", (e) => {
      this.handlePlay(e);
    });
    this.eventManager.on("pause", (e) => {
      this.reportProgress(e, true, "pause");
    });
    this.eventManager.on("seeked", (e) => {
      this.reportProgress(e, true, "pause");
    });
    this.eventManager.on("ended", (e) => {
      this.handleEnded(e);
    });
    this.eventManager.on("timeUpdate", (e) => {
      this.handleTimeUpdate(e);
    });
  }

  private handlePlay(event: PlayerEvents["play"]): void {
    const { videoId, episodeId } = event;

    const state = this.stateManager.getInstance(videoId);
    if (state?.videoData && state.currentEpisode) {
      this.trackingManager.trackPlay(state.videoData, state.currentEpisode);
    }

    this.reportProgress(event, true, "start");
  }

  private handleEnded(event: PlayerEvents["ended"]): void {
    const { videoId } = event;
    const state = this.stateManager.getInstance(videoId);
    if (state?.videoData && state.currentEpisode) {
      this.trackingManager.trackFinishPlay(
        state.videoData,
        state.currentEpisode,
      );
    }
    this.reportProgress(
      {
        ...event,
        currentTime: event.duration, // Use duration on ended
      },
      true,
      "end",
    );
  }

  private handleTimeUpdate(
    event: PlayerEvents["timeUpdate"] | PlayerEvents["loadedData"],
  ): void {
    if ("currentTime" in event) {
      const { videoId, episodeId, currentTime } = event;
      const progressKey = `${videoId}-${episodeId}`;

      // 使用智能30秒播放追踪，基于 cumulateTime
      const state = this.stateManager.getInstance(videoId);
      if (state?.videoData && state.currentEpisode) {
        // 获取播放器实例的 cumulateTime 和播放倍速
        const player = this.stateManager.getPlayer(videoId);
        const cumulateTime = player?.xgplayer?.cumulateTime ?? 0;
        const currentTime = player?.xgplayer?.currentTime ?? 0;
        const playbackRate = player?.xgplayer?.playbackRate ?? 1;

        // 使用新的智能30秒播放追踪，支持倍速播放
        this.trackingManager.checkAndTriggerThirtySecondPlay(
          episodeId,
          cumulateTime,
          currentTime,
          playbackRate,
          state.videoData,
          state.currentEpisode,
        );
      }

      const lastProgress = this.lastReportedProgress[progressKey];

      const timeIntervalReached =
        !lastProgress || Date.now() - lastProgress.timestamp >= this.interval;

      if (timeIntervalReached) {
        this.reportProgress(event, false, "pause");
      }
    } else {
      // loadedData event
      if (
        this.previousVideoId !== event.videoId ||
        this.previousEpisodeId !== event.episodeId
      ) {
        this.previousVideoId = event.videoId;
        this.previousEpisodeId = event.episodeId;
        this.initProgress(event.videoId, event.episodeId);
      }
    }
  }

  private initProgress(videoId: string, episodeId: string): void {
    const localProgress = this.getStorageProgress(videoId, episodeId);
    if (localProgress) {
      const progressKey = `${videoId}-${episodeId}`;
      this.lastReportedProgress[progressKey] = localProgress;
    } else {
      // 若上一次进度不存在, 则立即上报进度
      // this.reportProgress({ videoId, episodeId, currentTime: 0 });
    }
  }

  public reportProgressNow(episodeId: string, force: boolean = false): void {
    const state = this.stateManager.getGlobalState();
    const videoId = state.videoDetail?.id ?? "";
    const activeInstanceId = this.stateManager.getActiveInstance()?.id;

    if (!activeInstanceId || !episodeId) {
      return;
    }

    const instance = this.stateManager.getPlayer(activeInstanceId);
    if (!instance) {
      return;
    }
    const currentTime = instance.xgplayer?.currentTime ?? 0;
    moduleLoggers.StatisticsManager.info("reportProgressNow - 2", {
      videoId,
      episodeId,
      currentTime,
    });
    this.reportProgress(
      {
        videoId,
        episodeId,
        currentTime,
      },
      force,
    );
  }

  private async reportProgress(
    event: {
      videoId: string;
      episodeId: string;
      currentTime: number;
      playTrackId?: string;
      playStartTimeStamp?: number;
    },
    force = false,
    status?: "start" | "pause" | "end",
  ) {
    if (this.skipped) {
      return;
    }

    const { videoId, episodeId, currentTime, playTrackId, playStartTimeStamp } =
      event;

    if (!videoId || !episodeId) {
      return;
    }

    // Track duration event
    if (status && playTrackId && playStartTimeStamp) {
      const state = this.stateManager.getInstance(videoId);
      if (state?.videoData && state.currentEpisode) {
        this.trackingManager.trackDuration(
          state.videoData,
          state.currentEpisode,
          {
            playId: playTrackId,
            playStartAt: playStartTimeStamp,
            status,
            vDuration:
              this.stateManager.getPlayer(videoId)?.xgplayer?.duration ?? 0,
            wDuration: currentTime,
          },
        );
      }
    }

    const progress = {
      currentTime,
      videoId,
      episodeId,
      timestamp: Date.now(),
    };

    const progressKey = `${videoId}-${episodeId}`;
    const lastProgress = this.lastReportedProgress[progressKey];

    const timePositionChanged =
      !lastProgress ||
      Math.abs(currentTime - lastProgress.currentTime) * 1000 >=
        this.minPlayTimeThreshold;

    const firstReport = !lastProgress;
    const shouldReport = force || firstReport || timePositionChanged;

    moduleLoggers.StatisticsManager.info("reportProgress", {
      shouldReport,
      force,
      firstReport,
      timePositionChanged,
      progress,
    });

    if (shouldReport) {
      // 检查API上报时间间隔，确保至少间隔1秒
      const now = Date.now();
      const lastReportTime = this.lastApiReportTime[progressKey] || 0;
      const lastProgress = this.lastReportedProgress[progressKey];

      // 检查当前时间的整数部分是否与上次上报相同
      const currentTimeFloor = Math.floor(currentTime);
      const lastReportedTimeFloor = lastProgress
        ? Math.floor(lastProgress.currentTime)
        : -1;
      const sameTimePosition = currentTimeFloor === lastReportedTimeFloor;

      // 时间间隔检查：距离上次上报已经超过1秒
      const timeIntervalPassed =
        now - lastReportTime >= this.API_REPORT_INTERVAL;

      // 上报条件：(强制上报或时间间隔已过) 且 时间位置发生变化
      const shouldActuallyReport =
        (force || timeIntervalPassed) && !sameTimePosition;

      if (shouldActuallyReport) {
        moduleLoggers.StatisticsManager.info(
          force
            ? "[ProgressTracker] Force reporting progress:"
            : "Reporting progress:",
          progress,
        );

        this.saveLocalProgress(progress);
        this.lastReportedProgress[progressKey] = progress;

        // 更新最后上报时间
        this.lastApiReportTime[progressKey] = now;

        await this.reportProgressToServer(progress);
      } else {
        const skipReason = sameTimePosition
          ? `same time position (${currentTimeFloor}s)`
          : `time interval not passed (${now - lastReportTime}ms ago)`;
        moduleLoggers.StatisticsManager.info(
          `[ProgressTracker] API call skipped: ${skipReason}`,
        );
      }
    }
  }

  private saveLocalProgress(progress: {
    videoId: string;
    episodeId: string;
    currentTime: number;
    timestamp: number;
  }) {
    const videoId = progress.videoId;
    const episodeId = progress.episodeId || "default";
    const allProgress = this.getAllStorageProgress();

    allProgress[videoId] = {
      currentEpisodeId: episodeId,
      timestamp: progress.timestamp,
      currentTime: progress.currentTime,
    };

    this.saveAllStorageProgress(allProgress);
  }

  private getStorageProgress(videoId: string, episodeId: string) {
    const allProgress = this.getAllStorageProgress();
    if (!allProgress[videoId]) {
      return null;
    }

    if (episodeId && allProgress[videoId].currentEpisodeId !== episodeId) {
      return null;
    }

    return {
      currentTime: allProgress[videoId].currentTime,
      timestamp: allProgress[videoId].timestamp,
      currentEpisodeId: allProgress[videoId].currentEpisodeId,
    };
  }

  public getAllStorageProgress() {
    try {
      const storage = window.localStorage;
      const allProgress = storage.getItem(this.STORAGE_KEY);
      return allProgress ? JSON.parse(allProgress) : {};
    } catch (e) {
      moduleLoggers.StatisticsManager.error(
        "[StatisticsManager] Error reading from localStorage",
        e,
      );
      return {};
    }
  }

  private saveAllStorageProgress(
    allProgress: Record<
      string,
      {
        currentEpisodeId: string;
        timestamp: number;
        currentTime: number;
      }
    >,
  ) {
    try {
      const storage = window.localStorage;
      storage.setItem(this.STORAGE_KEY, JSON.stringify(allProgress));
    } catch (e) {
      moduleLoggers.StatisticsManager.error(
        "[ProgressTracker] Error writing to localStorage",
        e,
      );
    }
  }

  private async reportProgressToServer(progress: {
    videoId: string;
    episodeId: string;
    currentTime: number;
    timestamp: number;
  }): Promise<void> {
    // 时间间隔检查已经在 reportProgress 方法中处理，这里直接执行上报

    await fetch(`/apps/best-short/api/v1/preview/process`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        event_type: 5,
        msg: {
          skit_id: progress.videoId,
          skit_series_id: progress.episodeId,
          process: Math.floor(progress.currentTime),
          issue_at: Math.floor(progress.timestamp / 1000),
        },
      }),
    })
      .then((res) => res.json())
      .then((data) => {
        moduleLoggers.StatisticsManager.info(
          "[ProgressTracker] Server response:",
          data,
        );
      })
      .catch((err) => {
        moduleLoggers.StatisticsManager.error(
          "[ProgressTracker] Failed to report progress:",
          err,
        );
      });
  }
}

export class StatisticsManager {
  private static instance: StatisticsManager;
  private statisticsCache = new Map<StatisticsCacheKey, Statistics>();
  private progressTracker: ProgressTracker | null = null;

  // eslint-disable-next-line @typescript-eslint/no-empty-function
  private constructor() {}

  public static getInstance(): StatisticsManager {
    if (!StatisticsManager.instance) {
      StatisticsManager.instance = new StatisticsManager();
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_STATISTICS_MANAGER_CLASS__ = StatisticsManager;
      }
    }
    return StatisticsManager.instance;
  }

  public initializeProgressTracker(
    eventManager: EventManager,
    stateManager: StateManager,
  ): void {
    if (!this.progressTracker) {
      moduleLoggers.StatisticsManager.info("初始化进度追踪器");
      this.progressTracker = new ProgressTracker(eventManager, stateManager);
    }
  }

  public reportPlaybackProgress(
    episodeId: string,
    force: boolean = false,
  ): void {
    if (this.progressTracker) {
      this.progressTracker.reportProgressNow(episodeId, force);
    } else {
      moduleLoggers.StatisticsManager.warn(
        "ProgressTracker 未初始化，无法上报进度",
      );
    }
  }

  private getStatisticsCacheKey(videoId: string): StatisticsCacheKey {
    return videoId;
  }

  public getStatisticsFromCache(videoId: string): Statistics | null {
    const cacheKey = this.getStatisticsCacheKey(videoId);
    return this.statisticsCache.get(cacheKey) || null;
  }

  public updateStatisticsCache(videoId: string, stats: Statistics): void {
    const cacheKey = this.getStatisticsCacheKey(videoId);
    this.statisticsCache.set(cacheKey, stats);
  }

  public async refreshStatistics(
    videoId: string,
    episodeId: string,
  ): Promise<Statistics> {
    try {
      const response = await fetch(
        `/apps/best-short/api/v1/preview/statistics?skit_id=${videoId}&skit_series_id=${episodeId}`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch statistics");
      }
      const data = (await response.json()) as {
        data: {
          like: string;
          collect: string;
        };
      };
      const newStats: Statistics = {
        like: Number(data.data.like) || 0,
        collect: Number(data.data.collect) || 0,
      };
      this.updateStatisticsCache(videoId, newStats);
      return newStats;
    } catch (error) {
      moduleLoggers.StatisticsManager.error(
        "Failed to fetch statistics:",
        error,
      );
      throw error;
    }
  }

  public async updateFavorite(
    videoId: string,
    episodeId: string,
    isLike: boolean,
  ): Promise<void> {
    if (isLike) {
      const videoData = StateManager.getInstance().getGlobalState().videoDetail;
      const episode = videoData?.series.find((ep) => ep.id === episodeId);
      TrackingManager.getInstance().trackLike(
        {
          title: videoData?.title ?? "",
          id: videoId,
        },
        {
          no: episode?.no ?? 0,
          id: episodeId,
        },
      );
    }
    try {
      await retryOperation(() =>
        fetch(`/apps/best-short/api/v1/preview/like`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            event_type: isLike ? 1 : 2,
            msg: {
              skit_id: videoId,
              skit_series_id: episodeId,
            },
          }),
        }),
      );
    } catch (error) {
      moduleLoggers.StatisticsManager.error(
        "Failed to update favorite status:",
        error,
      );
      throw error;
    }
  }

  public async updateCollect(
    videoId: string,
    episodeId: string,
    isCollect: boolean,
  ): Promise<void> {
    if (isCollect) {
      const videoData = StateManager.getInstance().getGlobalState().videoDetail;
      TrackingManager.getInstance().trackCollect({
        title: videoData?.title ?? "",
        id: videoId,
      });
    }
    try {
      await retryOperation(() =>
        fetch(`/apps/best-short/api/v1/preview/collection`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            event_type: isCollect ? 3 : 4,
            msg: {
              skit_id: videoId,
              skit_series_id: episodeId,
            },
          }),
        }),
      );
    } catch (error) {
      moduleLoggers.StatisticsManager.error(
        "Failed to update collect status:",
        error,
      );
      throw error;
    }
  }

  public getLastPlayedFromStorage(
    videoId: string,
  ): { episodeId: string; currentTime: number } | null {
    try {
      const storage = window.localStorage;
      const allProgress = JSON.parse(
        storage.getItem(PROGRESS_STORAGE_KEY) || "{}",
      );
      if (allProgress[videoId]) {
        return {
          episodeId: allProgress[videoId].currentEpisodeId,
          currentTime: allProgress[videoId].currentTime,
        };
      }
    } catch (e) {
      moduleLoggers.StatisticsManager.error(
        "[StatisticsManager] Error reading progress from localStorage",
        e,
      );
    }
    return null;
  }

  /**
   * 重置统计管理器到初始状态
   */
  public reset(): void {
    moduleLoggers.StatisticsManager.info("重置 StatisticsManager 到初始状态");

    // 清理统计数据缓存
    this.statisticsCache.clear();

    // 清理进度追踪器
    if (this.progressTracker) {
      // 清理本地存储的进度数据
      try {
        window.localStorage.removeItem(PROGRESS_STORAGE_KEY);
      } catch (e) {
        moduleLoggers.StatisticsManager.error(
          "[StatisticsManager] Error clearing progress from localStorage",
          e,
        );
      }
      this.progressTracker = null;
    }

    moduleLoggers.StatisticsManager.info("StatisticsManager 重置完成");
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.StatisticsManager.info("开始重置全局 StatisticsManager 实例");

    if (StatisticsManager.instance) {
      StatisticsManager.instance.reset();
      StatisticsManager.instance = null as any;
    }

    moduleLoggers.StatisticsManager.info("全局 StatisticsManager 实例重置完成");
  }
}
