import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import { StatisticsManager } from "./StatisticsManager";
import { EventManager } from "./EventManager";
import { StateManager } from "./StateManager";

// Mock fetch
global.fetch = vi.fn();

describe("StatisticsManager ProgressTracker", () => {
  let statisticsManager: StatisticsManager;
  let eventManager: EventManager;
  let stateManager: StateManager;

  beforeEach(() => {
    // Reset global instances
    EventManager.resetGlobalInstance();
    StatisticsManager.resetGlobalInstance();
    
    // Get fresh instances
    eventManager = EventManager.getInstance();
    stateManager = StateManager.getInstance();
    statisticsManager = StatisticsManager.getInstance();
    
    // Initialize progress tracker
    statisticsManager.initializeProgressTracker(eventManager, stateManager);
    
    // Mock fetch response
    vi.mocked(fetch).mockResolvedValue({
      json: () => Promise.resolve({ success: true }),
    } as Response);
    
    // Mock timers
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.clearAllMocks();
  });

  it("should debounce multiple rapid progress reports", async () => {
    // Simulate multiple rapid timeUpdate events (not seeked events)
    const mockEvent = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 30,
      duration: 100,
    };

    // First, emit a timeUpdate event to set the last reported progress
    eventManager.emit("timeUpdate", mockEvent);
    
    // Wait for the first event to be processed
    await Promise.resolve();
    
    // Clear the fetch mock to reset the count
    vi.clearAllMocks();

    // Now emit multiple timeUpdate events rapidly with different times
    // that exceed the minPlayTimeThreshold (3 seconds)
    eventManager.emit("timeUpdate", { ...mockEvent, currentTime: 35 }); // 5 seconds difference
    eventManager.emit("timeUpdate", { ...mockEvent, currentTime: 40 }); // 10 seconds difference
    eventManager.emit("timeUpdate", { ...mockEvent, currentTime: 45 }); // 15 seconds difference

    // Fast-forward time to trigger debounced call
    vi.advanceTimersByTime(300);

    // Wait for async operations
    await Promise.resolve();

    // Should only call fetch once due to debouncing
    expect(fetch).toHaveBeenCalledTimes(1);
  });

  it("should force report immediately for seeked events", async () => {
    const mockEvent = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 30,
    };

    // Emit seeked event
    eventManager.emit("seeked", mockEvent);

    // Should call fetch immediately without waiting
    expect(fetch).toHaveBeenCalledTimes(1);
  });

  it("should clear debounce timers on reset", () => {
    const mockEvent = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 30,
    };

    // Emit an event to create a debounce timer
    eventManager.emit("timeUpdate", {
      ...mockEvent,
      duration: 100,
    });

    // Reset the manager
    statisticsManager.reset();

    // Fast-forward time - should not trigger any fetch calls
    vi.advanceTimersByTime(300);

    expect(fetch).not.toHaveBeenCalled();
  });

  it("should throttle API calls for same content within 1 second", async () => {
    const mockEvent = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 30,
    };

    // Emit multiple seeked events with same content rapidly
    eventManager.emit("seeked", mockEvent);
    eventManager.emit("seeked", mockEvent);
    eventManager.emit("seeked", mockEvent);

    // Wait for async operations
    await Promise.resolve();

    // Should only call fetch once due to API throttling
    expect(fetch).toHaveBeenCalledTimes(1);
  });

  it("should allow API calls for different content even within 1 second", async () => {
    const mockEvent1 = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 30,
    };

    const mockEvent2 = {
      episodeIndex: 1,
      videoId: "test-video",
      episodeId: "test-episode",
      currentTime: 35, // Different time
    };

    // Emit seeked events with different content
    eventManager.emit("seeked", mockEvent1);
    eventManager.emit("seeked", mockEvent2);

    // Wait for async operations
    await Promise.resolve();

    // Should call fetch twice due to different content
    expect(fetch).toHaveBeenCalledTimes(2);
  });
}); 