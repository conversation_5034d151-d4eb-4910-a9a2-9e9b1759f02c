/**
 * @file NetworkStateManager.test.ts
 * @description NetworkStateManager 测试文件
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import { NetworkStateManager } from "./NetworkStateManager";

// Mock dependencies
vi.mock("@/utils/LogManager");

describe("NetworkStateManager", () => {
  let networkStateManager: NetworkStateManager;

  beforeEach(() => {
    // 重置单例实例
    (NetworkStateManager as any).instance = undefined;
    networkStateManager = NetworkStateManager.getInstance();
  });

  afterEach(() => {
    // 清理
    networkStateManager.destroy();
  });

  describe("clearErrorState", () => {
    it("应该清除错误状态", () => {
      // 先记录一个网络错误
      networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Connection Timeout(Network Error: manifestLoadError)",
      );

      // 验证错误状态已设置
      const stateBeforeClear = networkStateManager.getNetworkState();
      expect(stateBeforeClear.currentError).toBeDefined();
      expect(stateBeforeClear.currentError?.type).toBe("timeout");
      expect(stateBeforeClear.currentError?.errorMessage).toBe(
        "Connection Timeout(Network Error: manifestLoadError)",
      );

      // 清除错误状态
      networkStateManager.clearErrorState();

      // 验证错误状态已清除
      const stateAfterClear = networkStateManager.getNetworkState();
      expect(stateAfterClear.currentError).toBeUndefined();
      expect(stateAfterClear.isBlocked).toBe(false);
    });

    it("应该通知监听器错误状态已清除", () => {
      const mockListener = vi.fn();
      networkStateManager.subscribe(mockListener);

      // 先记录一个网络错误
      networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Test error",
      );

      // 清除错误状态
      networkStateManager.clearErrorState();

      // 验证监听器被调用，且错误状态已清除
      expect(mockListener).toHaveBeenCalledWith(
        expect.objectContaining({
          currentError: undefined,
          isBlocked: false,
        }),
      );
    });

    it("应该在剧集切换时正确清除错误状态", () => {
      // 模拟一集播放时出现网络错误
      networkStateManager.recordNetworkError(
        "episode-1",
        "timeout",
        "Connection Timeout(Network Error: manifestLoadError)",
      );

      // 验证错误状态存在
      let state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeDefined();
      expect(state.currentError?.errorMessage).toBe(
        "Connection Timeout(Network Error: manifestLoadError)",
      );

      // 模拟切换到新剧集
      networkStateManager.clearErrorState();

      // 验证错误状态已清除
      state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeUndefined();
      expect(state.isBlocked).toBe(false);

      // 验证新剧集可以正常记录新的错误（如果出现的话）
      networkStateManager.recordNetworkError(
        "episode-2",
        "server_error",
        "New episode error",
      );

      state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeDefined();
      expect(state.currentError?.errorMessage).toBe("New episode error");
      expect(state.currentError?.type).toBe("server_error");
    });
  });

  describe("recordNetworkError", () => {
    it("应该正确记录网络错误", () => {
      const result = networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Test timeout error",
      );

      expect(result).toBe(true);

      const state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeDefined();
      expect(state.currentError?.type).toBe("timeout");
      expect(state.currentError?.errorMessage).toBe("Test timeout error");
      expect(state.currentError?.retryCount).toBe(1);
    });

    it("应该按实例ID分别记录重试次数", () => {
      // 实例1的错误
      networkStateManager.recordNetworkError(
        "instance-1",
        "timeout",
        "Error 1",
      );

      // 实例2的错误
      networkStateManager.recordNetworkError(
        "instance-2",
        "timeout",
        "Error 2",
      );

      // 实例1再次错误
      networkStateManager.recordNetworkError(
        "instance-1",
        "timeout",
        "Error 1 again",
      );

      const state = networkStateManager.getNetworkState();
      expect(state.currentError?.retryCount).toBe(2); // 实例1的重试次数
      expect(state.currentError?.errorMessage).toBe("Error 1 again");
    });
  });

  describe("resetRetryCount", () => {
    it("应该重置指定实例的重试计数", () => {
      // 记录错误
      networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Test error",
      );

      // 验证重试计数
      expect(networkStateManager.canContinueRequest("test-instance")).toBe(
        true,
      );

      // 重置重试计数
      networkStateManager.resetRetryCount("test-instance");

      // 验证可以继续请求
      expect(networkStateManager.canContinueRequest("test-instance")).toBe(
        true,
      );
    });
  });

  describe("canContinueRequest", () => {
    it("当网络离线时应该返回false", () => {
      // 模拟网络离线
      const originalOnLine = navigator.onLine;
      Object.defineProperty(navigator, "onLine", {
        value: false,
        writable: true,
        configurable: true,
      });

      // 重新创建实例以获取新的网络状态
      (NetworkStateManager as any).instance = undefined;
      const offlineNetworkManager = NetworkStateManager.getInstance();

      expect(offlineNetworkManager.canContinueRequest("test-instance")).toBe(
        false,
      );

      // 恢复原始状态
      Object.defineProperty(navigator, "onLine", {
        value: originalOnLine,
        writable: true,
        configurable: true,
      });
    });

    it("当被阻止时应该返回false", () => {
      // 模拟被阻止状态
      (networkStateManager as any).state.isBlocked = true;

      expect(networkStateManager.canContinueRequest("test-instance")).toBe(
        false,
      );
    });

    it("当重试次数超过限制时应该返回false", () => {
      // 记录多次错误，超过最大重试次数
      for (let i = 0; i < 4; i++) {
        networkStateManager.recordNetworkError(
          "test-instance",
          "timeout",
          `Error ${i + 1}`,
        );
      }

      expect(networkStateManager.canContinueRequest("test-instance")).toBe(
        false,
      );
    });
  });

  describe("延迟提示功能", () => {
    beforeEach(() => {
      // 重置单例
      (NetworkStateManager as any).instance = undefined;
      networkStateManager = NetworkStateManager.getInstance();

      // 模拟网络在线
      Object.defineProperty(navigator, "onLine", {
        value: true,
        writable: true,
        configurable: true,
      });
    });

    afterEach(() => {
      // 清理定时器 - 使用vi而不是jest
      vi.clearAllTimers();
    });

    it("网络断开时应该延迟检查缓冲而不是立即提示", () => {
      // 模拟网络断开
      Object.defineProperty(navigator, "onLine", {
        value: false,
        writable: true,
        configurable: true,
      });

      // 触发网络断开事件
      window.dispatchEvent(new Event("offline"));

      // 验证没有立即显示离线消息，但设置了错误状态
      expect(networkStateManager.getNetworkState().isOnline).toBe(false);
      expect(networkStateManager.getNetworkState().currentError?.type).toBe(
        "offline",
      );
    });

    it("应该正确处理剧集切换失败", () => {
      // 模拟网络断开
      Object.defineProperty(navigator, "onLine", {
        value: false,
        writable: true,
        configurable: true,
      });

      // 重新创建实例以获取新的网络状态
      (NetworkStateManager as any).instance = undefined;
      networkStateManager = NetworkStateManager.getInstance();

      // 调用剧集切换失败处理方法
      networkStateManager.handleEpisodeSwitchFailure(
        "test-instance",
        "Network timeout",
      );

      // 验证网络状态为离线
      expect(networkStateManager.getNetworkState().isOnline).toBe(false);
    });

    it("manifest 加载错误时应该显示错误提示", () => {
      // 模拟网络在线
      Object.defineProperty(navigator, "onLine", {
        value: true,
        writable: true,
        configurable: true,
      });

      // 记录包含 manifestLoadError 的网络错误
      networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Network Error: manifestLoadError",
      );

      // 验证错误状态已设置
      const state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeDefined();
      expect(state.currentError?.type).toBe("timeout");
      expect(state.currentError?.errorMessage).toBe("Network Error: manifestLoadError");
    });

    it("非 manifest 错误时不应该立即显示错误提示", () => {
      // 模拟网络在线
      Object.defineProperty(navigator, "onLine", {
        value: true,
        writable: true,
        configurable: true,
      });

      // 记录不包含 manifestLoadError 的网络错误
      networkStateManager.recordNetworkError(
        "test-instance",
        "timeout",
        "Network Error: fragLoadTimeOut",
      );

      // 验证错误状态已设置
      const state = networkStateManager.getNetworkState();
      expect(state.currentError).toBeDefined();
      expect(state.currentError?.type).toBe("timeout");
      expect(state.currentError?.errorMessage).toBe("Network Error: fragLoadTimeOut");
    });
  });
});
