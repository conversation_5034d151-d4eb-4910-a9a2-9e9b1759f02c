import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

import { PlayerFactory } from "../factories/PlayerFactory";
import { LifecycleController } from "../instance/LifecycleController";
import { EventManager } from "./EventManager";
import { NetworkStateManager } from "./NetworkStateManager";
import { StateManager } from "./StateManager";

/**
 * @file InstanceLifecycleManager.ts
 * @description 实例生命周期管理器
 * - 集成 LifecycleController 到现有的多播放器系统
 * - 提供统一的实例管理接口
 * - 处理剧集切换时的实例优化
 */

export class InstanceLifecycleManager {
  private static instance: InstanceLifecycleManager;
  private lifecycleController: LifecycleController;
  private playerFactory: PlayerFactory;
  private stateManager: StateManager;
  private eventManager: EventManager;
  private cleanupInterval: NodeJS.Timeout | null = null;

  private constructor(
    playerFactory: PlayerFactory,
    stateManager: StateManager,
    eventManager: EventManager,
  ) {
    this.playerFactory = playerFactory;
    this.stateManager = stateManager;
    this.eventManager = eventManager;
    this.lifecycleController = new LifecycleController(
      playerFactory,
      eventManager,
    );

    // 启动定期清理任务
    this.startPeriodicCleanup();

    moduleLoggers.InstanceLifecycleManager.info(
      "InstanceLifecycleManager initialized",
    );
  }

  public static getInstance(
    playerFactory?: PlayerFactory,
    stateManager?: StateManager,
    eventManager?: EventManager,
  ): InstanceLifecycleManager {
    if (!InstanceLifecycleManager.instance) {
      if (!playerFactory || !stateManager || !eventManager) {
        throw new Error(
          "InstanceLifecycleManager requires all dependencies for first initialization",
        );
      }
      InstanceLifecycleManager.instance = new InstanceLifecycleManager(
        playerFactory,
        stateManager,
        eventManager,
      );
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_INSTANCE_LIFECYCLE_MANAGER_CLASS__ =
          InstanceLifecycleManager;
      }
    }
    return InstanceLifecycleManager.instance;
  }

  /**
   * 创建播放器实例（集成生命周期管理）
   */
  public createPlayerInstance(
    id: string,
    containerId: string,
    videoData: IPlayerVideoData,
    autoPlay: boolean = false,
    startTime: number = 0,
    currentEpisode?: IPlayerEpisode,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ) {
    moduleLoggers.InstanceLifecycleManager.info(
      `创建播放器实例: ${id} (第${currentEpisode?.no || 1}集)`,
    );

    // 使用生命周期控制器创建实例
    const instance = this.lifecycleController.createInstance(
      id,
      containerId,
      videoData,
      autoPlay,
      startTime,
      currentEpisode,
      onEnded,
    );

    // 设置活跃播放器
    this.lifecycleController.setActivePlayer(id);

    // 创建实例后立即预加载下一集
    if (currentEpisode) {
      moduleLoggers.InstanceLifecycleManager.info(
        `创建实例后立即预加载下一集: 当前第${currentEpisode.no}集`,
      );
      this.smartPreloadNextEpisode(videoData, currentEpisode.no, onEnded);
    }

    return instance;
  }

  /**
   * 切换剧集（集成生命周期管理）
   */
  public switchEpisode(
    episodeNo: number,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): void {
    const activeInstance = this.stateManager.getActiveInstance();
    if (!activeInstance?.videoData) {
      moduleLoggers.InstanceLifecycleManager.warn(
        "没有活跃的播放器实例，无法切换剧集",
      );
      return;
    }

    const videoData = activeInstance.videoData;
    const videoId = videoData.id;
    const targetEpisodeInstanceId = `episode-${videoId}-${episodeNo}`;
    const targetContainerId = `container-${targetEpisodeInstanceId}`;

    // 获取当前集数索引（用于事件触发）
    const currentEpisodeIndex = activeInstance.currentEpisode?.no || 1;
    const targetEpisodeIndex = episodeNo;

    moduleLoggers.InstanceLifecycleManager.info(
      `切换剧集: 第${episodeNo}集 -> ${targetEpisodeInstanceId}`,
    );

    // 检查是否真的在切换剧集
    const currentEpisode = activeInstance.currentEpisode;
    const isActuallySwitchingEpisode =
      !currentEpisode || currentEpisode.no !== episodeNo;

    // 只有在真正切换剧集时才清除错误状态
    if (isActuallySwitchingEpisode) {
      moduleLoggers.InstanceLifecycleManager.info(
        `真正切换剧集，清除错误状态: 从第${currentEpisode?.no || "未知"}集到第${episodeNo}集`,
      );
      const networkStateManager = NetworkStateManager.getInstance();
      networkStateManager.clearErrorState();
    } else {
      moduleLoggers.InstanceLifecycleManager.info(
        `重新播放当前剧集，保持错误状态: 第${episodeNo}集`,
      );
    }

    // 使用生命周期控制器处理剧集切换，传递 onEnded 回调
    this.lifecycleController.handleEpisodeSwitch(episodeNo, videoData, onEnded);

    // 使用惰性处理获取或创建目标实例
    const targetEpisode = videoData.series.find((ep) => ep.no === episodeNo);
    if (!targetEpisode) {
      moduleLoggers.InstanceLifecycleManager.error(
        `目标剧集不存在: 第${episodeNo}集`,
      );
      return;
    }

    // 使用惰性处理获取或创建实例，传递 onEnded 回调
    const targetInstance = this.lifecycleController.getOrCreateInstance(
      targetEpisodeInstanceId,
      targetContainerId,
      videoData,
      true, // 自动播放
      0, // 从头开始
      targetEpisode,
      onEnded, // 传递 onEnded 回调
    );

    // 播放指定集数
    if (targetInstance) {
      targetInstance.playEpisode(episodeNo);
    }

    // 设置活跃播放器
    this.lifecycleController.setActivePlayer(targetEpisodeInstanceId);
    this.stateManager.setActiveInstance(targetEpisodeInstanceId);

    // 触发剧集切换事件
    this.eventManager.emit("episodeChange", {
      fromEpisodeIndex: currentEpisodeIndex,
      toEpisodeIndex: targetEpisodeIndex,
    });

    // 暂停当前播放器
    const currentInstance = this.playerFactory.getPlayer(activeInstance.id);
    if (
      currentInstance?.xgplayer &&
      activeInstance.id !== targetEpisodeInstanceId
    ) {
      currentInstance.pause();
    }
  }

  /**
   * 预加载指定集数
   */
  public preloadEpisode(
    videoData: IPlayerVideoData,
    episodeNo: number,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): void {
    const videoId = videoData.id;
    const containerId = `container-episode-${videoId}-${episodeNo}`;

    moduleLoggers.InstanceLifecycleManager.info(`预加载剧集: 第${episodeNo}集`);

    this.lifecycleController.preloadEpisode(
      videoData,
      episodeNo,
      containerId,
      onEnded,
    );
  }

  /**
   * 智能预加载下一集（集成 PreloadManager，自动终止之前的预加载）
   */
  public smartPreloadNextEpisode(
    videoData: IPlayerVideoData,
    currentEpisodeNo: number,
    onEnded?: (playerId: string, currentEpisodeNo: number) => void,
  ): void {
    moduleLoggers.InstanceLifecycleManager.info(
      `智能预加载下一集: 当前第${currentEpisodeNo}集`,
    );

    this.lifecycleController.smartPreloadNextEpisode(
      videoData,
      currentEpisodeNo,
      onEnded,
    );
  }

  /**
   * 获取预加载状态
   */
  public getPreloadStatus() {
    return this.lifecycleController.getPreloadStatus();
  }

  /**
   * 销毁播放器实例
   */
  public destroyPlayerInstance(id: string): void {
    moduleLoggers.InstanceLifecycleManager.info(`销毁播放器实例: ${id}`);

    this.lifecycleController.destroyInstance(id);
  }

  /**
   * 获取实例统计信息
   */
  public getInstanceStats() {
    return this.lifecycleController.getStats();
  }

  /**
   * 获取所有实例信息
   */
  public getAllInstances() {
    return this.lifecycleController.getAllInstances();
  }

  /**
   * 获取核心实例
   */
  public getCoreInstances() {
    return this.lifecycleController.getCoreInstances();
  }

  /**
   * 手动清理过期实例
   */
  public cleanupExpiredInstances(): void {
    moduleLoggers.InstanceLifecycleManager.info("手动清理过期实例");
    this.lifecycleController.cleanupExpiredInstances();
  }

  /**
   * 启动定期清理任务
   */
  private startPeriodicCleanup(): void {
    // 每 1 分钟清理一次过期实例
    this.cleanupInterval = setInterval(
      () => {
        this.lifecycleController.cleanupExpiredInstances();
      },
      1 * 60 * 1000,
    );

    moduleLoggers.InstanceLifecycleManager.info("启动定期清理任务 (每1分钟)");
  }

  /**
   * 停止定期清理任务
   */
  public stopPeriodicCleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      moduleLoggers.InstanceLifecycleManager.info("停止定期清理任务");
    }
  }

  /**
   * 销毁所有实例
   */
  public destroyAllInstances(): void {
    moduleLoggers.InstanceLifecycleManager.info("销毁所有实例");

    // 先销毁生命周期控制器中的实例
    moduleLoggers.InstanceLifecycleManager.info(
      "1. 销毁 LifecycleController 中的实例",
    );
    this.lifecycleController.destroyAllInstances();

    // 同时清理 PlayerFactory 中的实例，保持状态一致性
    if (this.playerFactory) {
      moduleLoggers.InstanceLifecycleManager.info(
        "2. 销毁 PlayerFactory 中的实例",
      );
      this.playerFactory.destroyAllPlayers();
    } else {
      moduleLoggers.InstanceLifecycleManager.warn("PlayerFactory 不存在");
    }

    moduleLoggers.InstanceLifecycleManager.info("所有实例销毁完成");
  }

  /**
   * 获取生命周期控制器实例
   */
  public getLifecycleController(): LifecycleController {
    return this.lifecycleController;
  }

  /**
   * 销毁管理器
   */
  public destroy(): void {
    this.stopPeriodicCleanup();
    this.destroyAllInstances();
    moduleLoggers.InstanceLifecycleManager.info(
      "InstanceLifecycleManager destroyed",
    );
  }

  /**
   * 重置 InstanceLifecycleManager 状态
   */
  public reset(): void {
    moduleLoggers.InstanceLifecycleManager.info(
      "重置 InstanceLifecycleManager 状态",
    );
    this.destroyAllInstances();
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.InstanceLifecycleManager.info(
      "开始重置全局 InstanceLifecycleManager 实例",
    );

    if (InstanceLifecycleManager.instance) {
      InstanceLifecycleManager.instance.reset();
      InstanceLifecycleManager.instance = null as any;
    }

    moduleLoggers.InstanceLifecycleManager.info(
      "全局 InstanceLifecycleManager 实例重置完成",
    );
  }
}
