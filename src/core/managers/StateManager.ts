/**
 * @file StateManager.ts
 * @description 状态管理器 (SM)
 * - 管理播放器的核心状态，例如播放/暂停、加载状态、当前剧集等。
 * - 为UI层提供响应式状态。
 */
import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { IProduct } from "@/types/product";
import { moduleLoggers } from "@/utils/LogManager";

import { PlayerInstance } from "../instance/PlayerInstance";
import { NetworkStateManager } from "./NetworkStateManager";

export interface PlayerInstanceState {
  id: string;
  containerId: string;
  videoData?: IPlayerVideoData;
  isLoading: boolean;
  isActive: boolean;
  status:
    | "idle"
    | "loading"
    | "playing"
    | "paused"
    | "error"
    | "ready"
    | "locked";
  currentEpisode?: IPlayerEpisode;
  currentTime: number;
  cumulatedWatchedTime?: number;
}

export interface GlobalState {
  instances: Map<string, PlayerInstanceState>;
  activeInstanceId: string | null;
  videoDetail: IPlayerVideoData | null;
  currentEpisodeIndex: number;
  globalConfig: {
    maxPreloadInstances: number;
    autoPreload: boolean;
    preloadDistance: number;
  };
  // 全局播放设置
  globalPlaybackSettings: {
    playbackRate: number;
    volume: number;
    muted: boolean;
  };
  players: Map<string, PlayerInstance>;
}

export class StateManager {
  private static instance: StateManager;
  private state: GlobalState;
  private listeners: Map<string, Set<(state: PlayerInstanceState) => void>>;
  private globalListeners: Set<(state: GlobalState) => void>;

  private constructor() {
    this.state = {
      instances: new Map(),
      activeInstanceId: null,
      videoDetail: null,
      currentEpisodeIndex: 0,
      globalConfig: {
        maxPreloadInstances: 3,
        autoPreload: true,
        preloadDistance: 2,
      },
      globalPlaybackSettings: {
        playbackRate: 1.25,
        volume: 1,
        muted: false,
      },
      players: new Map(),
    };
    this.listeners = new Map();
    this.globalListeners = new Set();
  }

  public static getInstance(): StateManager {
    if (!StateManager.instance) {
      StateManager.instance = new StateManager();
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_STATE_MANAGER_CLASS__ = StateManager;
      }
    }
    return StateManager.instance;
  }

  // 实例状态管理
  public createInstance(
    id: string,
    containerId: string,
    videoData?: IPlayerVideoData,
  ): PlayerInstanceState {
    const instanceState: PlayerInstanceState = {
      id,
      containerId,
      videoData: videoData || undefined,
      isLoading: false,
      isActive: false,
      status: "idle",
      currentEpisode: undefined,
      currentTime: 0,
    };

    this.state.instances.set(id, instanceState);
    this.notifyInstanceListeners(id, instanceState);
    this.notifyGlobalListeners();

    moduleLoggers.StateManager.info(`创建实例: ${id}`);
    return instanceState;
  }

  public updateInstance(
    id: string,
    updates: Partial<PlayerInstanceState>,
  ): void {
    const instance = this.state.instances.get(id);
    if (!instance) {
      moduleLoggers.StateManager.warn(`实例不存在: ${id}`);
      return;
    }

    const updatedInstance = { ...instance, ...updates };
    this.state.instances.set(id, updatedInstance);
    this.notifyInstanceListeners(id, updatedInstance);
    this.notifyGlobalListeners();
  }

  public removeInstance(id: string): void {
    if (this.state.instances.delete(id)) {
      this.listeners.delete(id);
      if (this.state.activeInstanceId === id) {
        this.state.activeInstanceId = null;
      }
      this.notifyGlobalListeners();
      moduleLoggers.StateManager.info(`删除实例: ${id}`);
    }
  }

  public getInstance(id: string): PlayerInstanceState | undefined {
    return this.state.instances.get(id);
  }

  public getAllInstances(): PlayerInstanceState[] {
    return Array.from(this.state.instances.values());
  }

  // 活跃实例管理
  public setActiveInstance(id: string): void {
    const instance = this.state.instances.get(id);
    if (instance) {
      // 取消当前活跃实例
      if (this.state.activeInstanceId) {
        this.updateInstance(this.state.activeInstanceId, { isActive: false });
      }

      // 设置新的活跃实例
      this.state.activeInstanceId = id;
      this.updateInstance(id, { isActive: true });

      moduleLoggers.StateManager.info(`设置活跃实例: ${id}`);

      // 通知全局监听器状态变化
      this.notifyGlobalListeners();

      // 如果网络已恢复，触发新活跃实例的HLS加载
      this.triggerActiveInstanceHlsLoading();
    } else {
      moduleLoggers.StateManager.warn(`实例不存在，无法设置活跃实例: ${id}`);
    }
  }

  public getActiveInstance(): PlayerInstanceState | null {
    if (this.state.activeInstanceId) {
      return this.state.instances.get(this.state.activeInstanceId) || null;
    }
    return null;
  }

  // 监听器管理
  public subscribeToInstance(
    id: string,
    listener: (state: PlayerInstanceState) => void,
  ): () => void {
    if (!this.listeners.has(id)) {
      this.listeners.set(id, new Set());
    }
    this.listeners.get(id)!.add(listener);

    // 返回取消订阅函数
    return () => {
      const instanceListeners = this.listeners.get(id);
      if (instanceListeners) {
        instanceListeners.delete(listener);
        if (instanceListeners.size === 0) {
          this.listeners.delete(id);
        }
      }
    };
  }

  public subscribeToGlobalState(
    listener: (state: GlobalState) => void,
  ): () => void {
    this.globalListeners.add(listener);
    return () => {
      this.globalListeners.delete(listener);
    };
  }

  private notifyInstanceListeners(
    id: string,
    state: PlayerInstanceState,
  ): void {
    const listeners = this.listeners.get(id);
    if (listeners) {
      listeners.forEach((listener) => listener(state));
    }
  }

  private notifyGlobalListeners(): void {
    this.globalListeners.forEach((listener) => listener(this.state));
  }

  // Setters for new global state
  // public setProductData(productData: IProduct) {
  //   this.state.productData = productData;
  //   this.notifyGlobalListeners();
  // }

  public setVideoDetail(videoDetail: IPlayerVideoData) {
    this.state.videoDetail = videoDetail;
    this.notifyGlobalListeners();
  }

  public setCurrentEpisodeIndex(index: number) {
    this.state.currentEpisodeIndex = index;
    this.notifyGlobalListeners();
  }

  // 全局状态访问
  public getGlobalState(): GlobalState {
    return { ...this.state };
  }

  public updateGlobalConfig(
    config: Partial<GlobalState["globalConfig"]>,
  ): void {
    this.state.globalConfig = { ...this.state.globalConfig, ...config };
    this.notifyGlobalListeners();
  }

  public setPlayer(id: string, player: PlayerInstance): void {
    this.state.players.set(id, player);
  }

  public getPlayer(id: string): PlayerInstance | undefined {
    return this.state.players.get(id);
  }

  public removePlayer(id: string): void {
    this.state.players.delete(id);
  }

  // 全局播放设置管理
  public getGlobalPlaybackSettings() {
    return { ...this.state.globalPlaybackSettings };
  }

  public updateGlobalPlaybackSettings(
    settings: Partial<GlobalState["globalPlaybackSettings"]>,
  ): void {
    this.state.globalPlaybackSettings = {
      ...this.state.globalPlaybackSettings,
      ...settings,
    };
    this.notifyGlobalListeners();
  }

  public setGlobalPlaybackRate(rate: number): void {
    this.updateGlobalPlaybackSettings({ playbackRate: rate });
  }

  public setGlobalVolume(volume: number): void {
    this.updateGlobalPlaybackSettings({
      volume: Math.max(0, Math.min(1, volume)),
    });
  }

  public setGlobalMuted(muted: boolean): void {
    this.updateGlobalPlaybackSettings({ muted });
  }

  /**
   * 重置状态管理器到初始状态
   * @param resetActiveInstance 是否重置活跃实例，默认为 true
   */
  public reset(resetActiveInstance: boolean = true): void {
    moduleLoggers.StateManager.info("重置 StateManager 到初始状态");

    // 保存当前的活跃实例ID（如果不需要重置的话）
    const currentActiveInstanceId = resetActiveInstance
      ? null
      : this.state.activeInstanceId;

    // 清理所有实例
    this.state.instances.clear();
    this.state.players.clear();
    this.listeners.clear();
    this.globalListeners.clear();

    // 重置全局状态
    this.state = {
      instances: new Map(),
      activeInstanceId: currentActiveInstanceId, // 根据参数决定是否重置
      videoDetail: null,
      currentEpisodeIndex: 0,
      globalConfig: {
        maxPreloadInstances: 3,
        autoPreload: true,
        preloadDistance: 2,
      },
      globalPlaybackSettings: {
        playbackRate: 1.25,
        volume: 1,
        muted: false,
      },
      players: new Map(),
    };

    moduleLoggers.StateManager.info(
      `StateManager 重置完成${resetActiveInstance ? "" : "，保留活跃实例状态"}`,
    );
  }

  /**
   * 触发活跃实例的HLS加载
   */
  private triggerActiveInstanceHlsLoading(): void {
    // 检查网络状态
    const networkStateManager = NetworkStateManager.getInstance();
    const networkState = networkStateManager.getNetworkState();

    if (networkState.isOnline && !networkState.isBlocked) {
      // 延迟触发，确保状态更新完成
      setTimeout(() => {
        networkStateManager.triggerActiveInstanceHlsLoading();
      }, 50);
    }
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.StateManager.info("开始重置全局 StateManager 实例");

    if (StateManager.instance) {
      StateManager.instance.reset();
      StateManager.instance = null as any;
    }

    moduleLoggers.StateManager.info("全局 StateManager 实例重置完成");
  }
}
