import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";

import { PlayerFactory } from "../factories/PlayerFactory";
import { PreloadManager } from "../managers/PreloadManager";
import { EventManager } from "./EventManager";
import { InstanceLifecycleManager } from "./InstanceLifecycleManager";
import { StateManager } from "./StateManager";

// Mock dependencies
vi.mock("@/utils/LogManager");
vi.mock("../factories/PlayerFactory");
vi.mock("./EventManager");
vi.mock("./StateManager");

// Mock PreloadManager
vi.mock("../managers/PreloadManager", () => ({
  PreloadManager: {
    getInstance: vi.fn(() => ({
      preloadEpisode: vi.fn(),
      cancelPreload: vi.fn(),
      getPreloadStatus: vi.fn(() => ({
        total: 0,
        active: 0,
        tasks: [],
      })),
    })),
  },
}));

describe("InstanceLifecycleManager", () => {
  let instanceLifecycleManager: InstanceLifecycleManager;
  let mockPlayerFactory: any;
  let mockEventManager: any;
  let mockStateManager: any;
  let mockVideoData: IPlayerVideoData;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();

    // Create mock instances
    mockPlayerFactory = {
      createPlayer: vi.fn(),
      getPlayer: vi.fn(),
      destroyPlayer: vi.fn(),
      getAllPlayers: vi.fn(),
      getStats: vi.fn(),
      destroyAllPlayers: vi.fn(),
    };

    mockEventManager = {
      on: vi.fn(),
      off: vi.fn(),
      emit: vi.fn(),
    };

    mockStateManager = {
      getInstance: vi.fn(),
      getActiveInstance: vi.fn(),
      createInstance: vi.fn(),
      updateInstance: vi.fn(),
      removeInstance: vi.fn(),
      setActiveInstance: vi.fn(),
    };

    // Mock video data
    mockVideoData = {
      id: "test-video-1",
      title: "Test Video",
      series: [
        {
          id: "ep1",
          no: 1,
          // title: "Episode 1",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep1",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep2",
          no: 2,
          // title: "Episode 2",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep2",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
        {
          id: "ep3",
          no: 3,
          // title: "Episode 3",
          currentTime: "0",
          is_last_play: false,
          subTitles: [],
          metaFields: {},
          url: "http://example.com/ep3",
          status: "free",
          play_duration: "0",
          operate: { liked: false, collected: false, subscribed: true },
        },
      ],
      description: "Test Video",
      tags: [],
      cover: {
        src: "http://example.com/cover.jpg",
        alt: "Test Video",
        width: 100,
        height: 100,
      },
      operate: { liked: false, collected: false, subscribed: true },
      metaFields: {},
    };

    // Mock player factory to return stateManager
    mockPlayerFactory.stateManager = mockStateManager;

    // Create instance lifecycle manager
    instanceLifecycleManager = InstanceLifecycleManager.getInstance(
      mockPlayerFactory,
      mockStateManager,
      mockEventManager,
    );
  });

  describe("基本功能", () => {
    it("应该能够创建实例", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-1",
        destroy: vi.fn(),
        playEpisode: vi.fn(),
      };
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      const instance = instanceLifecycleManager.createPlayerInstance(
        "episode-test-video-1-1",
        "container-episode-test-video-1-1",
        mockVideoData,
        false,
        0,
        mockVideoData.series[0],
      );

      expect(instance).toBe(mockInstance);
    });

    it("应该能够获取实例统计信息", () => {
      const stats = instanceLifecycleManager.getInstanceStats();
      expect(stats).toBeDefined();
      expect(stats.maxAllowed).toBe(5);
    });

    it("应该能够获取所有实例", () => {
      const instances = instanceLifecycleManager.getAllInstances();
      expect(Array.isArray(instances)).toBe(true);
    });

    it("应该能够获取核心实例", () => {
      const coreInstances = instanceLifecycleManager.getCoreInstances();
      expect(Array.isArray(coreInstances)).toBe(true);
    });
  });

  describe("剧集切换", () => {
    it("应该能够切换剧集", () => {
      const mockInstance = {
        instanceId: "episode-test-video-1-2",
        destroy: vi.fn(),
        playEpisode: vi.fn(),
        xgplayer: {
          pause: vi.fn(),
        },
      };

      // Mock active instance
      mockStateManager.getActiveInstance.mockReturnValue({
        id: "episode-test-video-1-1",
        videoData: mockVideoData,
      });

      // Mock player factory to return the instance
      mockPlayerFactory.getPlayer.mockReturnValue(mockInstance);
      mockPlayerFactory.createPlayer.mockReturnValue(mockInstance);

      // 切换剧集
      instanceLifecycleManager.switchEpisode(2);

      // TODO: 验证是否调用了相关方法
      // expect(mockStateManager.getActiveInstance).toHaveBeenCalled();
      // expect(mockLifecycleController.playEpisode).toHaveBeenCalledWith(2);
    });
  });

  describe("清理功能", () => {
    it("应该能够清理过期实例", () => {
      instanceLifecycleManager.cleanupExpiredInstances();
      // 这个方法应该不会抛出错误
      expect(true).toBe(true);
    });

    it("应该能够销毁所有实例", () => {
      instanceLifecycleManager.destroyAllInstances();
      // 这个方法应该不会抛出错误
      expect(true).toBe(true);
    });
  });
});

describe("InstanceLifecycleManager - 预加载功能", () => {
  let instanceLifecycleManager: InstanceLifecycleManager;
  let mockPlayerFactory: any;
  let mockStateManager: any;
  let mockEventManager: any;
  let mockPreloadManager: any;

  beforeEach(() => {
    // 重置所有 mocks
    vi.clearAllMocks();

    // 创建 mock 对象
    mockPlayerFactory = {
      createPlayer: vi.fn(),
      getPlayer: vi.fn(),
      getAllPlayers: vi.fn(() => []),
      destroyPlayer: vi.fn(),
    };

    mockStateManager = {
      createInstance: vi.fn(),
      setActiveInstance: vi.fn(),
      getActiveInstance: vi.fn(),
      getInstance: vi.fn(),
      setPlayer: vi.fn(),
      getPlayer: vi.fn(),
    };

    mockEventManager = {
      emit: vi.fn(),
      on: vi.fn(),
      off: vi.fn(),
    };

    mockPreloadManager = {
      preloadEpisode: vi.fn(),
      cancelPreload: vi.fn(),
      getPreloadStatus: vi.fn(() => ({
        total: 0,
        active: 0,
        tasks: [],
      })),
    };

    // 设置 PreloadManager mock
    const { PreloadManager } = require("../managers/PreloadManager");
    PreloadManager.getInstance.mockReturnValue(mockPreloadManager);

    // 创建实例
    instanceLifecycleManager = InstanceLifecycleManager.getInstance(
      mockPlayerFactory,
      mockStateManager,
      mockEventManager,
    );
  });

  afterEach(() => {
    InstanceLifecycleManager.resetGlobalInstance();
  });

  describe("createPlayerInstance", () => {
    it("应该在创建实例后立即预加载下一集", () => {
      const mockVideoData: IPlayerVideoData = {
        id: "test-video",
        title: "Test Video",
        description: "Test Description",
        tags: [],
        cover: {
          src: "http://example.com/cover.jpg",
          alt: "Test Video",
          width: 100,
          height: 100,
        },
        operate: { liked: false, collected: false, subscribed: true },
        metaFields: {},
        series: [
          {
            no: 1,
            status: "free",
            url: "http://example.com/ep1.m3u8",
            id: "ep1",
            currentTime: "0",
            is_last_play: false,
            subTitles: [],
            metaFields: {},
            play_duration: "0",
            operate: { liked: false, collected: false, subscribed: true },
          },
          {
            no: 2,
            status: "free",
            url: "http://example.com/ep2.m3u8",
            id: "ep2",
            currentTime: "0",
            is_last_play: false,
            subTitles: [],
            metaFields: {},
            play_duration: "0",
            operate: { liked: false, collected: false, subscribed: true },
          },
        ],
      };

      const mockCurrentEpisode: IPlayerEpisode = {
        no: 1,
        status: "free",
        id: "ep1",
        currentTime: "0",
        is_last_play: false,
        subTitles: [],
        metaFields: {},
        play_duration: "0",
        url: "http://example.com/ep1.m3u8",
        operate: { liked: false, collected: false, subscribed: true },
      };
      const mockInstance = { id: "test-instance" };

      // Mock lifecycleController
      const mockLifecycleController = {
        createInstance: vi.fn(() => mockInstance),
        setActivePlayer: vi.fn(),
        smartPreloadNextEpisode: vi.fn(),
      };

      // 替换 lifecycleController
      (instanceLifecycleManager as any).lifecycleController =
        mockLifecycleController;

      // 调用 createPlayerInstance
      instanceLifecycleManager.createPlayerInstance(
        "test-id",
        "test-container",
        mockVideoData,
        true,
        0,
        mockCurrentEpisode,
      );

      // 验证创建实例
      expect(mockLifecycleController.createInstance).toHaveBeenCalledWith(
        "test-id",
        "test-container",
        mockVideoData,
        true,
        0,
        mockCurrentEpisode,
        undefined,
      );

      // 验证设置活跃播放器
      expect(mockLifecycleController.setActivePlayer).toHaveBeenCalledWith(
        "test-id",
      );

      // 验证预加载下一集
      expect(
        mockLifecycleController.smartPreloadNextEpisode,
      ).toHaveBeenCalledWith(mockVideoData, 1, undefined);
    });

    it("应该跳过预加载如果当前剧集不存在", () => {
      const mockVideoData: IPlayerVideoData = {
        id: "test-video",
        title: "Test Video",
        description: "Test Description",
        tags: [],
        cover: {
          src: "http://example.com/cover.jpg",
          alt: "Test Video",
          width: 100,
          height: 100,
        },
        series: [],
        operate: { liked: false, collected: false, subscribed: true },
        metaFields: {},
      };

      const mockInstance = { id: "test-instance" };

      // Mock lifecycleController
      const mockLifecycleController = {
        createInstance: vi.fn(() => mockInstance),
        setActivePlayer: vi.fn(),
        smartPreloadNextEpisode: vi.fn(),
      };

      // 替换 lifecycleController
      (instanceLifecycleManager as any).lifecycleController =
        mockLifecycleController;

      // 调用 createPlayerInstance（不传递 currentEpisode）
      instanceLifecycleManager.createPlayerInstance(
        "test-id",
        "test-container",
        mockVideoData,
        true,
        0,
      );

      // 验证创建实例
      expect(mockLifecycleController.createInstance).toHaveBeenCalled();

      // 验证没有调用预加载
      expect(
        mockLifecycleController.smartPreloadNextEpisode,
      ).not.toHaveBeenCalled();
    });
  });

  describe("smartPreloadNextEpisode", () => {
    it("应该调用 lifecycleController 的智能预加载方法", () => {
      const mockVideoData: IPlayerVideoData = {
        id: "test-video",
        title: "Test Video",
        description: "Test Description",
        tags: [],
        cover: {
          src: "http://example.com/cover.jpg",
          alt: "Test Video",
          width: 100,
          height: 100,
        },
        series: [
          {
            no: 1,
            status: "free",
            url: "http://example.com/ep1.m3u8",
            id: "ep1",
            currentTime: "0",
            is_last_play: false,
            subTitles: [],
            metaFields: {},
            play_duration: "0",
            operate: { liked: false, collected: false, subscribed: true },
          },
          {
            no: 2,
            status: "free",
            url: "http://example.com/ep2.m3u8",
            id: "ep2",
            currentTime: "0",
            is_last_play: false,
            subTitles: [],
            metaFields: {},
            play_duration: "0",
            operate: { liked: false, collected: false, subscribed: true },
          },
        ],
        operate: { liked: false, collected: false, subscribed: true },
        metaFields: {},
      };

      const mockLifecycleController = {
        smartPreloadNextEpisode: vi.fn(),
      };

      // 替换 lifecycleController
      (instanceLifecycleManager as any).lifecycleController =
        mockLifecycleController;

      // 调用智能预加载
      instanceLifecycleManager.smartPreloadNextEpisode(mockVideoData, 1);

      // 验证调用了 lifecycleController 的方法
      expect(
        mockLifecycleController.smartPreloadNextEpisode,
      ).toHaveBeenCalledWith(mockVideoData, 1, undefined);
    });
  });
});
