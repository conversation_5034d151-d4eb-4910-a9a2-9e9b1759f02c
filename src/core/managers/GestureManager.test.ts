/**
 * @file GestureManager.test.ts
 * @description GestureManager 测试文件 - 验证防重复触发机制
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest";

import { GestureManager } from "./GestureManager";

function createTouchEvent(type: string, y: number, x: number) {
  return new TouchEvent(type, {
    touches: [{ clientY: y, clientX: x } as Touch],
    changedTouches: [{ clientY: y, clientX: x } as Touch],
    bubbles: true,
    cancelable: true,
  });
}

describe("GestureManager", () => {
  let gestureManager: GestureManager;
  let mockOnNextEpisode: ReturnType<typeof vi.fn>;
  let mockOnPrevEpisode: ReturnType<typeof vi.fn>;

  beforeEach(() => {
    gestureManager = new GestureManager({
      minSwipeDistance: 50,
      episodeChangeCooldown: 1000,
      swipeCooldown: 500,
    });
    mockOnNextEpisode = vi.fn();
    mockOnPrevEpisode = vi.fn();
    gestureManager.init(mockOnNextEpisode, mockOnPrevEpisode);
  });

  afterEach(() => {
    gestureManager.destroy();
  });

  it("上滑应该切换下一集", () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    ); // 上滑60px
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
  });

  it("下滑应该切换上一集", () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 180, 100),
    ); // 下滑80px
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 180, 100),
    );
    expect(mockOnPrevEpisode).toHaveBeenCalledTimes(1);
  });

  it("短距离滑动不应触发切换", () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 90, 100),
    ); // 只滑动10px
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 90, 100),
    );
    expect(mockOnNextEpisode).not.toHaveBeenCalled();
    expect(mockOnPrevEpisode).not.toHaveBeenCalled();
  });

  it("快速连续滑动只触发一次切换", () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
    // 立即第二次滑动
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
  });

  it("冷却时间后允许再次切换", async () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
    // 等待冷却时间
    await new Promise((resolve) => setTimeout(resolve, 1100));
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(2);
  });

  it("音量调整时不触发切换", () => {
    (window as any).__BS_IS_ADJUSTING_VOLUME__ = true;
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).not.toHaveBeenCalled();
    expect(mockOnPrevEpisode).not.toHaveBeenCalled();
    (window as any).__BS_IS_ADJUSTING_VOLUME__ = false;
  });

  it("事件监听器管理: 销毁后不再响应", () => {
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
    gestureManager.destroy();
    mockOnNextEpisode.mockClear();
    (gestureManager as any).handleTouchStart(
      createTouchEvent("touchstart", 100, 100),
    );
    (gestureManager as any).handleTouchMove(
      createTouchEvent("touchmove", 40, 100),
    );
    (gestureManager as any).handleTouchEnd(
      createTouchEvent("touchend", 40, 100),
    );
    expect(mockOnNextEpisode).not.toHaveBeenCalled();
  });

  describe("全局手势禁用功能", () => {
    it("全局禁用手势后不应触发切换", () => {
      // 全局禁用手势
      GestureManager.disableGlobalGesture();
      expect(GestureManager.isGlobalGestureDisabled()).toBe(true);

      (gestureManager as any).handleTouchStart(
        createTouchEvent("touchstart", 100, 100),
      );
      (gestureManager as any).handleTouchMove(
        createTouchEvent("touchmove", 40, 100),
      );
      (gestureManager as any).handleTouchEnd(
        createTouchEvent("touchend", 40, 100),
      );

      // 全局禁用时不应该触发切换
      expect(mockOnNextEpisode).not.toHaveBeenCalled();

      // 重新启用手势
      GestureManager.enableGlobalGesture();
    });

    it("全局启用手势后应正常触发切换", () => {
      // 先禁用
      GestureManager.disableGlobalGesture();
      expect(GestureManager.isGlobalGestureDisabled()).toBe(true);

      // 再启用
      GestureManager.enableGlobalGesture();
      expect(GestureManager.isGlobalGestureDisabled()).toBe(false);

      (gestureManager as any).handleTouchStart(
        createTouchEvent("touchstart", 100, 100),
      );
      (gestureManager as any).handleTouchMove(
        createTouchEvent("touchmove", 40, 100),
      );
      (gestureManager as any).handleTouchEnd(
        createTouchEvent("touchend", 40, 100),
      );

      // 启用后应该正常触发
      expect(mockOnNextEpisode).toHaveBeenCalledTimes(1);
    });

    it("实例级别禁用不影响全局状态", () => {
      // 实例级别禁用
      gestureManager.disableGesture();
      expect(GestureManager.isGlobalGestureDisabled()).toBe(false);

      (gestureManager as any).handleTouchStart(
        createTouchEvent("touchstart", 100, 100),
      );
      (gestureManager as any).handleTouchMove(
        createTouchEvent("touchmove", 40, 100),
      );
      (gestureManager as any).handleTouchEnd(
        createTouchEvent("touchend", 40, 100),
      );

      // 实例禁用时不应该触发
      expect(mockOnNextEpisode).not.toHaveBeenCalled();

      // 重新启用实例
      gestureManager.enableGesture();
    });

    it("全局禁用优先级高于实例启用", () => {
      // 全局禁用
      GestureManager.disableGlobalGesture();
      // 实例启用
      gestureManager.enableGesture();

      (gestureManager as any).handleTouchStart(
        createTouchEvent("touchstart", 100, 100),
      );
      (gestureManager as any).handleTouchMove(
        createTouchEvent("touchmove", 40, 100),
      );
      (gestureManager as any).handleTouchEnd(
        createTouchEvent("touchend", 40, 100),
      );

      // 全局禁用优先级更高，不应该触发
      expect(mockOnNextEpisode).not.toHaveBeenCalled();

      // 清理
      GestureManager.enableGlobalGesture();
    });
  });
});
