import { moduleLoggers } from "@/utils/LogManager";

/**
 * @file GestureManager.ts
 * @description 手势管理器 - 处理上划下划切换剧集等手势操作
 */

export interface GestureManagerConfig {
  minSwipeDistance?: number;
  episodeChangeCooldown?: number;
  swipeCooldown?: number;
}

export class GestureManager {
  private touchStartY = 0;
  private touchEndY = 0;
  private touchStartX = 0;
  private touchEndX = 0;
  private minSwipeDistance: number;
  private episodeChangeCooldown: number;
  private swipeCooldown: number;

  private swipeInProgress = false;
  private episodeChangeInProgress = false;
  private episodeChangeTimeout: NodeJS.Timeout | null = null;
  private isSwipe = false;
  private touchEventsStatus = true;
  private isDestroyed = false;

  // 添加时间戳检查，防止快速连续触发
  private lastEpisodeChangeTime = 0;
  private lastTouchStartTime = 0;

  private onNextEpisode?: () => void;
  private onPrevEpisode?: () => void;

  // 修复事件监听器绑定问题：使用箭头函数或绑定后的函数引用
  private boundHandleTouchStart: (event: TouchEvent) => void;
  private boundHandleTouchMove: (event: TouchEvent) => void;
  private boundHandleTouchEnd: (event: TouchEvent) => void;
  private boundHandleClick: (event: Event) => void;

  constructor(config: GestureManagerConfig = {}) {
    this.minSwipeDistance = config.minSwipeDistance || 50;
    this.episodeChangeCooldown = config.episodeChangeCooldown || 1000;
    this.swipeCooldown = config.swipeCooldown || 500;

    // 预先绑定事件处理函数，避免重复绑定问题
    this.boundHandleTouchStart = this.handleTouchStart.bind(this);
    this.boundHandleTouchMove = this.handleTouchMove.bind(this);
    this.boundHandleTouchEnd = this.handleTouchEnd.bind(this);
    this.boundHandleClick = this.handleClick.bind(this);
  }

  /**
   * 全局禁用手势（静态方法）
   * 用于在页面有蒙层弹出时禁用所有手势操作
   */
  public static disableGlobalGesture(): void {
    window.__BS_IS_GESTURE_DISABLED__ = true;
    moduleLoggers.GestureManager.info("全局手势已禁用");
  }

  /**
   * 全局启用手势（静态方法）
   * 用于在蒙层关闭后重新启用手势操作
   */
  public static enableGlobalGesture(): void {
    window.__BS_IS_GESTURE_DISABLED__ = false;
    moduleLoggers.GestureManager.info("全局手势已启用");
  }

  /**
   * 获取全局手势状态（静态方法）
   */
  public static isGlobalGestureDisabled(): boolean {
    return window.__BS_IS_GESTURE_DISABLED__ || false;
  }

  /**
   * 初始化手势管理器
   */
  public init(onNextEpisode?: () => void, onPrevEpisode?: () => void): void {
    // 先销毁旧的事件监听器，避免重复绑定
    this.destroyEventListeners();

    this.onNextEpisode = onNextEpisode;
    this.onPrevEpisode = onPrevEpisode;
    this.initEventListeners();
    moduleLoggers.GestureManager.info("手势管理器已初始化");
  }

  /**
   * 设置触摸事件状态
   */
  public setTouchEventsStatus(status: boolean): void {
    this.touchEventsStatus = status;
  }

  /**
   * 禁用当前实例的手势
   */
  public disableGesture(): void {
    this.touchEventsStatus = false;
    moduleLoggers.GestureManager.info("当前实例手势已禁用");
  }

  /**
   * 启用当前实例的手势
   */
  public enableGesture(): void {
    this.touchEventsStatus = true;
    moduleLoggers.GestureManager.info("当前实例手势已启用");
  }

  /**
   * 检查手势是否可用
   */
  private isGestureEnabled(): boolean {
    return (
      !this.isDestroyed &&
      this.touchEventsStatus &&
      !GestureManager.isGlobalGestureDisabled()
    );
  }

  /**
   * 初始化事件监听器
   */
  private initEventListeners(): void {
    document.addEventListener("touchstart", this.boundHandleTouchStart, {
      passive: false,
    });
    document.addEventListener("touchmove", this.boundHandleTouchMove, {
      passive: false,
    });
    document.addEventListener("touchend", this.boundHandleTouchEnd, {
      passive: false,
    });
    document.addEventListener("click", this.boundHandleClick, {
      capture: true,
    });
  }

  /**
   * 销毁事件监听器
   */
  private destroyEventListeners(): void {
    document.removeEventListener("touchstart", this.boundHandleTouchStart);
    document.removeEventListener("touchmove", this.boundHandleTouchMove);
    document.removeEventListener("touchend", this.boundHandleTouchEnd);
    document.removeEventListener("click", this.boundHandleClick, {
      capture: true,
    });
  }

  /**
   * 重置触摸状态
   */
  private resetTouchState(): void {
    this.touchStartY = 0;
    this.touchEndY = 0;
    this.touchStartX = 0;
    this.touchEndX = 0;
    this.isSwipe = false;
  }

  /**
   * 处理触摸开始事件
   */
  private handleTouchStart(event: TouchEvent): void {
    if (this.isDestroyed) {
      moduleLoggers.GestureManager.info("手势管理器已销毁，跳过处理");
      return;
    }

    if (!this.isGestureEnabled()) {
      moduleLoggers.GestureManager.info("手势已禁用，跳过处理");
      return;
    }

    // 如果正在调整音量，则不处理触摸开始事件
    if (window.__BS_IS_ADJUSTING_VOLUME__ === true) {
      moduleLoggers.GestureManager.info("正在调整音量，跳过手势处理");
      return;
    }

    // 如果正在剧集切换中，跳过新的触摸事件
    if (this.episodeChangeInProgress) {
      moduleLoggers.GestureManager.info("剧集切换进行中，跳过新的触摸事件");
      return;
    }

    // 检查时间间隔，防止快速连续触摸
    const now = Date.now();
    if (now - this.lastTouchStartTime < 300) {
      moduleLoggers.GestureManager.info("触摸间隔过短，跳过处理");
      return;
    }
    this.lastTouchStartTime = now;

    // 重置触摸状态
    this.resetTouchState();

    this.touchStartY = event.touches[0].clientY;
    this.touchStartX = event.touches[0].clientX;

    moduleLoggers.GestureManager.info(
      `触摸开始: Y=${this.touchStartY}, X=${this.touchStartX}`,
    );
  }

  /**
   * 处理触摸移动事件
   */
  private handleTouchMove(event: TouchEvent): void {
    if (this.isDestroyed) {
      return;
    }

    if (!this.isGestureEnabled()) return;

    // 如果正在调整音量，则完全跳过触摸处理
    if (window.__BS_IS_ADJUSTING_VOLUME__ === true) {
      event.stopPropagation();
      event.preventDefault();
      return;
    }

    // 如果触摸位置未初始化，跳过处理
    if (this.touchStartY === 0 || this.touchStartX === 0) {
      return;
    }

    if (event.touches.length === 0) return;

    this.touchEndY = event.touches[0].clientY;
    this.touchEndX = event.touches[0].clientX;

    const verticalDistance = this.touchEndY - this.touchStartY;
    const horizontalDistance = this.touchEndX - this.touchStartX;

    // 判断是否为垂直滑动
    if (
      Math.abs(verticalDistance) > Math.abs(horizontalDistance) &&
      Math.abs(verticalDistance) > 20
    ) {
      this.isSwipe = true; // 标记为滑动交互

      // 仅对垂直滑动阻止默认行为，允许水平滚动
      if (!this.swipeInProgress) {
        event.preventDefault();
      }
    }
  }

  /**
   * 处理触摸结束事件
   */
  private handleTouchEnd(event: TouchEvent): void {
    if (this.isDestroyed) {
      return;
    }

    if (!this.isGestureEnabled()) return;

    // 如果正在调整音量，则完全跳过触摸结束处理
    if (window.__BS_IS_ADJUSTING_VOLUME__ === true) {
      event.stopPropagation();
      event.preventDefault();
      return;
    }

    // 如果触摸位置未初始化，跳过处理
    if (this.touchStartY === 0 || this.touchStartX === 0) {
      moduleLoggers.GestureManager.info("触摸位置未初始化，跳过处理");
      return;
    }

    if (this.swipeInProgress) {
      moduleLoggers.GestureManager.info("滑动进行中，跳过处理");
      return; // 防止多次滑动
    }

    const swipeDistance = this.touchEndY - this.touchStartY;
    moduleLoggers.GestureManager.info(
      `触摸结束: 滑动距离=${swipeDistance}, 是否滑动=${this.isSwipe}, 最小距离=${this.minSwipeDistance}`,
    );

    // 检查滑动距离是否足够且被标记为滑动
    if (this.isSwipe && Math.abs(swipeDistance) >= this.minSwipeDistance) {
      // 检查剧集切换时间间隔
      const now = Date.now();
      if (now - this.lastEpisodeChangeTime < this.episodeChangeCooldown) {
        moduleLoggers.GestureManager.info("剧集切换间隔过短，跳过处理");
        this.resetTouchState();
        return;
      }

      this.swipeInProgress = true;
      moduleLoggers.GestureManager.info(`滑动距离足够，开始处理剧集切换`);

      // 只有在没有剧集切换进行中时才继续
      if (!this.episodeChangeInProgress) {
        this.episodeChangeInProgress = true;
        this.lastEpisodeChangeTime = now;
        moduleLoggers.GestureManager.info(`开始剧集切换`);

        // 清除任何现有的超时
        if (this.episodeChangeTimeout) {
          clearTimeout(this.episodeChangeTimeout);
        }

        // 保存滑动方向，避免在异步操作中状态被修改
        const isDownwardSwipe = swipeDistance > 0;

        if (isDownwardSwipe) {
          // 向下滑动 - 切换到上一集
          moduleLoggers.GestureManager.info("检测到向下滑动，切换到上一集");
          this.onPrevEpisode?.();
        } else {
          // 向上滑动 - 切换到下一集
          moduleLoggers.GestureManager.info("检测到向上滑动，切换到下一集");
          this.onNextEpisode?.();
        }

        // 设置超时以重置剧集切换标志
        this.episodeChangeTimeout = setTimeout(() => {
          this.episodeChangeInProgress = false;
          moduleLoggers.GestureManager.info(`剧集切换冷却结束`);
        }, this.episodeChangeCooldown);
      } else {
        moduleLoggers.GestureManager.info(`剧集切换进行中，跳过`);
      }

      // 短暂延迟后重置滑动状态
      setTimeout(() => {
        this.swipeInProgress = false;
        moduleLoggers.GestureManager.info(`滑动状态重置`);
      }, this.swipeCooldown);

      event.preventDefault(); // 防止滑动后的点击事件
    } else {
      moduleLoggers.GestureManager.info(
        `滑动条件不满足: 距离=${Math.abs(swipeDistance)}, 最小距离=${this.minSwipeDistance}, 是否滑动=${this.isSwipe}`,
      );
    }

    // 重置触摸位置
    this.resetTouchState();
  }

  /**
   * 处理点击事件 - 如果检测到滑动则阻止
   */
  private handleClick(event: Event): void {
    if (this.isDestroyed) {
      return;
    }

    if (this.isSwipe) {
      event.stopPropagation();
      event.preventDefault();

      // 短暂延迟后重置
      setTimeout(() => {
        this.isSwipe = false;
      }, 100);
    }
  }

  /**
   * 销毁手势管理器
   */
  public destroy(): void {
    // 设置销毁标志
    this.isDestroyed = true;

    // 移除事件监听器
    this.destroyEventListeners();

    // 清除任何待处理的超时
    if (this.episodeChangeTimeout) {
      clearTimeout(this.episodeChangeTimeout);
    }

    // 重置所有状态
    this.resetTouchState();
    this.swipeInProgress = false;
    this.episodeChangeInProgress = false;
    this.lastEpisodeChangeTime = 0;
    this.lastTouchStartTime = 0;

    moduleLoggers.GestureManager.info("手势管理器已销毁");
  }
}
