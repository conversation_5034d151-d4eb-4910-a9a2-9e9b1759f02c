import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

import { PlayerInstance } from "../instance/PlayerInstance";
import { EventManager } from "../managers/EventManager";
import { StateManager } from "../managers/StateManager";

export interface PlayerConfig {
  id: string;
  containerId: string;
  videoData?: IPlayerVideoData;
  autoPlay?: boolean;
  preload?: boolean;
  startTime?: number;
  onEnded?: (playerId: string, currentEpisodeNo: number) => void;
  currentEpisode?: IPlayerEpisode;
}

export class PlayerFactory {
  private static instance: PlayerFactory;
  private stateManager: StateManager;
  private instances: Map<string, PlayerInstance>;
  private eventManager: EventManager;

  private constructor(eventManager: EventManager, stateManager: StateManager) {
    this.stateManager = stateManager;
    this.instances = new Map();
    this.eventManager = eventManager;
  }

  public static getInstance(
    eventManager?: EventManager,
    stateManager?: StateManager,
  ): PlayerFactory {
    if (!PlayerFactory.instance) {
      if (!eventManager || !stateManager) {
        throw new Error(
          "PlayerFactory 单例尚未初始化，必须传入 eventManager 和 stateManager",
        );
      }
      PlayerFactory.instance = new PlayerFactory(eventManager, stateManager);
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_PLAYER_FACTORY_CLASS__ = PlayerFactory;
      }
    }
    return PlayerFactory.instance;
  }

  /**
   * 创建新的播放器实例
   */
  public createPlayer(config: PlayerConfig): PlayerInstance {
    const {
      id,
      containerId,
      videoData,
      autoPlay = false,
      preload = false,
      startTime = 0,
      onEnded,
      currentEpisode,
    } = config;

    // 检查是否已存在
    if (this.instances.has(id)) {
      const existingInstance = this.instances.get(id)!;

      // 检查现有实例是否正常工作
      if (existingInstance.xgplayer) {
        moduleLoggers.PlayerFactory.info(`播放器实例已存在且正常工作: ${id}`);

        // 如果传入了新的 startTime，需要更新现有实例
        if (existingInstance.config.startTime !== startTime) {
          moduleLoggers.PlayerFactory.info(
            `更新现有实例的 startTime: ${startTime}s`,
          );
          // 更新配置中的 startTime
          existingInstance.config.startTime = startTime;
          // 重新设置实例的 startTime
          existingInstance.startTime = startTime;
          existingInstance.isFirstInitialization = true; // 重置为第一次初始化状态
        }

        return existingInstance;
      } else {
        // 如果现有实例有问题，先销毁再重新创建
        moduleLoggers.PlayerFactory.warn(
          `播放器实例存在但有问题，重新创建: ${id}`,
        );
        this.destroyPlayer(id);
      }
    }

    // 确保DOM容器存在
    const containerElement = document.getElementById(containerId);
    if (!containerElement) {
      moduleLoggers.PlayerFactory.error(`DOM容器不存在: ${containerId}`);
      throw new Error(`DOM容器不存在: ${containerId}`);
    }

    // 在状态管理器中创建实例状态
    this.stateManager.createInstance(id, containerId, videoData);

    // 如果是第一个实例（没有活跃实例），立即设置为活跃实例
    if (!this.stateManager.getActiveInstance()) {
      moduleLoggers.PlayerFactory.info(`第一个实例，立即设置为活跃实例: ${id}`);
      this.stateManager.setActiveInstance(id);
    }

    // 创建播放器实例
    const playerInstance = new PlayerInstance({
      id,
      containerId,
      autoPlay,
      preload,
      startTime,
      onEnded,
      eventManager: this.eventManager,
    });

    // 如果有视频数据，立即加载（异步）
    if (videoData) {
      playerInstance.loadVideoData(videoData, currentEpisode);
    }

    this.instances.set(id, playerInstance);
    this.stateManager.setPlayer(id, playerInstance);

    moduleLoggers.PlayerFactory.info(
      `创建播放器实例: ${id}, startTime: ${startTime}`,
    );
    return playerInstance;
  }

  /**
   * 销毁播放器实例
   */
  public destroyPlayer(id: string): void {
    const instance = this.instances.get(id);
    if (instance) {
      instance.destroy();
      this.instances.delete(id);
      this.stateManager.removeInstance(id);
      this.stateManager.removePlayer(id);
      moduleLoggers.PlayerFactory.info(`销毁播放器实例: ${id}`);
    }
  }

  /**
   * 获取播放器实例
   */
  public getPlayer(id: string): PlayerInstance | undefined {
    const instance = this.instances.get(id);
    return instance;
  }

  /**
   * 获取所有播放器实例
   */
  public getAllPlayers(): PlayerInstance[] {
    return Array.from(this.instances.values());
  }

  /**
   * 批量创建播放器实例（用于预加载）
   */
  public createMultiplePlayers(configs: PlayerConfig[]): PlayerInstance[] {
    return configs.map((config) => this.createPlayer(config));
  }

  /**
   * 清理所有播放器实例
   */
  public destroyAllPlayers(): void {
    const instanceIds = Array.from(this.instances.keys());
    moduleLoggers.PlayerFactory.info(
      `清理所有播放器实例，共 ${instanceIds.length} 个`,
    );
    moduleLoggers.PlayerFactory.info(`实例列表: ${instanceIds.join(", ")}`);

    for (const [id] of this.instances) {
      moduleLoggers.PlayerFactory.info(`销毁播放器实例: ${id}`);
      this.destroyPlayer(id);
    }

    moduleLoggers.PlayerFactory.info(
      `清理完成，剩余实例数量: ${this.instances.size}`,
    );
  }

  /**
   * 获取实例统计信息
   */
  public getStats(): {
    total: number;
    active: number;
    idle: number;
    loading: number;
  } {
    const instances = this.stateManager.getAllInstances();
    const stats = {
      total: instances.length,
      active: instances.filter((i) => i.isActive).length,
      idle: instances.filter((i) => i.status === "idle").length,
      loading: instances.filter((i) => i.status === "loading").length,
    };

    return stats;
  }

  /**
   * 重置 PlayerFactory 状态
   */
  public reset(): void {
    moduleLoggers.PlayerFactory.info("重置 PlayerFactory 状态");
    moduleLoggers.PlayerFactory.info(`重置前实例数量: ${this.instances.size}`);
    this.destroyAllPlayers();
    moduleLoggers.PlayerFactory.info(`重置后实例数量: ${this.instances.size}`);
    moduleLoggers.PlayerFactory.info("PlayerFactory 状态重置完成");
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.PlayerFactory.info("开始重置全局 PlayerFactory 实例");

    if (PlayerFactory.instance) {
      moduleLoggers.PlayerFactory.info("找到现有实例，开始重置");
      PlayerFactory.instance.reset();
      PlayerFactory.instance = null as any;
      moduleLoggers.PlayerFactory.info("实例已设置为 null");
    } else {
      moduleLoggers.PlayerFactory.warn("没有找到现有实例");
    }

    moduleLoggers.PlayerFactory.info("全局 PlayerFactory 实例重置完成");
  }
}
