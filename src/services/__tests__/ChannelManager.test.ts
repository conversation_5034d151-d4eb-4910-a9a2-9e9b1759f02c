import { ChannelManager, Channels } from "../ChannelManager";

/**
 * @file ChannelManager.test.ts
 * @description ChannelManager 的单元测试
 * 测试渠道管理器的缓存机制和功能
 */

// Mock userAgentDetection
jest.mock("@/utils/userAgentDetection", () => ({
  isFacebookBrowser: jest.fn(),
}));

// Mock LogManager
jest.mock("@/utils/LogManager", () => ({
  moduleLoggers: {
    AuthManager: {
      info: jest.fn(),
      error: jest.fn(),
    },
  },
}));

import { isFacebookBrowser } from "@/utils/userAgentDetection";

describe("ChannelManager", () => {
  let channelManager: ChannelManager;
  const mockIsFacebookBrowser = isFacebookBrowser as jest.MockedFunction<typeof isFacebookBrowser>;

  beforeEach(() => {
    // 清除单例实例
    (ChannelManager as any).instance = null;
    channelManager = ChannelManager.getInstance();
    channelManager.clearCache();
    jest.clearAllMocks();
  });

  describe("getCurrentChannel", () => {
    it("should detect Facebook channel when in Facebook browser", () => {
      mockIsFacebookBrowser.mockReturnValue(true);
      
      const channel = channelManager.getCurrentChannel();
      
      expect(channel).toBe(Channels.FACEBOOK);
      expect(mockIsFacebookBrowser).toHaveBeenCalledTimes(1);
    });

    it("should detect default channel when not in Facebook browser", () => {
      mockIsFacebookBrowser.mockReturnValue(false);
      
      const channel = channelManager.getCurrentChannel();
      
      expect(channel).toBe(Channels.DEFAULT);
      expect(mockIsFacebookBrowser).toHaveBeenCalledTimes(1);
    });

    it("should cache channel detection result", () => {
      mockIsFacebookBrowser.mockReturnValue(true);
      
      // 第一次调用
      const channel1 = channelManager.getCurrentChannel();
      // 第二次调用
      const channel2 = channelManager.getCurrentChannel();
      
      expect(channel1).toBe(Channels.FACEBOOK);
      expect(channel2).toBe(Channels.FACEBOOK);
      // 只应该调用一次检测函数
      expect(mockIsFacebookBrowser).toHaveBeenCalledTimes(1);
    });
  });

  describe("shouldUseChannelStrategy", () => {
    it("should return true for debugging purposes", () => {
      // 当前是调试模式，总是返回 true
      const result = channelManager.shouldUseChannelStrategy();
      expect(result).toBe(true);
    });
  });

  describe("getChannelStrategy", () => {
    it("should return null when channel strategy is not needed", () => {
      // 修改 shouldUseChannelStrategy 的实现来测试这个场景
      jest.spyOn(channelManager, "shouldUseChannelStrategy").mockReturnValue(false);
      
      const strategy = channelManager.getChannelStrategy();
      
      expect(strategy).resolves.toBeNull();
    });

    it("should fetch and cache Facebook channel strategy", async () => {
      mockIsFacebookBrowser.mockReturnValue(true);
      
      const strategy = await channelManager.getChannelStrategy();
      
      expect(strategy).toBeDefined();
      expect(strategy?.channel).toBe(Channels.FACEBOOK);
      expect(strategy?.freeUntil).toBe(5);
      expect(strategy?.img).toBeDefined();
    });

    it("should return cached strategy on subsequent calls (permanent cache)", async () => {
      mockIsFacebookBrowser.mockReturnValue(true);

      // 第一次调用
      const strategy1 = await channelManager.getChannelStrategy();
      // 第二次调用
      const strategy2 = await channelManager.getChannelStrategy();

      expect(strategy1).toEqual(strategy2);
      // 验证缓存状态
      const cacheStatus = channelManager.getCacheStatus();
      expect(cacheStatus.cachedChannels).toContain(Channels.FACEBOOK);
    });

    it("should fetch default channel strategy", async () => {
      mockIsFacebookBrowser.mockReturnValue(false);
      
      const strategy = await channelManager.getChannelStrategy();
      
      expect(strategy).toBeDefined();
      expect(strategy?.channel).toBe(Channels.DEFAULT);
      expect(strategy?.freeUntil).toBe(0);
    });
  });

  describe("checkEpisodePlayable", () => {
    it("should return true when no strategy is provided", () => {
      const result = channelManager.checkEpisodePlayable(1);
      expect(result).toBe(true);
    });

    it("should return false for debugging purposes when strategy is provided", () => {
      const mockStrategy = {
        channel: Channels.FACEBOOK,
        freeUntil: 5,
        img: "test.jpg",
      };
      
      const result = channelManager.checkEpisodePlayable(1, mockStrategy);
      // 当前调试模式下总是返回 false
      expect(result).toBe(false);
    });
  });

  describe("cache management", () => {
    it("should clear cache correctly", async () => {
      mockIsFacebookBrowser.mockReturnValue(true);
      
      // 先获取策略以填充缓存
      await channelManager.getChannelStrategy();
      
      let cacheStatus = channelManager.getCacheStatus();
      expect(cacheStatus.cachedChannels.length).toBeGreaterThan(0);
      
      // 清除缓存
      channelManager.clearCache();
      
      cacheStatus = channelManager.getCacheStatus();
      expect(cacheStatus.cachedChannels.length).toBe(0);
      expect(cacheStatus.channel).toBeNull();
    });

    it("should provide cache status information", async () => {
      mockIsFacebookBrowser.mockReturnValue(true);
      
      // 获取当前渠道
      channelManager.getCurrentChannel();
      // 获取策略
      await channelManager.getChannelStrategy();
      
      const cacheStatus = channelManager.getCacheStatus();
      
      expect(cacheStatus.channel).toBe(Channels.FACEBOOK);
      expect(cacheStatus.cachedChannels).toContain(Channels.FACEBOOK);
    });
  });

  describe("singleton pattern", () => {
    it("should return the same instance", () => {
      const instance1 = ChannelManager.getInstance();
      const instance2 = ChannelManager.getInstance();
      
      expect(instance1).toBe(instance2);
    });
  });
});
