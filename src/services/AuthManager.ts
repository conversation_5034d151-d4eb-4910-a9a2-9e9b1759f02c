import { moduleLoggers } from "@/utils/LogManager";
import { isFacebookBrowser } from "@/utils/userAgentDetection";

/**
 * @file AuthManager.ts
 * @description 权限管理器 (AM)
 * - 处理与视频播放相关的用户认证和授权。
 * - 例如，付费视频的播放权限检查。
 */

interface IOperate {
  is_collect: boolean;
  is_like: boolean;
  is_subscribe: boolean;
}

interface IMetaField {
  key: string;
  type: string;
  value: string;
}

export interface ICover {
  src: string;
  alt: string;
  width: number;
  height: number;
}

interface ISeriesInfo {
  skit_series_id: string;
  play_duration: string;
  video_url: string;
  operate: IOperate;
  skit_series_meta_fields: IMetaField[];
  episodes_number: string;
  url: string;
  is_last_play: boolean;
}

export interface ISubtitle {
  id: string;
  language: string;
  label: string;
  url: string;
  isDefault: boolean;
}

export interface IVideoDetail {
  skit_id: string;
  skit_title: string;
  skit_describe: string;
  skit_tags: string;
  operate: IOperate;
  skit_meta_fields: IMetaField[];
  skit_series_info: ISeriesInfo[];
  cover: ICover;
  url: string;
  id: string;
  view_count: string;
}

interface IVideoDetailResponse {
  skit_info: IVideoDetail;
}

// Types for the structured data returned by getVideoDetail
export interface IPlayerOperate {
  liked: boolean;
  collected: boolean;
  subscribed: boolean;
}

export type EpisodeStatus = "free" | "paid" | "locked" | "channel_blocked";

export interface IPlayerEpisode {
  id: string;
  no: number;
  currentTime: string;
  url: string;
  is_last_play: boolean;
  play_duration: string;
  subTitles: ISubtitle[];
  operate: IPlayerOperate;
  status: EpisodeStatus;
  metaFields: { [key: string]: string };
}

export interface IPlayerVideoData {
  id: string;
  title: string;
  description: string;
  tags: string[];
  cover: ICover;
  operate: IPlayerOperate;
  metaFields: { [key: string]: string };
  series: IPlayerEpisode[];
  strategy?: IChannelStrategyConfig;
}

export enum Channels {
  FACEBOOK = "facebook",
  DEFAULT = "default",
}

interface IChannelStrategyConfig {
  channel: Channels;
  freeUntil: number;
  img: string;
}

const MOCK_CHANNEL_CONFIG: IChannelStrategyConfig = {
  channel: Channels.FACEBOOK,
  freeUntil: 5,
  img: "https://img.staticdj.com/40a8c3c08de8e00c485cb7459f765af5.jpg",
};

export class AuthManager {
  private static instance: AuthManager;

  private constructor() {}

  public static getInstance(): AuthManager {
    if (!AuthManager.instance) {
      AuthManager.instance = new AuthManager();
    }
    return AuthManager.instance;
  }

  private resizeImage(src: string) {
    const url = src;
    const extIndex = url.lastIndexOf(".");
    const ext = url.substring(extIndex + 1);
    const filePath = url.substring(0, extIndex);

    // 标准尺寸列表
    const SIZE_SPLITERS = [
      48, 180, 420, 540, 720, 900, 1024, 1280, 1366, 1440, 1536, 1600, 1920,
      2056, 2560, 2732, 2880, 3072, 3200, 3840,
    ];

    // 计算合适的裁切尺寸 - 基于设备显示尺寸
    const devicePixelRatio = window.devicePixelRatio || 1;
    const viewportWidth =
      window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight =
      window.innerHeight || document.documentElement.clientHeight;
    const maxViewportDimension = Math.max(viewportWidth, viewportHeight);
    const calculatedSize = ((devicePixelRatio + 1) / 2) * maxViewportDimension;

    // 选择大于计算值的最小标准尺寸
    const autofit =
      SIZE_SPLITERS.find((size) => size > calculatedSize) ||
      SIZE_SPLITERS[SIZE_SPLITERS.length - 1];

    return `${filePath}_${autofit}x.${ext}`;
  }

  private getChannel() {
    const isFacebook = isFacebookBrowser();

    if (isFacebook) {
      return Channels.FACEBOOK;
    }

    return Channels.DEFAULT;
  }

  private checkChannelPlayable(
    config: IChannelStrategyConfig,
    episodeNo: number,
  ) {
    // FIXME: 临时调试
    return false;
    const isEpisodeShouldCheck = episodeNo > config.freeUntil;

    if (!isEpisodeShouldCheck) {
      return true;
    }

    let episodeAllowed = true;

    switch (config.channel) {
      case Channels.FACEBOOK:
        episodeAllowed = !isFacebookBrowser();
        break;
      case Channels.DEFAULT:
        episodeAllowed = true;
        break;
    }

    return episodeAllowed;
  }

  private async getChannelStrategy() {
    const channel = this.getChannel();

    // TODO: API 对接
    // await fetch("/apps/best-short/api/v1/channel_browser")
    //   .then((res) => res.json())
    //   .then((data) => {
    //     return data;
    //   });

    return {
      ...MOCK_CHANNEL_CONFIG,
      img: this.resizeImage(MOCK_CHANNEL_CONFIG.img),
    };
  }

  private shouldGetChannelStrategy() {
    // FIXME: 临时调试
    return true;
    // return this.getChannel() !== Channels.DEFAULT;
  }

  async getVideoDetail(videoId: string): Promise<IPlayerVideoData> {
    let strategy: undefined | IChannelStrategyConfig;
    if (this.shouldGetChannelStrategy()) {
      strategy = await this.getChannelStrategy();
    }
    const videoData = await fetch(
      `/apps/best-short/api/v1/preview/skit?product_id=${videoId}`,
    )
      .then((res) => res.json())
      .then((data: IVideoDetailResponse) => {
        moduleLoggers.AuthManager.info("getVideoDetail parsing data", data);

        const structuredData: IPlayerVideoData = {
          id: data.skit_info.skit_id,
          title: data.skit_info.skit_title,
          description: data.skit_info.skit_describe,
          tags: data.skit_info.skit_tags.split(","),
          cover: {
            ...data.skit_info.cover,
            src: this.resizeImage(data.skit_info.cover.src),
          },
          operate: {
            liked: data.skit_info.operate.is_like,
            collected: data.skit_info.operate.is_collect,
            subscribed: data.skit_info.operate.is_subscribe,
          },
          metaFields: data.skit_info.skit_meta_fields.reduce(
            (acc: { [key: string]: string }, item: IMetaField) => {
              acc[item.key] = item.value;
              return acc;
            },
            {},
          ),
          series: data.skit_info.skit_series_info.map(
            (series: ISeriesInfo, index: number): IPlayerEpisode => {
              let isPlayable = !!series.video_url;
              let videoUrl = series.video_url;
              const no = index + 1;

              const metaFields = series.skit_series_meta_fields.reduce(
                (acc: { [key: string]: string }, item: IMetaField) => {
                  acc[item.key] = item.value;
                  return acc;
                },
                {},
              );

              let status: EpisodeStatus = "free";

              if (metaFields.Chargeable === "Y") {
                if (isPlayable) {
                  status = "paid";
                } else {
                  status = "locked";
                }
              }

              // 判断是否渠道内允许播放
              if (status !== "locked") {
                console.log("Touch videoData locked", status);
                const isChannelBlocked = !this.checkChannelPlayable(
                  MOCK_CHANNEL_CONFIG,
                  no,
                );
                if (isChannelBlocked) {
                  status = "channel_blocked";
                  isPlayable = false;
                  videoUrl = "";
                }
              }

              const subTitles: ISubtitle[] = [];

              // TODO: "CaptionUrl" key 值可能会变动
              if (metaFields.CaptionUrl) {
                const captionList = metaFields.CaptionUrl.split(";")[0];
                captionList.split("\n").forEach((srt, srtIdx) => {
                  const [lang, url] = srt.split(",");
                  if (lang && url) {
                    subTitles.push({
                      id: lang,
                      language: lang,
                      label: lang,
                      url,
                      isDefault: srtIdx === 0,
                    });
                  }
                });
              }

              return {
                id: series.skit_series_id,
                no,
                currentTime: series.play_duration,
                url: videoUrl,
                is_last_play: series.is_last_play,
                play_duration: series.play_duration,
                subTitles,
                operate: {
                  liked: series.operate.is_like,
                  // 收藏为全剧维度
                  collected: data.skit_info.operate.is_collect,
                  // collected: series.operate.is_collect,
                  subscribed: series.operate.is_subscribe,
                },
                status,
                metaFields,
              };
            },
          ),
        };
        return structuredData;
      });

    if (strategy) {
      videoData.strategy = strategy;
    }

    console.log("Touch videoData:>", videoData);

    return videoData;
  }
}
