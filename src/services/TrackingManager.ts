/**
 * @file TrackingManager.ts
 * @description 埋点管理器 (TM)
 * - 负责收集和发送用户行为数据和播放器性能数据。
 * - 用于数据分析和业务监控。
 */
import { StateManager } from "@/core/managers";
import { IPlayerEpisode, IPlayerVideoData } from "@/services/AuthManager";
import { moduleLoggers } from "@/utils/LogManager";

type TrackingEvent = {
  name: string;
  params: Record<string, any>;
};

// 30秒播放事件追踪状态
interface ThirtySecondPlayState {
  hasTriggered: boolean; // 是否已触发过30秒事件
  lastTriggerTime: number; // 上次触发时间戳
  // 倍速播放相关状态
  lastPlaybackRate: number; // 上次的播放倍速
  adjustedCumulateTime: number; // 经过倍速调整的累计观看时长
  lastTimeUpdateTime: number; // 上次timeupdate事件的时间戳
  lastCurrentTime: number; // 上次的播放位置
  lastCumulateTime: number; // 上次的累计观看时长(原始值)
}

export class TrackingManager {
  private static instance: TrackingManager;

  private lastTrackInfo: Record<string, { time: number; status: number }> = {};

  // 30秒播放事件状态管理
  private thirtySecondPlayStates: Map<string, ThirtySecondPlayState> =
    new Map();

  private constructor() {
    moduleLoggers.TrackingManager.info("TrackingManager initialized");
  }

  public static getInstance(): TrackingManager {
    if (!TrackingManager.instance) {
      TrackingManager.instance = new TrackingManager();
      // 将类本身挂载到 window 上，方便重置时使用
      if (typeof window !== "undefined") {
        window.__BS_TRACKING_MANAGER_CLASS__ = TrackingManager;
      }
    }
    return TrackingManager.instance;
  }

  public track(event: TrackingEvent) {
    moduleLoggers.TrackingManager.info("Tracking event:", event);
    // 在这里实现将事件发送到分析服务的逻辑
    if (window.csTracker) {
      window.csTracker.track(event.name, event.params);
    }
  }

  public trackViewContent(product: IPlayerVideoData) {
    window.csTracker?.track(
      "viewContent",
      {
        name: product.title,
        id: product.id,
      },
      ["ga"],
    );
  }

  public trackPlay(product: IPlayerVideoData, episode: IPlayerEpisode) {
    moduleLoggers.TrackingManager.info(
      "Tracking play event:",
      product.id,
      episode.id,
    );
    window.csTracker?.track("playDrama", {
      name: product.title,
      id: product.id,
      variant: episode.no.toString(),
      variant_id: episode.id,
    });
  }

  public trackLoadComplete(
    product: IPlayerVideoData,
    episode: IPlayerEpisode,
    loadDuration: number,
  ) {
    moduleLoggers.TrackingManager.info(
      "Tracking loadComplete event:",
      product.id,
      episode.id,
    );
    window.csTracker?.track(
      "loadComplete",
      {
        name: product.title,
        id: product.id,
        variant: episode.no.toString(),
        variant_id: episode.id,
        loadTime: loadDuration,
      },
      ["ga", "fb"],
    );
  }

  public track10ExitPlay(product: IPlayerVideoData, episode: IPlayerEpisode) {
    moduleLoggers.TrackingManager.info(
      "Tracking 10 exit play event:",
      product.id,
      episode.id,
    );
    window.csTracker?.track("tenSecExitPlay", {
      name: product.title,
      id: product.id,
      variant: episode.no.toString(),
      variant_id: episode.id,
    });
  }

  /**
   * 智能30秒播放事件追踪
   * 确保只有在用户真正观看了30秒内容时才触发事件
   */
  public track30Play(product: IPlayerVideoData, episode: IPlayerEpisode) {
    moduleLoggers.TrackingManager.info(
      "Tracking 30 play event:",
      product.id,
      episode.id,
    );
    window.csTracker?.track("thirtySecFinishPlay", {
      name: product.title,
      id: product.id,
      variant: episode.no.toString(),
      variant_id: episode.id,
    });
  }

  /**
   * 检查并触发30秒播放事件（支持倍速播放）
   * 使用经过倍速调整的累计观看时长来判断是否达到30秒
   * 允许重复上报，每次切换剧集后重新开始计算
   * @param episodeId 剧集ID
   * @param cumulateTime 累计观看时长（毫秒）
   * @param currentTime 当前播放位置（秒）
   * @param playbackRate 当前播放倍速
   * @param product 视频数据
   * @param episode 剧集数据
   */
  public checkAndTriggerThirtySecondPlay(
    episodeId: string,
    cumulateTime: number,
    currentTime: number,
    playbackRate: number,
    product: IPlayerVideoData,
    episode: IPlayerEpisode,
  ): void {
    let state = this.thirtySecondPlayStates.get(episodeId);
    const now = Date.now();

    if (!state) {
      // 如果没有初始化状态，说明是新的播放会话
      this.thirtySecondPlayStates.set(episodeId, {
        hasTriggered: false,
        lastTriggerTime: 0,
        lastPlaybackRate: playbackRate,
        adjustedCumulateTime: 0,
        lastTimeUpdateTime: now,
        lastCurrentTime: currentTime,
        lastCumulateTime: cumulateTime,
      });
      state = this.thirtySecondPlayStates.get(episodeId)!;
    } else {
      // 检查倍速是否发生变化
      if (state.lastPlaybackRate !== playbackRate) {
        state.lastPlaybackRate = playbackRate;
      }

      // 计算经过倍速调整的累计观看时长
      const timeProgress = cumulateTime - state.lastCumulateTime;

      // 如果播放位置有进展，说明视频在播放
      if (timeProgress > 0) {
        // 计算实际观看时长（考虑倍速影响）
        const actualWatchTime = timeProgress * playbackRate;
        state.adjustedCumulateTime += actualWatchTime;
      }
    }

    // 更新状态
    state.lastTimeUpdateTime = now;
    state.lastCurrentTime = currentTime;
    state.lastCumulateTime = cumulateTime;

    if (state.adjustedCumulateTime >= 30 * 1000) {
      // 如果从未触发过，或者距离上次触发已经超过一定时间（比如5秒），则允许重新触发
      if (!state.hasTriggered) {
        this.track30Play(product, episode);
        state.hasTriggered = true;
        state.lastTriggerTime = now;
      }
    }
  }

  /**
   * 重置30秒播放事件追踪状态
   * 在切换剧集时调用，允许重新触发30秒事件
   * @param episodeId 剧集ID
   */
  public resetThirtySecondPlayTracking(episodeId: string): void {
    this.thirtySecondPlayStates.delete(episodeId);
    moduleLoggers.TrackingManager.info(
      `重置30秒播放追踪: episodeId=${episodeId}`,
    );
  }

  /**
   * 重置所有30秒播放事件追踪状态
   * 在切换视频或重置播放器时调用
   */
  public resetAllThirtySecondPlayTracking(): void {
    this.thirtySecondPlayStates.clear();
    moduleLoggers.TrackingManager.info("重置所有30秒播放追踪状态");
  }

  public trackFinishPlay(product: IPlayerVideoData, episode: IPlayerEpisode) {
    moduleLoggers.TrackingManager.info(
      "Tracking finish play event:",
      product.id,
      episode.id,
    );
    window.csTracker?.track("finishPlay", {
      name: product.title,
      id: product.id,
      variant: episode.no.toString(),
      variant_id: episode.id,
    });
  }

  public trackLike(
    product: {
      title: string;
      id: string;
    },
    episode: {
      no: number;
      id: string;
    },
  ) {
    window.csTracker?.track("like", {
      name: product.title,
      id: product.id,
      variant: episode.no.toString(),
      variant_id: episode.id,
    });
  }

  public trackCollect(product: { title: string; id: string }) {
    window.csTracker?.track("collect", {
      name: product.title,
      id: product.id,
    });
  }

  public trackDuration(
    product: IPlayerVideoData,
    episode: IPlayerEpisode,
    data: {
      playId: string;
      playStartAt: number;
      status: "start" | "pause" | "end";
      vDuration: number; // Video duration
      wDuration: number; // Watched duration
    },
  ) {
    const statusMap = {
      start: 0,
      pause: 1,
      end: 2,
    };
    const numericStatus = statusMap[data.status];
    const v_duration = Math.ceil(data.vDuration);
    const w_duration = Math.ceil(
      this.thirtySecondPlayStates.get(episode.id)?.adjustedCumulateTime ??
        data.wDuration,
    );

    // 防重复上报逻辑
    const currentTime = Date.now();
    const variantId = episode.id;

    if (
      this.lastTrackInfo[variantId] &&
      this.lastTrackInfo[variantId].status === 2 // 上一次是播放结束
    ) {
      const timeDiff = currentTime - this.lastTrackInfo[variantId].time;
      if (timeDiff < 1000) {
        moduleLoggers.TrackingManager.info(
          `跳过重复上报，variant_id: ${variantId}，上次状态: ${this.lastTrackInfo[variantId].status}，时间间隔: ${timeDiff}ms`,
        );
        return;
      }
    }

    // 记录本次上报信息
    this.lastTrackInfo[variantId] = {
      time: currentTime,
      status: numericStatus,
    };

    window.csTracker?.track(
      "function_duration",
      {
        event_name: "function_duration",
        event_type: "play",
        event_info: JSON.stringify({
          skit_user_id: window.csTracker.getSkitUid(),
          product_id: product.id,
          variant_id: episode.id,
          play_id: data.playId,
          play_start_at: data.playStartAt,
          play_status: numericStatus,
          v_duration,
          w_duration,
        }),
      },
      ["sa"],
    );
  }

  /**
   * 重置追踪管理器到初始状态
   */
  public reset(): void {
    moduleLoggers.TrackingManager.info("重置 TrackingManager 到初始状态");

    // 清理所有追踪状态
    this.lastTrackInfo = {};
    this.resetAllThirtySecondPlayTracking();

    moduleLoggers.TrackingManager.info("TrackingManager 重置完成");
  }

  /**
   * 静态方法：重置全局实例
   */
  public static resetGlobalInstance(): void {
    moduleLoggers.TrackingManager.info("开始重置全局 TrackingManager 实例");

    if (TrackingManager.instance) {
      TrackingManager.instance.reset();
      TrackingManager.instance = null as any;
    }

    moduleLoggers.TrackingManager.info("全局 TrackingManager 实例重置完成");
  }
}
