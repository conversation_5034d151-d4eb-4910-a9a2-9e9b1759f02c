import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TrackingManager } from './TrackingManager';
import { IPlayerVideoData, IPlayerEpisode } from './AuthManager';

// Mock window.csTracker
const mockCsTracker = {
  track: vi.fn(),
  getSkitUid: vi.fn(() => 'test-user-id'),
};

Object.defineProperty(window, 'csTracker', {
  value: mockCsTracker,
  writable: true,
});

describe('TrackingManager - 30秒播放追踪（支持重复上报）', () => {
  let trackingManager: TrackingManager;
  let mockProduct: IPlayerVideoData;
  let mockEpisode: IPlayerEpisode;

  beforeEach(() => {
    trackingManager = TrackingManager.getInstance();
    mockProduct = {
      id: 'test-video-id',
      title: '测试视频',
    } as IPlayerVideoData;
    mockEpisode = {
      id: 'test-episode-id',
      no: 1,
    } as IPlayerEpisode;
    
    // 重置所有状态
    trackingManager.resetAllThirtySecondPlayTracking();
    vi.clearAllMocks();
  });

  describe('checkAndTriggerThirtySecondPlay', () => {
    it('应该在累计观看时长达到30秒时触发事件', () => {
      // 模拟累计观看30秒（30000毫秒）
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      expect(mockCsTracker.track).toHaveBeenCalledWith(
        'thirtySecFinishPlay',
        {
          name: '测试视频',
          id: 'test-video-id',
          variant: '1',
          variant_id: 'test-episode-id',
        }
      );
    });

    it('不应该在累计观看时长不足30秒时触发事件', () => {
      // 模拟累计观看29秒（29000毫秒）
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        29000,
        mockProduct,
        mockEpisode
      );

      expect(mockCsTracker.track).not.toHaveBeenCalled();
    });

    it('应该允许重复触发30秒事件（通过重置状态）', () => {
      // 第一次触发
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      // 重置状态
      trackingManager.resetThirtySecondPlayTracking('test-episode-id');

      // 第二次触发（应该能够重新触发）
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      // 应该被调用两次
      expect(mockCsTracker.track).toHaveBeenCalledTimes(2);
    });

    it('在重置状态后应该能够重新触发事件', () => {
      // 第一次触发
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      // 重置状态
      trackingManager.resetThirtySecondPlayTracking('test-episode-id');

      // 第二次触发（应该能够重新触发）
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      // 应该被调用两次
      expect(mockCsTracker.track).toHaveBeenCalledTimes(2);
    });

    it('应该自动初始化新的剧集状态', () => {
      // 直接调用检查方法，没有预先初始化
      trackingManager.checkAndTriggerThirtySecondPlay(
        'new-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );

      // 应该能够正常触发事件
      expect(mockCsTracker.track).toHaveBeenCalledWith(
        'thirtySecFinishPlay',
        {
          name: '测试视频',
          id: 'test-video-id',
          variant: '1',
          variant_id: 'test-episode-id',
        }
      );
    });

    it('不同剧集应该有独立的状态', () => {
      // 第一个剧集触发
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-1',
        30000,
        mockProduct,
        mockEpisode
      );

      // 第二个剧集触发
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-2',
        30000,
        mockProduct,
        mockEpisode
      );

      // 两个剧集都应该被触发
      expect(mockCsTracker.track).toHaveBeenCalledTimes(2);
    });
  });

  describe('resetAllThirtySecondPlayTracking', () => {
    it('应该重置所有剧集的状态', () => {
      // 触发多个剧集的30秒事件
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-1',
        30000,
        mockProduct,
        mockEpisode
      );
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-2',
        30000,
        mockProduct,
        mockEpisode
      );

      // 重置所有状态
      trackingManager.resetAllThirtySecondPlayTracking();

      // 再次触发，应该能够重新触发
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-1',
        30000,
        mockProduct,
        mockEpisode
      );
      trackingManager.checkAndTriggerThirtySecondPlay(
        'episode-2',
        30000,
        mockProduct,
        mockEpisode
      );

      // 应该被调用4次（每个剧集2次）
      expect(mockCsTracker.track).toHaveBeenCalledTimes(4);
    });
  });

  describe('边界情况', () => {
    it('应该处理精确的30秒边界', () => {
      // 29.999秒
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        29999,
        mockProduct,
        mockEpisode
      );
      expect(mockCsTracker.track).not.toHaveBeenCalled();

      // 30.000秒
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        30000,
        mockProduct,
        mockEpisode
      );
      expect(mockCsTracker.track).toHaveBeenCalledTimes(1);
    });

    it('应该处理零和负数时长', () => {
      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        0,
        mockProduct,
        mockEpisode
      );
      expect(mockCsTracker.track).not.toHaveBeenCalled();

      trackingManager.checkAndTriggerThirtySecondPlay(
        'test-episode-id',
        -1000,
        mockProduct,
        mockEpisode
      );
      expect(mockCsTracker.track).not.toHaveBeenCalled();
    });
  });
}); 