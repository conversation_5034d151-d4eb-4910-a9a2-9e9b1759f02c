import { moduleLoggers } from "@/utils/LogManager";
import { isFacebookBrowser } from "@/utils/userAgentDetection";

/**
 * @file ChannelManager.ts
 * @description 渠道管理器 - 统一管理渠道策略和缓存
 * - 提供渠道检测和策略获取的统一接口
 * - 实现渠道策略的缓存机制
 * - 简化渠道相关的调用流程
 */

export enum Channels {
  FACEBOOK = "facebook",
  DEFAULT = "default",
}

export interface IChannelStrategyConfig {
  channel: Channels;
  freeUntil: number;
  img: string;
}

export type EpisodeStatus = "free" | "paid" | "locked" | "channel_blocked";

interface IChannelDetector {
  detect(): Channels;
}

interface IChannelStrategyProvider {
  getStrategy(channel: Channels): Promise<IChannelStrategyConfig>;
}

interface IChannelPlayabilityChecker {
  checkPlayable(config: IChannelStrategyConfig, episodeNo: number): boolean;
}

/**
 * Facebook 渠道检测器
 */
class FacebookChannelDetector implements IChannelDetector {
  detect(): Channels {
    return isFacebookBrowser() ? Channels.FACEBOOK : Channels.DEFAULT;
  }
}

/**
 * 渠道策略提供者 - 负责获取渠道配置
 */
class ChannelStrategyProvider implements IChannelStrategyProvider {
  private resizeImage(src: string): string {
    const url = src;
    const extIndex = url.lastIndexOf(".");
    const ext = url.substring(extIndex + 1);
    const filePath = url.substring(0, extIndex);

    // 标准尺寸列表
    const SIZE_SPLITERS = [
      48, 180, 420, 540, 720, 900, 1024, 1280, 1366, 1440, 1536, 1600, 1920,
      2056, 2560, 2732, 2880, 3072, 3200, 3840,
    ];

    // 计算合适的裁切尺寸 - 基于设备显示尺寸
    const devicePixelRatio = window.devicePixelRatio || 1;
    const viewportWidth =
      window.innerWidth || document.documentElement.clientWidth;
    const viewportHeight =
      window.innerHeight || document.documentElement.clientHeight;
    const maxViewportDimension = Math.max(viewportWidth, viewportHeight);
    const calculatedSize = ((devicePixelRatio + 1) / 2) * maxViewportDimension;

    // 选择大于计算值的最小标准尺寸
    const autofit =
      SIZE_SPLITERS.find((size) => size > calculatedSize) ||
      SIZE_SPLITERS[SIZE_SPLITERS.length - 1];

    return `${filePath}_${autofit}x.${ext}`;
  }

  async getStrategy(channel: Channels): Promise<IChannelStrategyConfig> {
    // TODO: 根据不同渠道获取不同的策略配置
    // 这里可以扩展为从 API 获取或从配置文件读取
    
    const MOCK_CONFIGS: Record<Channels, IChannelStrategyConfig> = {
      [Channels.FACEBOOK]: {
        channel: Channels.FACEBOOK,
        freeUntil: 5,
        img: "https://img.staticdj.com/40a8c3c08de8e00c485cb7459f765af5.jpg",
      },
      [Channels.DEFAULT]: {
        channel: Channels.DEFAULT,
        freeUntil: 0,
        img: "",
      },
    };

    // TODO: API 对接
    // const response = await fetch(`/apps/best-short/api/v1/channel_browser?channel=${channel}`);
    // const data = await response.json();
    
    const config = MOCK_CONFIGS[channel];
    
    return {
      ...config,
      img: config.img ? this.resizeImage(config.img) : config.img,
    };
  }
}

/**
 * 渠道播放能力检查器
 */
class ChannelPlayabilityChecker implements IChannelPlayabilityChecker {
  checkPlayable(config: IChannelStrategyConfig, episodeNo: number): boolean {
    // FIXME: 临时调试
    return false;
    
    const isEpisodeShouldCheck = episodeNo > config.freeUntil;

    if (!isEpisodeShouldCheck) {
      return true;
    }

    let episodeAllowed = true;

    switch (config.channel) {
      case Channels.FACEBOOK:
        episodeAllowed = !isFacebookBrowser();
        break;
      case Channels.DEFAULT:
        episodeAllowed = true;
        break;
    }

    return episodeAllowed;
  }
}

/**
 * 渠道管理器 - 统一管理渠道相关功能
 */
export class ChannelManager {
  private static instance: ChannelManager;
  
  private detector: IChannelDetector;
  private strategyProvider: IChannelStrategyProvider;
  private playabilityChecker: IChannelPlayabilityChecker;
  
  // 缓存相关
  private currentChannel: Channels | null = null;
  private strategyCache: Map<Channels, IChannelStrategyConfig> = new Map();
  private cacheExpiry: Map<Channels, number> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

  private constructor() {
    this.detector = new FacebookChannelDetector();
    this.strategyProvider = new ChannelStrategyProvider();
    this.playabilityChecker = new ChannelPlayabilityChecker();
  }

  public static getInstance(): ChannelManager {
    if (!ChannelManager.instance) {
      ChannelManager.instance = new ChannelManager();
    }
    return ChannelManager.instance;
  }

  /**
   * 获取当前渠道
   */
  public getCurrentChannel(): Channels {
    if (this.currentChannel === null) {
      this.currentChannel = this.detector.detect();
      moduleLoggers.AuthManager.info("Detected channel:", this.currentChannel);
    }
    return this.currentChannel;
  }

  /**
   * 检查是否需要获取渠道策略
   */
  public shouldUseChannelStrategy(): boolean {
    // FIXME: 临时调试
    return true;
    // return this.getCurrentChannel() !== Channels.DEFAULT;
  }

  /**
   * 获取渠道策略（带缓存）
   */
  public async getChannelStrategy(): Promise<IChannelStrategyConfig | null> {
    if (!this.shouldUseChannelStrategy()) {
      return null;
    }

    const channel = this.getCurrentChannel();
    const now = Date.now();
    
    // 检查缓存是否有效
    const cachedStrategy = this.strategyCache.get(channel);
    const cacheExpiry = this.cacheExpiry.get(channel);
    
    if (cachedStrategy && cacheExpiry && now < cacheExpiry) {
      moduleLoggers.AuthManager.info("Using cached channel strategy for:", channel);
      return cachedStrategy;
    }

    // 获取新的策略配置
    try {
      const strategy = await this.strategyProvider.getStrategy(channel);
      
      // 更新缓存
      this.strategyCache.set(channel, strategy);
      this.cacheExpiry.set(channel, now + this.CACHE_TTL);
      
      moduleLoggers.AuthManager.info("Fetched and cached channel strategy for:", channel, strategy);
      return strategy;
    } catch (error) {
      moduleLoggers.AuthManager.error("Failed to get channel strategy:", error);
      return null;
    }
  }

  /**
   * 检查剧集是否可播放
   */
  public checkEpisodePlayable(episodeNo: number, strategy?: IChannelStrategyConfig): boolean {
    if (!strategy) {
      return true; // 没有策略配置时默认可播放
    }
    
    return this.playabilityChecker.checkPlayable(strategy, episodeNo);
  }

  /**
   * 清除缓存（用于测试或强制刷新）
   */
  public clearCache(): void {
    this.strategyCache.clear();
    this.cacheExpiry.clear();
    this.currentChannel = null;
    moduleLoggers.AuthManager.info("Channel cache cleared");
  }

  /**
   * 获取缓存状态（用于调试）
   */
  public getCacheStatus(): { channel: Channels | null; cachedChannels: Channels[]; } {
    return {
      channel: this.currentChannel,
      cachedChannels: Array.from(this.strategyCache.keys()),
    };
  }
}
