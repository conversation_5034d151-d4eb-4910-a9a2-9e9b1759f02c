<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8" />
    <!-- <link rel="icon" href="%PUBLIC_URL%/favicon.ico" /> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="短剧播放器" />
    <title>短剧播放器</title>
    
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      html, body {
        width: 100%;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
      }
      
      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background-color: #000;
        overflow-x: hidden;
      }
      
      #root {
        width: 100%;
        height: 100vh;
        overflow: hidden;
      }
      
      /* 防止水平滚动条 */
      .app,
      .player-container,
      .multi-player-container,
      #custom-solution-product-container {
        max-width: 100vw;
        overflow-x: hidden;
      }
    </style>

    <script async crossorigin="anonymous" src="https://cn.static.shoplazza.com/cuttlefish/v1/spz.min.js"></script>
  </head>
  <body>
    <noscript>您需要启用JavaScript才能运行此应用程序。</noscript>
    <div id="root"></div>
    <script>
      window.__BS_PLAYER_ID__ = "root";
      window.__PRODUCT_ID__ = null;
      window.addEventListener("BSPlayerReady", () => {
        console.log("BSPlayerReady");
        window.mountBSPlayer(window.__PRODUCT_ID__, window.__BS_PLAYER_ID__);
      });
    </script>
    <script type="module" src="/src/index.tsx"></script>
  </body>
</html> 