# 定义构建参数
ARG TADPOLE_FRONTEND_TAG
ARG STG_REGISTRY
ARG DEV_REGISTRY

# 使用 Development 环境的 Node.js 基础镜像，作为构建阶段
FROM ${DEV_REGISTRY}/library/node:18-alpine as builder

# 定义 CI/CD 环境中的提交引用（用于版本标识）
ARG CI_COMMIT_REF_SLUG

# 设置工作目录
WORKDIR /app

# 将当前上下文中的所有文件复制到工作目录
COPY . /app

# 安装 pnpm 包管理器，并使用 pnpm 安装项目依赖
RUN npm i -g pnpm && pnpm i --registry https://npm.shoplazza.site/ && pnpm run build

# 使用 Staging 环境的基础镜像，准备运行阶段
FROM ${STG_REGISTRY}/shoplaza-publish/tadpole-frontend:${TADPOLE_FRONTEND_TAG}

# 设置工作目录
WORKDIR /app

# 从构建阶段复制构建生成的文件到运行阶段的工作目录
COPY --from=builder /app/dist /app/dist
COPY --from=builder /app/package.json /app
COPY --from=builder /app/server/index.mjs /app/server/index.mjs

# 暴露容器的 80 和 443 端口
EXPOSE 80 443

# 执行替换静态域名并上传脚本（上传构建的资产到 OSS），设置容器启动时执行的命令
CMD /app/replace_and_upload_oss_assets.sh && node server/index.mjs
