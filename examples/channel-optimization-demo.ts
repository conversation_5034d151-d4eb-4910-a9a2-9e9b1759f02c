/**
 * @file channel-optimization-demo.ts
 * @description 演示渠道管理优化前后的对比
 */

import { ChannelManager } from '../src/services/ChannelManager';
import { AuthManager } from '../src/services/AuthManager';

// 模拟性能测试
async function performanceDemo() {
  console.log('=== 渠道管理优化演示 ===\n');

  const channelManager = ChannelManager.getInstance();
  const authManager = AuthManager.getInstance();

  // 1. 渠道检测缓存演示
  console.log('1. 渠道检测缓存演示:');
  console.time('第一次渠道检测');
  const channel1 = channelManager.getCurrentChannel();
  console.timeEnd('第一次渠道检测');
  
  console.time('第二次渠道检测（缓存）');
  const channel2 = channelManager.getCurrentChannel();
  console.timeEnd('第二次渠道检测（缓存）');
  
  console.log(`渠道检测结果: ${channel1} === ${channel2}`, channel1 === channel2);
  console.log();

  // 2. 策略配置缓存演示
  console.log('2. 策略配置缓存演示:');
  console.time('第一次获取策略');
  const strategy1 = await channelManager.getChannelStrategy();
  console.timeEnd('第一次获取策略');
  
  console.time('第二次获取策略（缓存）');
  const strategy2 = await channelManager.getChannelStrategy();
  console.timeEnd('第二次获取策略（缓存）');
  
  console.log('策略配置相同:', strategy1 === strategy2);
  console.log('策略内容:', strategy1);
  console.log();

  // 3. 缓存状态查看
  console.log('3. 缓存状态:');
  const cacheStatus = channelManager.getCacheStatus();
  console.log('当前渠道:', cacheStatus.channel);
  console.log('已缓存的渠道:', cacheStatus.cachedChannels);
  console.log();

  // 4. 剧集播放检查演示
  console.log('4. 剧集播放检查演示:');
  const episodes = [1, 3, 5, 7, 10];
  
  episodes.forEach(episodeNo => {
    const isPlayable = channelManager.checkEpisodePlayable(episodeNo, strategy1);
    console.log(`第 ${episodeNo} 集可播放:`, isPlayable);
  });
  console.log();

  // 5. 完整视频详情获取演示
  console.log('5. 完整视频详情获取演示:');
  console.time('获取视频详情');
  try {
    // 注意：这里会实际调用 API，在演示环境中可能会失败
    // const videoData = await authManager.getVideoDetail('test-video-id');
    // console.log('视频数据获取成功，包含策略:', !!videoData.strategy);
    console.log('（跳过实际 API 调用）');
  } catch (error) {
    console.log('API 调用失败（预期行为）:', error.message);
  }
  console.timeEnd('获取视频详情');
  console.log();

  // 6. 缓存清除演示
  console.log('6. 缓存清除演示:');
  console.log('清除前缓存状态:', channelManager.getCacheStatus());
  channelManager.clearCache();
  console.log('清除后缓存状态:', channelManager.getCacheStatus());
  console.log();
}

// 对比优化前后的调用方式
function callPatternComparison() {
  console.log('=== 调用方式对比 ===\n');

  console.log('优化前的调用方式:');
  console.log(`
// 在 AuthManager 中
async getVideoDetail(videoId: string) {
  let strategy: undefined | IChannelStrategyConfig;
  
  // 第一步：检查是否需要获取策略
  if (this.shouldGetChannelStrategy()) {  // 调用 1
    // 第二步：获取策略
    strategy = await this.getChannelStrategy();  // 调用 2
  }
  
  // 在处理每个 episode 时
  episodes.forEach(episode => {
    // 第三步：检查播放能力（使用硬编码配置）
    const isBlocked = !this.checkChannelPlayable(
      MOCK_CHANNEL_CONFIG,  // 硬编码！
      episode.no,
    );  // 调用 3
  });
}
  `);

  console.log('优化后的调用方式:');
  console.log(`
// 在 AuthManager 中
async getVideoDetail(videoId: string) {
  // 一步到位：获取策略（带永久缓存）
  const strategy = await this.channelManager.getChannelStrategy();  // 调用 1
  
  // 在处理每个 episode 时
  episodes.forEach(episode => {
    // 直接检查播放能力（使用实际策略）
    const isPlayable = this.channelManager.checkEpisodePlayable(
      episode.no,
      strategy,  // 使用实际获取的策略
    );  // 调用 2
  });
}
  `);

  console.log('优化效果:');
  console.log('- 调用步骤：3 步 → 2 步');
  console.log('- 渠道检测：每次调用 → 永久缓存');
  console.log('- 策略获取：每次调用 → 永久缓存');
  console.log('- 配置来源：硬编码 → 动态获取');
  console.log('- 扩展性：单一渠道 → 多渠道支持');
  console.log();
}

// 扩展性演示
function extensibilityDemo() {
  console.log('=== 扩展性演示 ===\n');

  console.log('添加新渠道的步骤:');
  console.log(`
1. 在 Channels 枚举中添加新渠道:
   export enum Channels {
     FACEBOOK = "facebook",
     WECHAT = "wechat",     // 新增
     TIKTOK = "tiktok",     // 新增
     DEFAULT = "default",
   }

2. 扩展渠道检测器:
   class MultiChannelDetector implements IChannelDetector {
     detect(): Channels {
       if (isFacebookBrowser()) return Channels.FACEBOOK;
       if (isWeChatBrowser()) return Channels.WECHAT;     // 新增
       if (isTikTokBrowser()) return Channels.TIKTOK;     // 新增
       return Channels.DEFAULT;
     }
   }

3. 在策略提供者中添加配置:
   const CONFIGS: Record<Channels, IChannelStrategyConfig> = {
     [Channels.FACEBOOK]: { ... },
     [Channels.WECHAT]: { ... },   // 新增
     [Channels.TIKTOK]: { ... },   // 新增
     [Channels.DEFAULT]: { ... },
   };

4. 在播放检查器中添加规则:
   switch (config.channel) {
     case Channels.FACEBOOK: ...
     case Channels.WECHAT: ...     // 新增
     case Channels.TIKTOK: ...     // 新增
     case Channels.DEFAULT: ...
   }
  `);

  console.log('无需修改:');
  console.log('- AuthManager 的调用代码');
  console.log('- 缓存机制');
  console.log('- 接口定义');
  console.log('- 测试框架');
  console.log();
}

// 运行演示
async function runDemo() {
  try {
    await performanceDemo();
    callPatternComparison();
    extensibilityDemo();
    
    console.log('=== 演示完成 ===');
    console.log('优化总结:');
    console.log('✅ 简化了调用流程');
    console.log('✅ 实现了永久缓存');
    console.log('✅ 提高了可维护性');
    console.log('✅ 增强了扩展性');
    console.log('✅ 消除了冗余调用');
  } catch (error) {
    console.error('演示过程中出现错误:', error);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  runDemo();
}

export { runDemo, performanceDemo, callPatternComparison, extensibilityDemo };
